include:
  - project: dbmy/ci
    ref: master
    file:
      - pipelines/go/service.yml

variables:
  CMD_NAME: transaction-history
  ECR_URI: 712221657655.dkr.ecr.ap-southeast-1.amazonaws.com/transaction-history
  UNITTEST_MIN_COVERAGE: 65
#  todo: we should add more test for utils package
  REPO_SPECIFIC_IGNORE_PATHS: /mock_|storage/z_|.*routes\.go|utils/*|/storage\.go|consumers/*
  DOCKER_CONTEXTS: "localise,deploy/docker/transaction-history/init"
  DISABLE_VERIFY_MYSQL: "true"
  DISABLE_BUNDLE_MYSQL: "true"
  DISABLE_PACKAGE_DOCKER_MYSQL: "true"
  DISABLE_SAST: "true"
  SEMVER_SUFFIX: "-dbmy"
  DISABLE_DEPLOY_DEV: "true"
  GO_VERSION: 1.24.1a
  GOLINT_VERSION: 'v1.64.2-alpine-a'

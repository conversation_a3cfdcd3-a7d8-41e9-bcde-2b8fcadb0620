package constants

const (
	// DrawdownTransactionType ...
	DrawdownTransactionType = "DRAWDOWN"
	// RepaymentTransactionType ...
	RepaymentTransactionType = "REPAYMENT"
	// InterestApplicationTransactionType ...
	InterestApplicationTransactionType = "INTEREST_APPLICATION"
	// InterestAccrualForPenalty ...
	InterestAccrualForPenalty = "INTEREST_ACCRUAL"
	// PaymentTransactionType ...
	PaymentTransactionType = "PAYMENT"
	// LineOfCreditTransactionType ...
	LineOfCreditTransactionType = "LINE_OF_CREDIT"
	// TermLoanTransactionType ...
	TermLoanTransactionType = "TERM_LOAN"
	// BillingTransactionType ...
	BillingTransactionType = "BILLING"
	// WriteOffTransactionType ...
	WriteOffTransactionType = "WRITE_OFF"
	// UtilizeLimitTransactionSubType ...
	UtilizeLimitTransactionSubType = "UTILISE_LIMIT"
	// BadDebtRecoveryIntrabankSubType ...
	BadDebtRecoveryIntrabankSubType = "BAD_DEBT_RECOVERY_INTRABANK"
	// OpsLossRecoveryIntrabankTransactionSubType ...
	OpsLossRecoveryIntrabankTransactionSubType = "OPS_LOSS_RECOVERY_INTRABANK"
	// LendingDomain ...
	LendingDomain = "LENDING"
	// BizLendingDomain ...
	BizLendingDomain = "BIZ_LENDING"
	// LendingAccountType ...
	LendingAccountType = "UNSECURED_LINE_OF_CREDIT"
	//LoanConversionSubType ...
	LoanConversionSubType = "LOAN_CONVERSION"
	// AkpkFundReallocationSubtype ...
	AkpkFundReallocationSubtype = "AKPK_FUND_REALLOCATION"
)

var (
	// LendingTransactionTypes is the list of lending transaction types that need to be displayed
	LendingTransactionTypes = []string{DrawdownTransactionType, RepaymentTransactionType, LineOfCreditTransactionType, WriteOffTransactionType}
	// LendingTransactionSubtypes is the list of lending transaction subtypes that need to be displayed
	LendingTransactionSubtypes = []string{IntraBank, FastNetwork, UtilizeLimitTransactionSubType, BadDebtRecoveryIntrabankSubType, OpsLossRecoveryIntrabankTransactionSubType, LoanConversionSubType, AkpkFundReallocationSubtype}
)

const (
	// OverPaidTitle ...
	OverPaidTitle = "You paid extra with excess credit"
	// OverPaidDescription ...
	OverPaidDescription = "You've paid all remaining balance with excess of RM%v. It will be stored safely in your GXS FlexiLoan account. You will be able to transfer it out during public launch very soon."
)

// RepaymentMadeAccountAddress ...
const RepaymentMadeAccountAddress = "REPAYMENT_MADE"

// DisbursementContraAccountAddress ...
const DisbursementContraAccountAddress = "DISBURSEMENT_CONTRA"

// Constants used by casa account history code
const (
	// DrawDownDisplayName ...
	DrawDownDisplayName = "GXS FlexiLoan drawdown"
	// RepaymentDisplayName ...
	RepaymentDisplayName = "GXS FlexiLoan payment"
)

// WrittenOffStatus denotes that the term loan is written off
const WrittenOffStatus = "WRITTEN_OFF"

// Variables used by casa account history code
var (
	// LendingTransactionTypesToExcludeInCasaAccountHistory ...
	LendingTransactionTypesToExcludeInCasaAccountHistory = []string{InterestApplicationTransactionType, PaymentTransactionType,
		InterestAccrualForPenalty, LineOfCreditTransactionType, TermLoanTransactionType, BillingTransactionType, WriteOffTransactionType}
)

const (
	// FlexiLOCProductVariantCode ...
	FlexiLOCProductVariantCode = "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"
	// FlexiBizLOCProductVariantCode ...
	FlexiBizLOCProductVariantCode = "DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT"
)

const (
	// Transfer ...
	Transfer = "TRANSFER"

	// Settlement ...
	Settlement = "SETTLEMENT"

	// Custom ...
	Custom = "CUSTOM"

	// CustomInstruction ...
	CustomInstruction = "CUSTOM_INSTRUCTION"
)

const (
	// LendingDrawdownScenarioKey ...
	LendingDrawdownScenarioKey = LendingDomain + "." + DrawdownTransactionType
	// LendingRepaymentScenarioKey ...
	LendingRepaymentScenarioKey = LendingDomain + "." + RepaymentTransactionType
	// LendingWriteOffScenarioKey ...
	LendingWriteOffScenarioKey = LendingDomain + "." + WriteOffTransactionType
	// CASALendingDrawdownScenarioKey ...
	CASALendingDrawdownScenarioKey = "CASA" + "." + LendingDomain + "." + DrawdownTransactionType
	// CASALendingRepaymentScenarioKey ...
	CASALendingRepaymentScenarioKey = "CASA" + "." + LendingDomain + "." + RepaymentTransactionType
	// CASALendingWriteOffScenarioKey ...
	CASALendingWriteOffScenarioKey = "CASA" + "." + LendingDomain + "." + WriteOffTransactionType
)

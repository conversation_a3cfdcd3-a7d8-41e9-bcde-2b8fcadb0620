// Package constants contains all constants related to deposits
package constants

import (
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
)

const (
	// DayFormat ...
	DayFormat = "2006-01-02"

	// MonthFormat ...
	MonthFormat = "2006-01"
)

// Constants denoting direction of money
const (
	// CREDIT ...
	CREDIT = "credit"
	// DEBIT ...
	DEBIT = "debit"
	// DepositAccountIDPrefix ...
	DepositAccountIDPrefix = "8888"
)

const (
	// DepositsDomain is a transaction domain for all the deposits transactions
	DepositsDomain = "DEPOSITS"
)

// Constants denoting TransactionType as per Thought Machine
const (
	// PostingPhaseCommitted ...
	PostingPhaseCommitted = "POSTING_PHASE_COMMITTED"
	// PostingPhasePendingOutgoing ...
	PostingPhasePendingOutgoing = "POSTING_PHASE_PENDING_OUTGOING"

	// OutboundAuthorisation ...
	OutboundAuthorisation = "OUTBOUND_AUTHORISATION"
	// Release ...
	Release = "RELEASE"
)

// Constants denoting status as per Thought Machine
const (
	// BatchStatusAccepted ...
	BatchStatusAccepted = "ACCEPTED"
	// BatchStatusRejected ...
	BatchStatusRejected = "REJECTED"
)

// Constants denoting status to show on UI
const (
	// CompletedStatus ...
	CompletedStatus = "COMPLETED"
	// FailedStatus ...
	FailedStatus = "FAILED"
	// ProcessingStatus ...
	ProcessingStatus = "PROCESSING"
	// AuthorizedStatus ...
	AuthorizedStatus = "AUTHORISED"
	// RefundedStatus ...
	RefundedStatus = "REFUNDED"
	// CanceledStatus ...
	// Return as CANCELED instead of CANCELLED since frontend handle CANCELLED as CANCELED
	CanceledStatus = "CANCELED"
)

const (
	// AccountPermissionAllowed ...
	AccountPermissionAllowed = "ALLOWED"
)

// Constants denoting account address of CASA account
const (
	// AccruedDepositInterest ...
	AccruedDepositInterest = "ACCRUED_DEPOSIT_INTEREST"

	// DefaultAccountAddress ...
	DefaultAccountAddress = "DEFAULT"
)

// Constants denoting types of CASA pockets
const (
	// SavingsPocketType ...
	SavingsPocketType = "SAVINGS"
)

// Interest Payout and Reversal Transaction Type constants
const (
	// InterestPayoutTransactionType ...
	InterestPayoutTransactionType = "INTEREST_PAYOUT"

	// InterestPayoutReversalTransactionType ...
	InterestPayoutReversalTransactionType = "INTEREST_PAYOUT_REVERSAL"

	// InterestPayoutTransactionSubType ...
	InterestPayoutTransactionSubType = "SAVINGS"
)

// pockets transfer transaction-subtype
const (
	// PocketFundingTransactionSubType ...
	PocketFundingTransactionSubType = "FROM_MAIN_TO_POCKET"

	// PocketWithdrawalTransactionSubType ...
	PocketWithdrawalTransactionSubType = "FROM_POCKET_TO_MAIN"

	/* PocketWithdrawalIconPocketView ...
	 * is a constant to use for FROM_POCKET_TO_MAIN in debit direction txn
	 * which the txn will only be shown in Pocket account (pocket view).
	 * This constant will only be used in IconURLMap to represent the respective txn type. */
	PocketWithdrawalIconPocketView = "DEBIT_FROM_POCKET_TO_MAIN"

	/* PocketWithdrawalIconMainAccountView ...
	 * is a constant to use for FROM_POCKET_TO_MAIN in credit direction txn
	 * which the txn will only be shown in Main account (main account view).
	 * This constant will only be used in IconURLMap to represent the respective txn type */
	PocketWithdrawalIconMainAccountView = "CREDIT_FROM_POCKET_TO_MAIN"
)

// intrabank transfer transaction-subtype
const (
	// IntrabankTransactionSubtype ...
	IntrabankTransactionSubtype = "INTRABANK"
)

// unknown transaction subtype
const (
	//	UnknownTransactionSubtype ...
	UnknownTransactionSubtype = "DEFAULT"
)

// sourceOfFund
const (
	// MainAccountSource ...
	MainAccountSource = "Main Account"
)

// Earmark Transactions
const (
	// ApplyEarmarkTransactionType ...
	ApplyEarmarkTransactionType = "APPLY_EARMARK"
	// ReleaseEarmarkTransactionType ...
	ReleaseEarmarkTransactionType = "RELEASE_EARMARK"
)

// QR Transactions
const (
	// Merchant ...
	Merchant = "Merchant"
)

// types of transactions
const (
	Withdrawal     = "WITHDRAWAL"
	TransferIn     = "TRANSFER_IN"
	TransferOut    = "TRANSFER_OUT"
	FundIn         = "FUND_IN"
	TransferFee    = "TRANSFER_FEE"
	InterestPayout = "INTEREST_PAYOUT"
	TaxOnInterest  = "TAX_ON_INTEREST"
	Adjustment     = "ADJUSTMENT"
	Rewards        = "REWARDS"
	PocketTransfer = "POCKET_TRANSFER"
)

var (
	// InterestAccrualTransactionTypes ...
	InterestAccrualTransactionTypes = []string{"INTEREST_ACCRUAL", "INTEREST_ACCRUAL_REVERSAL"}
	// InterestPayoutTransactionTypes ...
	InterestPayoutTransactionTypes = []string{InterestPayoutTransactionType, InterestPayoutReversalTransactionType}
	// SentReceiveTransactionTypes ...
	SentReceiveTransactionTypes = []string{"SEND_MONEY", "RECEIVE_MONEY", "TRANSFER_MONEY", "FUND_IN", "SEND_MONEY_REVERSAL", "RECEIVE_MONEY_REVERSAL", "TRANSFER_MONEY_REVERSAL", "FUND_IN_REVERSAL", "SPEND_MONEY", "SPEND_MONEY_REVERSAL", "CASH_OUT", "SETTLEMENT", "SETTLEMENT_REVERSAL", "ADJUSTMENT", RewardsCashback, RewardsCashbackReversal, SpendRefundTransactionType, MoneySendTransactionType, ATMCashWithdrawalRefundTransactionType, ManualCreditReconTransactionType, DisputeChargeBackTransactionType, DisputeBankRefundTransactionType, DisputeBankFraudRefundTransactionType, DomesticAtmFeeWaiverTransactionType, SpendCardPresentTransactionType, SpendCardNotPresentTransactionType, SpendCardAtmTransactionType, ManualDebitReconTransactionType, DomesticAtmFeeTransactionType, ProvisionalCreditTransactionType, ProvisionalCreditReversalTransactionType, OperationalLossTransactionType, QrPaymentTxType, QrPaymentReversalTxType, DrawdownTransactionType, RepaymentTransactionType, WriteOffTransactionType, InsurPremiumTxType, SpendCardATMReversalTransactionType, SpendCardPresentReversalTransactionType, SpendCardNotPresentReversalTransactionType, UnclaimedMoniesTransactionType,
		NewCardIssuanceFeeTransactionType, NewCardIssuanceFeeWaiverTransactionType, CardAnnualFeeTransactionType, CardAnnualFeeWaiverTransactionType, CardReplacementFeeTransactionType, CardReplacementFeeWaiverTransactionType}

	// ReverseTransactionType ...
	// TODO: add dbmy card transaction type https://jira.grab.com/browse/DBMY-5640
	ReverseTransactionType = []string{"SEND_MONEY_REVERSAL", "RECEIVE_MONEY_REVERSAL", "TRANSFER_MONEY_REVERSAL", "FUND_IN_REVERSAL", "SPEND_MONEY_REVERSAL", "SETTLEMENT_REVERSAL", InterestPayoutReversalTransactionType, RewardsCashbackReversal, SpendRefundTransactionType, ATMCashWithdrawalRefundTransactionType, QrPaymentReversalTxType, InsurPremiumReversalTxType}
	// PocketTransfersTransactionSubTypes ...
	PocketTransfersTransactionSubTypes = []string{PocketFundingTransactionSubType, PocketWithdrawalTransactionSubType}
	// PocketTransactionTypes ...
	PocketTransactionTypes = []string{"TRANSFER_MONEY", "TRANSFER_MONEY_REVERSAL"}
	// SpendMoneyTransactionTypes ...
	SpendMoneyTransactionTypes = []string{"SPEND_MONEY", "SPEND_MONEY_REVERSAL"}
	// MooMooTransactionTypes ...
	MooMooTransactionTypes = []string{"SPEND_MONEY", "SPEND_MONEY_REVERSAL", "CASH_OUT"}
	// MooMooTransactionSubTypes ...
	MooMooTransactionSubTypes = []string{"MOOMOO"}
	// Currency ...
	Currency string
	// IconURLMap ...
	IconURLMap = make(map[string]string)
	// TaxAccrualTransactionTypes ...
	TaxAccrualTransactionTypes = []string{"TAX_ACCRUAL", "TAX_ACCRUAL_REVERSAL"}
	// TaxPayoutTransactionTypes ...
	TaxPayoutTransactionTypes = []string{"TAX_PAYOUT", "TAX_PAYOUT_REVERSAL"}
	// SendMoneyFeeTransactionTypes ...
	SendMoneyFeeTransactionTypes = []string{"SEND_MONEY_FEE", "SEND_MONEY_FEE_REVERSAL"}
	// SendMoneyFeeTransactionType ...
	SendMoneyFeeTransactionType = "SEND_MONEY_FEE"
	// OpsInitiateTransactionType ...
	OpsInitiateTransactionType = []string{"ADJUSTMENT"}
	// EarmarkTransactionTypes ...
	EarmarkTransactionTypes = []string{ApplyEarmarkTransactionType, ReleaseEarmarkTransactionType}
)

// InitializeDynamicConstants ...
func InitializeDynamicConstants(appCfg *config.AppConfig) {
	Currency = appCfg.TransactionHistoryServiceConfig.DefaultCurrency
	IconURLMap[InterestEarned] = appCfg.IconConfig.InterestEarn
	IconURLMap[BankAdjustment] = appCfg.IconConfig.BankAdjustment
	IconURLMap[PocketFundingTransactionSubType] = appCfg.IconConfig.PocketFunding
	IconURLMap[PocketWithdrawalIconPocketView] = appCfg.IconConfig.PocketPocketWithdrawal
	IconURLMap[PocketWithdrawalIconMainAccountView] = appCfg.IconConfig.MainPocketWithdrawal
	IconURLMap[PocketWithdrawalTransactionSubType] = appCfg.IconConfig.SavingsPocketWithdrawal
	IconURLMap["DefaultTransaction"] = appCfg.IconConfig.DefaultTransaction
	IconURLMap[Merchant] = appCfg.IconConfig.Merchant
	IconURLMap[Withdrawal] = appCfg.IconConfig.Withdrawal
	IconURLMap[TransferIn] = appCfg.IconConfig.TransferIn
	IconURLMap[TransferOut] = appCfg.IconConfig.TransferOut
	IconURLMap[TransferFee] = appCfg.IconConfig.TransferFee
	IconURLMap[InterestPayout] = appCfg.IconConfig.InterestPayout
	IconURLMap[TaxOnInterest] = appCfg.IconConfig.TaxOnInterest
	IconURLMap[Adjustment] = appCfg.IconConfig.Adjustment
	IconURLMap[DebitCardDomain] = appCfg.IconConfig.Mastercard
	IconURLMap[Grab] = appCfg.IconConfig.Grab
	IconURLMap[Rewards] = appCfg.IconConfig.Rewards
	IconURLMap[DrawdownTransactionType] = appCfg.IconConfig.LendingDrawdown
	IconURLMap[RepaymentTransactionType] = appCfg.IconConfig.LendingRepayment
	IconURLMap[InsuranceDomain] = appCfg.IconConfig.Insurance
}

// accountType
const (
	// MainAccount ...
	MainAccount = "MAIN_ACCOUNT"

	// SavingsPocket ...
	SavingsPocket = "SAVINGS_POCKET"
)

const (
	// OverdraftRepaymentTransactionType ...
	OverdraftRepaymentTransactionType = "REPAYMENT_OVERDRAFT"

	// OverdraftRepaymentDisplayName ...
	OverdraftRepaymentDisplayName = "Repayment of Overdraft"
)

// productVariantID for GXB product
const (
	// DepositsAccount ...
	DepositsAccount = "DEPOSITS_ACCOUNT"
)

// Transaction scenario keys
const (
	// TransferIntrabankDebitScenarioKey ...
	TransferIntrabankDebitScenarioKey = "transfer_money.intrabank.debit"
	// TransferIntrabankCreditScenarioKey ...
	TransferIntrabankCreditScenarioKey = "transfer_money.intrabank.credit"
	// TransferMainToPocketDebitScenarioKey ...
	TransferMainToPocketDebitScenarioKey = "transfer_money.from_main_to_pocket.debit"
	// TransferMainToPocketCreditScenarioKey ...
	TransferMainToPocketCreditScenarioKey = "transfer_money.from_main_to_pocket.credit"
	// TransferPocketToMainDebitScenarioKey ...
	TransferPocketToMainDebitScenarioKey = "transfer_money.from_pocket_to_main.debit"
	// TransferPocketToMainCreditScenarioKey ...
	TransferPocketToMainCreditScenarioKey = "transfer_money.from_pocket_to_main.credit"
	// TransferMoneyReversalIntrabankDebitScenarioKey ...
	TransferMoneyReversalIntrabankDebitScenarioKey = "transfer_money_reversal.intrabank.debit"
	// TransferMoneyReversalIntrabankCreditScenarioKey ...
	TransferMoneyReversalIntrabankCreditScenarioKey = "transfer_money_reversal.intrabank.credit"
	// FundInScenarioKey ...
	FundInScenarioKey = "fund_in"
	// FundInReversalScenarioKey ...
	FundInReversalScenarioKey = "fund_in_reversal"
	// SendMoneyDebitScenarioKey ...
	SendMoneyDebitScenarioKey = "send_money.debit"
	// SendMoneyReversalCreditScenarioKey ...
	SendMoneyReversalCreditScenarioKey = "send_money_reversal.credit"
	// ReceiveMoneyCreditScenarioKey ...
	ReceiveMoneyCreditScenarioKey = "receive_money.credit"
	// ReceiveMoneyReversalDebitScenarioKey ...
	ReceiveMoneyReversalDebitScenarioKey = "receive_money_reversal.debit"
	// SpendMoneyScenarioKey ...
	SpendMoneyScenarioKey = "spend_money"
	// SpendMoneyReversalScenarioKey ...
	SpendMoneyReversalScenarioKey = "spend_money_reversal"
	// MooMooCashOutScenarioKey ...
	MooMooCashOutScenarioKey = "moomoo.cash_out"
	// MooMooSpendMoneyScenarioKey ...
	MooMooSpendMoneyScenarioKey = "moomoo.spend_money"
	// MooMooSpendMoneyReversalScenarioKey ...
	MooMooSpendMoneyReversalScenarioKey = "moomoo.spend_money_reversal"
	// InterestPayoutScenarioKey ...
	InterestPayoutScenarioKey = "interest_payout"
	// AdjustmentBankInitiatedDebitScenarioKey ...
	AdjustmentBankInitiatedDebitScenarioKey = "adjustment.bank_initiated.debit"
	// AdjustmentBankInitiatedCreditScenarioKey ...
	AdjustmentBankInitiatedCreditScenarioKey = "adjustment.bank_initiated.credit"
	// RewardsCashbackScenarioKey ...
	RewardsCashbackScenarioKey = "rewards_cashback"
	// RewardsCashbackReversalScenarioKey ...
	RewardsCashbackReversalScenarioKey = "rewards_cashback_reversal"
	// ApplyEarmarkScenarioKey ...
	ApplyEarmarkScenarioKey = "apply_earmark"
	// ReleaseEarmarkScenarioKey ...
	ReleaseEarmarkScenarioKey = "release_earmark"
	// UnclaimedMoniesSuspenseScenarioKey is the scenario key for customer balance being credited to suspense GL
	UnclaimedMoniesSuspenseScenarioKey = "unclaimed_monies.suspense"
)

package server

import (
	"context"
	"os"
	"time"

	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/consumers"
	"gitlab.myteksi.net/dakota/transaction-history/handlers"
	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/logic/handlerlogic"
	"gitlab.myteksi.net/dakota/transaction-history/logic/workerlogic"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils/client"
	"gitlab.myteksi.net/dakota/transaction-history/worker"
	"gitlab.myteksi.net/dbmy/core-banking/external-lib/accessmanagement/activeprofile"
)

var workerMode = os.Getenv("WORKER_MODE") == constants.True

// Serve ...
// nolint
func Serve() {
	appCfg := &config.AppConfig{}
	app := servus.Default(
		servus.WithAppConfig(appCfg), servus.WithTimeout(time.Duration(appCfg.TransactionHistoryServiceConfig.APIRequestTimeoutInSec)*time.Second))

	config.SetTenant(appCfg.Tenant)
	config.SetStatementReadyByDays(appCfg.TransactionStatementsConfig.StatementReadyByDays)
	config.SetMaxCursorLimit(appCfg.ExhaustivePaginationConfig.MaxCursorLimit)
	config.SetExecutionSleepDurationInMs(appCfg.ExhaustivePaginationConfig.ExecutionSleepDurationInMs)

	client.RegisterClients(app)
	storage.Init(appCfg, app, app.GetStatsD())
	constants.InitializeDynamicConstants(appCfg)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
	registerDefaults(appCfg)
	// init feature flags
	featureFlags := appCfg.NewFeatureFlagRepoImpl()
	app.MustRegister("featureFlags", featureFlags)

	redisClient := redis.MakeClusterClientV2(appCfg.CachingConfig.RedisConfig, redis.StatsD(app.GetStatsD()), redis.Logger(app.GetLogger()), redis.WithStatsForCB(&redis.StatsDConfig{Host: appCfg.StatsD.Host, Port: appCfg.StatsD.Port}))
	app.MustRegister("client.redis", redisClient)

	service := &handlers.TxHistoryService{}
	app.MustRegister("service", service)
	service.RegisterRoutes(app)

	app.MustRegister("logic.dependencies", handlerlogic.Dependencies)

	app.MustRegister("activeProfile", &activeprofile.ClientDBMY{})

	stats := app.GetStatsD()
	workerlogic.Init(stats)

	localise.Init(appCfg)

	if workerMode {
		worker.StartWorker(app, appCfg)
		app.GetTracer().Stop()
		app.GetLogger().Flush()
	} else {
		consumers.Init(app, appCfg, stats, service.PaymentExperienceClient, redisClient)
		app.OnShutdown(func(ctx context.Context) {
			consumers.Stop(appCfg)
		})
		app.Run()
	}
}

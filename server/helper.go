package server

import (
	"reflect"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
)

func registerDefaults(appCfg *config.AppConfig) {
	emptyLocaleConfig := config.Locale{}
	if reflect.DeepEqual(emptyLocaleConfig, appCfg.Locale) {
		appCfg.Locale.Currency = constants.DefaultCurrency
	}

	if reflect.DeepEqual(emptyLocaleConfig, appCfg.Locale) {
		appCfg.Locale.Language = constants.DefaultLanguage
	}
}

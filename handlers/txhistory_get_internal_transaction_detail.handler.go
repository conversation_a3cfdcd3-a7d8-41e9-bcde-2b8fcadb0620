package handlers

import (
	context "context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/handlerlogic"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	api "gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetInternalTransactionDetail API to fetch transaction detail for internal usage
func (t *TxHistoryService) GetInternalTransactionDetail(ctx context.Context, req *api.GetInternalTransactionDetailRequest) (*api.GetInternalTransactionDetailResponse, error) {
	slog.FromContext(ctx).Info(constants.GetInternalTransactionDetailLogTag, fmt.Sprintf("Received Request: %+v", req))

	//Check whether the request is valid
	if validationErrors := handlerlogic.GetInternalTransactionDetailRequestValidator(req); len(validationErrors) != 0 {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = validationErrors
		return nil, err
	}

	getTransactionDetailObj := handlerlogic.GetTransactionDetailStruct{AccountServiceClient: t.AccountServiceClient}
	response, err := getTransactionDetailObj.GetInternalTransactionDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	return response, nil
}

package handlers //nolint: dupl

import (
	"context"
	"fmt"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/servicename"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/logic/handlerlogic"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetCASAInterestEarned via POST
func (t *TxHistoryService) GetCASAInterestEarned(ctx context.Context, req *api.GetCASAInterestEarnedRequest) (*api.GetCASAInterestEarnedResponse, error) {
	slog.FromContext(ctx).Info(constants.GetCASAInterestEarnedHandlerLogTag, fmt.Sprintf("Received Request: %+v", req))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, t.FeatureFlags)

	//Check whether the request is valid
	if validationErrors := handlerlogic.GetCASAInterestEarnedRequestValidator(req); len(validationErrors) != 0 {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = validationErrors
		return nil, err
	}

	// Only check auth for T6
	clientIdentity := commonCtx.GetClientIdentity(ctx)
	if clientIdentity == servicename.SentryT6.ToString() {
		// Check Authorization via CIF Number
		if _, authErr := CheckIfAuthorized(ctx, req.AccountID, t); authErr != nil {
			return nil, authErr
		}
	}

	response, err := handlerlogic.GetCASAInterestEarned(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetCASAInterestEarnedHandlerLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return response, nil
}

// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package handlers

import (
	context "context"
	"runtime/debug"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/mockservices"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"

	"gitlab.myteksi.net/dakota/common/servicename"
	servus "gitlab.myteksi.net/dakota/servus/v2"
	api "gitlab.myteksi.net/dbmy/transaction-history/api"
)

func withClientIdentities(middlewareFunc servus.MiddlewareFunc) servus.MiddlewareFunc {
	if mockservices.UseMockAuth {
		return mockservices.WithAuth()
	} else {
		return middlewareFunc
	}
}

type handlerFunc func(ctx context.Context, req interface{}) (interface{}, error)

func withPanicRecovery(h handlerFunc) handlerFunc {
	return func(ctx context.Context, req interface{}) (res interface{}, err error) {
		defer func() {
			if anyPanic := recover(); anyPanic != nil {
				slog.FromContext(ctx).Error("servus", "[PANIC RECOVERED]", slog.CustomTag("err", anyPanic),
					slog.CustomTag("stack_trace", string(debug.Stack())))
				err = customErr.BuildErrorResponse(customErr.InternalServerError, "An unexpected error occurred. Please try again later.")
			}
		}()
		res, err = h(ctx, req)
		return res, err
	}
}

// RegisterRoutes registers handlers with the Servus library.
func (t *TxHistoryService) RegisterRoutes(app *servus.Application) {
	app.POST(
		"/v1/transactions-search",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetAccountTransactionsSearch(ctx, req.(*api.GetTransactionsHistoryRequest))
			return res, err
		},
		servus.WithRequest(&api.GetTransactionsHistoryRequest{}),
		servus.WithResponse(&api.GetTransactionsHistoryResponse{}),
		servus.WithDescription("GetAccountTransactionsSearch: API to fetch all transactions for customer (currently CASA account only)."),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6, servicename.CustomerPortal)),
		t.ExternalActiveProfileClient.WithActiveProfile(t.AppConfig.FeatureFlags.EnableAuthzWithProfileID.Enabled),
	)
	app.POST(
		"/v2/account-calendar-activity",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetAccountCalendarActivity(ctx, req.(*api.GetAccountCalendarActivityRequest))
			return res, err
		},
		servus.WithRequest(&api.GetAccountCalendarActivityRequest{}),
		servus.WithResponse(&api.AccountCalendarActivityResponse{}),
		servus.WithDescription("GetAccountCalendarActivity: API to fetch year and month for all accounts."),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6)),
		t.ExternalActiveProfileClient.WithActiveProfile(t.AppConfig.FeatureFlags.EnableAuthzWithProfileID.Enabled),
	)
	app.POST(
		"/v2/transactions/get",
		withPanicRecovery(func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetTransactionDetail(ctx, req.(*api.GetTransactionDetailRequest))
			return res, err
		}),
		servus.WithRequest(&api.GetTransactionDetailRequest{}),
		servus.WithResponse(&api.GetTransactionDetailResponse{}),
		servus.WithDescription("GetTransactionDetail: API to fetch transaction detail (currently only for CASA with pockets)"),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6, servicename.CustomerPortal, servicename.SentryPartnerT6)),
		t.ExternalActiveProfileClient.WithActiveProfile(t.AppConfig.FeatureFlags.EnableAuthzWithProfileID.Enabled),
	)
	app.POST(
		"/v1/casa-transactions-summary",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetCASATransactionsSummary(ctx, req.(*api.GetCASATransactionsSummaryRequest))
			return res, err
		},
		servus.WithRequest(&api.GetCASATransactionsSummaryRequest{}),
		servus.WithResponse(&api.GetCASATransactionsSummaryResponse{}),
		servus.WithDescription("GetCASATransactionsSummary: API to share detail of monthly spent and interest earned for CASA account only."),
		withClientIdentities(servus.WithClientIdentities(servicename.CustomerPortal, servicename.LokiService)),
		t.ExternalActiveProfileClient.WithActiveProfile(t.AppConfig.FeatureFlags.EnableAuthzWithProfileID.Enabled),
	)
	app.POST(
		"/v1/pocket-total-interest-earned",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetPocketInterestEarned(ctx, req.(*api.GetPocketInterestEarnedRequest))
			return res, err
		},
		servus.WithRequest(&api.GetPocketInterestEarnedRequest{}),
		servus.WithResponse(&api.GetPocketInterestEarnedResponse{}),
		servus.WithDescription("GetPocketInterestEarned: API to fetch total interest earned on the pocket"),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6, servicename.CustomerPortal)),
		t.ExternalActiveProfileClient.WithActiveProfile(t.AppConfig.FeatureFlags.EnableAuthzWithProfileID.Enabled),
	)
	app.POST(
		"/v1/pocket-activities",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetPocketActivities(ctx, req.(*api.GetPocketActivitiesRequest))
			return res, err
		},
		servus.WithRequest(&api.GetPocketActivitiesRequest{}),
		servus.WithResponse(&api.GetPocketActivitiesResponse{}),
		servus.WithDescription("GetPocketActivities: fetch all transactions related to pockets"),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6, servicename.CustomerPortal)),
		t.ExternalActiveProfileClient.WithActiveProfile(t.AppConfig.FeatureFlags.EnableAuthzWithProfileID.Enabled),
	)
	app.POST(
		"/v1/pocket-activity-detail",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetPocketActivityDetail(ctx, req.(*api.GetPocketActivityDetailRequest))
			return res, err
		},
		servus.WithRequest(&api.GetPocketActivityDetailRequest{}),
		servus.WithResponse(&api.GetPocketActivityDetailResponse{}),
		servus.WithDescription("GetPocketActivityDetail: API to fetch transaction detail for pocket transactions"),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6)),
		t.ExternalActiveProfileClient.WithActiveProfile(t.AppConfig.FeatureFlags.EnableAuthzWithProfileID.Enabled),
	)
	app.POST(
		"/v1/casa-total-interest-earned",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetCASAInterestEarned(ctx, req.(*api.GetCASAInterestEarnedRequest))
			return res, err
		},
		servus.WithRequest(&api.GetCASAInterestEarnedRequest{}),
		servus.WithResponse(&api.GetCASAInterestEarnedResponse{}),
		servus.WithDescription("GetCASAInterestEarned: API to fetch total interest earned on the CASA account"),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6, servicename.CustomerPortal, servicename.LokiService)),
		t.ExternalActiveProfileClient.WithActiveProfile(t.AppConfig.FeatureFlags.EnableAuthzWithProfileID.Enabled),
	)
	app.POST(
		"/v1/stm453-transaction-info",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetSTM453TransactionInfo(ctx, req.(*api.GetSTM453TransactionInfoRequest))
			return res, err
		},
		servus.WithRequest(&api.GetSTM453TransactionInfoRequest{}),
		servus.WithResponse(&api.GetSTM453TransactionInfoResponse{}),
		servus.WithDescription("GetSTM453TransactionInfo: API to fetch STM453 Transaction Info"),
		withClientIdentities(servus.WithClientIdentities(servicename.CustomerPortal)),
	)
	app.POST(
		"/v2/transactions/list",
		withPanicRecovery(func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.ListAccountTransactionsSearch(ctx, req.(*api.GetTransactionsHistoryRequest))
			return res, err
		}),
		servus.WithRequest(&api.GetTransactionsHistoryRequest{}),
		servus.WithResponse(&api.GetTransactionsHistoryResponse{}),
		servus.WithDescription("ListAccountTransactionsSearch: API to search all transaction history"),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6, servicename.CustomerPortal, servicename.SentryPartnerT6)),
		t.ExternalActiveProfileClient.WithActiveProfile(t.AppConfig.FeatureFlags.EnableAuthzWithProfileID.Enabled),
	)

	app.POST(
		"/v1/internal/transactions/get",
		withPanicRecovery(func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetInternalTransactionDetail(ctx, req.(*api.GetInternalTransactionDetailRequest))
			return res, err
		}),
		servus.WithRequest(&api.GetInternalTransactionDetailRequest{}),
		servus.WithResponse(&api.GetInternalTransactionDetailResponse{}),
		servus.WithDescription("GetInternalTransactionDetail: API to fetch transaction detail for internal usage"),
		withClientIdentities(servus.WithClientIdentities(servicename.CustomerPortal, servicename.TransactionStatements)),
	)

	app.POST(
		"/v1/internal/transactions/list",
		withPanicRecovery(func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetOpsSearch(ctx, req.(*api.GetOpsSearchRequest))
			return res, err
		}),
		servus.WithRequest(&api.GetOpsSearchRequest{}),
		servus.WithResponse(&api.GetOpsSearchResponse{}),
		servus.WithDescription("GetOpsSearch: API to fetch transaction detail for customer portal usage"),
		withClientIdentities(servus.WithClientIdentities(servicename.CustomerPortal)),
	)
	app.POST(
		"/v1/transactions-search-cx",
		withPanicRecovery(func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetAccountTransactionsSearchForCX(ctx, req.(*api.GetTransactionsHistoryCXRequest))
			return res, err
		}),
		servus.WithRequest(&api.GetTransactionsHistoryCXRequest{}),
		servus.WithResponse(&api.GetTransactionsHistoryCXResponse{}),
		servus.WithDescription("GetAccountTransactionsSearchForCX: API to fetch all transactions for customer (currently CASA account only) with transaction domain filter for CX."),
		withClientIdentities(servus.WithClientIdentities(servicename.CustomerPortal)),
	)
	app.POST(
		"/v1/lending/transactions-search",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetLendingTransactionSearch(ctx, req.(*api.GetLendingTransactionSearchRequest))
			return res, err
		},
		servus.WithRequest(&api.GetLendingTransactionSearchRequest{}),
		servus.WithResponse(&api.GetLendingTransactionSearchResponse{}),
		servus.WithDescription("GetLendingTransactionSearch : API to fetch all transactions for customer (Flexi Loan Account only)."),
		servus.WithClientIdentities(servicename.SentryT6),
	)
	app.POST(
		"/v1/lending/transaction-detail",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetLendingTransactionDetail(ctx, req.(*api.GetLendingTransactionDetailRequest))
			return res, err
		},
		servus.WithRequest(&api.GetLendingTransactionDetailRequest{}),
		servus.WithResponse(&api.GetLendingTransactionDetailResponse{}),
		servus.WithDescription("GetLendingTransactionDetail: API to fetch information about a particular loan transaction."),
		servus.WithClientIdentities(servicename.SentryT6),
	)
	app.POST(
		"/v1/lending/transactions-search-cx",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetLendingTransactionSearchForCRM(ctx, req.(*api.GetLendingTransactionSearchForCRMRequest))
			return res, err
		},
		servus.WithRequest(&api.GetLendingTransactionSearchForCRMRequest{}),
		servus.WithResponse(&api.GetLendingTransactionSearchResponse{}),
		servus.WithDescription("GetLendingTransactionSearchForCRM : API to fetch all transactions for customer"),
		withClientIdentities(servus.WithClientIdentities(servicename.CustomerPortal)),
	)
	app.POST(
		"/v1/lending/transaction-detail-cx",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := t.GetLendingTransactionDetailForCRM(ctx, req.(*api.GetLendingTransactionDetailForCRMRequest))
			return res, err
		},
		servus.WithRequest(&api.GetLendingTransactionDetailForCRMRequest{}),
		servus.WithResponse(&api.GetLendingTransactionDetailResponse{}),
		servus.WithDescription("GetLendingTransactionDetailForCRM : API to fetch information about a particular loan transaction."),
		withClientIdentities(servus.WithClientIdentities(servicename.CustomerPortal, servicename.SentryPartnerT6)),
	)
}

package handlers

import (
	"context"
	"errors"
	"slices"
	"testing"

	"gitlab.myteksi.net/dakota/transaction-history/test/utils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"

	accountServiceApi "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	txnhistoryUtils "gitlab.myteksi.net/dakota/transaction-history/utils"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile"
	accountServiceDBMYApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceDBMYMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
)

func TestGetCASATransactionSummary(t *testing.T) {
	locale := utils.GetLocale()
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}

	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsAuthorizationWithProfileIDEnabled").Return(true)
	flagRepo.On("IsTransactionsSearchDurationLimitEnabled").Return(false)
	flagRepo.On("IsBizAuthorisationEnabled").Return(false)
	activeProfileObj := activeProfile.ActiveProfile{
		ProfileID:   "test-cif",
		ProfileType: "CIF",
	}
	ctx := txnhistoryUtils.AddActiveProfileToHeader(context.Background(), activeProfileObj)
	mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}
	mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceDBMYApi.GetAccountResponse{
		Account: &accountServiceDBMYApi.Account{
			CifNumber: "test-cif",
		},
	}, nil)

	t.Run("missing-parameter-accountID", func(t *testing.T) {
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		txHistoryService := TxHistoryService{
			AppConfig: mockAppConfig,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'accountID' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetCASATransactionsSummary(context.Background(), req)
		assert.Equal(t, expectedError, err)
		assert.Equal(t, (*api.GetCASATransactionsSummaryResponse)(nil), response)
	})
	t.Run("error-response-request", func(t *testing.T) {
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		txHistoryService := TxHistoryService{
			AppConfig:                mockAppConfig,
			CustomerMasterClient:     mockCustomerMasterClient,
			AccountServiceClient:     mockAccountServiceClient,
			AccountServiceDBMYClient: mockAccountServiceDBMYClient,
			FeatureFlags:             flagRepo,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregate{}, errors.New("random error message"))
		storage.InterestAggregateD = mockInterestAggregate
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		expectedErr := customErr.DefaultInternalServerError
		storage.TransactionsDataD = mockTransactionData

		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		actualResp, err := txHistoryService.GetCASATransactionsSummary(ctx, req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})

	t.Run("happy path", func(t *testing.T) {
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		txHistoryService := TxHistoryService{
			AppConfig:                mockAppConfig,
			CustomerMasterClient:     mockCustomerMasterClient,
			AccountServiceClient:     mockAccountServiceClient,
			AccountServiceDBMYClient: mockAccountServiceDBMYClient,
			FeatureFlags:             flagRepo,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.GetCASATransactionsSummaryValidResponse()

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferTransactionsDBMockRows(), nil).Once()
		storage.TransactionsDataD = mockTransactionData

		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		response, err := txHistoryService.GetCASATransactionsSummary(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("dbmy-happy path", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		// Mocking Methods of External Services
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(
			responses.DBMYListCASAAccountsForCustomerDetailResponse(), nil)
		txHistoryService := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig, AccountServiceDBMYClient: mockAccountServiceDBMYClient, FeatureFlags: flagRepo,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.DBMYGetCASATransactionsSummaryValidWithCardTxnResponse()

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYInterestTransactionsDBMockRows(), nil).Once()
		storage.InterestAggregateD = mockInterestAggregate
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		transactionDatas := slices.Concat(
			resources.DBMYTransferTransactionsDBMockRows(), resources.CardMaintenanceFeeTransactionsDBMockRows(), resources.CardMaintenanceFeeWaiverTransactionsDBMockRows(),
		)
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(transactionDatas, nil).Once()
		storage.TransactionsDataD = mockTransactionData
		mockPaymentDetail := &storage.MockIPaymentDetailDAO{}
		mockPaymentDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferPaymentDetailDBMockRows(), nil).Once()
		storage.PaymentDetailD = mockPaymentDetail
		mockCardDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferCardDetailDBMockRows(), nil).Once()
		storage.CardTransactionDetailD = mockCardDetail

		accountResponse := &accountServiceApi.GetAccountResponse{
			Account: &accountServiceApi.Account{
				CifNumber: "test-cif",
			},
		}
		mockAccountServiceClient.On("GetAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()

		response, err := txHistoryService.GetCASATransactionsSummary(ctx, req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("dbmy-empty-response-request", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		// Mocking Methods of External Services
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: "MYR",
			},
		}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(
			responses.DBMYListCASAAccountsForCustomerDetailResponse(), nil)
		txHistoryService := TxHistoryService{
			CustomerMasterClient:     mockCustomerMasterClient,
			AccountServiceClient:     mockAccountServiceClient,
			AppConfig:                mockAppConfig,
			AccountServiceDBMYClient: mockAccountServiceDBMYClient,
			FeatureFlags:             flagRepo,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregate{}, nil)
		storage.InterestAggregateD = mockInterestAggregate
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionData
		mockPaymentDetail := &storage.MockIPaymentDetailDAO{}
		mockPaymentDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockPaymentDetail
		expectedResp := responses.GetCASATransactionsSummaryEmptyResponses()

		accountResponse := &accountServiceApi.GetAccountResponse{
			Account: &accountServiceApi.Account{
				CifNumber: "test-cif",
			},
		}
		mockAccountServiceClient.On("GetAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()

		actualResp, err := txHistoryService.GetCASATransactionsSummary(ctx, req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})

	t.Run("dbmy-error-getting-account-list", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		// Mocking Methods of External Services
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(
			nil, errors.New("random error message"))
		txHistoryService := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig, AccountServiceDBMYClient: mockAccountServiceDBMYClient, FeatureFlags: flagRepo,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregate{}, errors.New("random error message"))
		storage.InterestAggregateD = mockInterestAggregate
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		expectedErr := customErr.DefaultInternalServerError
		storage.TransactionsDataD = mockTransactionData

		accountResponse := &accountServiceApi.GetAccountResponse{
			Account: &accountServiceApi.Account{
				CifNumber: "test-cif",
			},
		}
		mockAccountServiceClient.On("GetAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()

		actualResp, err := txHistoryService.GetCASATransactionsSummary(ctx, req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})

	t.Run("empty-response-request", func(t *testing.T) {
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		txHistoryService := TxHistoryService{
			AppConfig:                mockAppConfig,
			CustomerMasterClient:     mockCustomerMasterClient,
			AccountServiceClient:     mockAccountServiceClient,
			AccountServiceDBMYClient: mockAccountServiceDBMYClient,
			FeatureFlags:             flagRepo,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregate{}, nil)
		storage.InterestAggregateD = mockInterestAggregate
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil)
		expectedResp := responses.GetCASATransactionsSummaryEmptyResponses()
		storage.TransactionsDataD = mockTransactionData

		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		actualResp, err := txHistoryService.GetCASATransactionsSummary(ctx, req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("no data error in db", func(t *testing.T) {
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		txHistoryService := TxHistoryService{
			AppConfig:                mockAppConfig,
			CustomerMasterClient:     mockCustomerMasterClient,
			AccountServiceClient:     mockAccountServiceClient,
			AccountServiceDBMYClient: mockAccountServiceDBMYClient,
			FeatureFlags:             flagRepo,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.InterestAggregateD = mockInterestAggregate
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		expectedResp := responses.GetCASATransactionsSummaryEmptyResponses()
		storage.TransactionsDataD = mockTransactionData

		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		actualResp, err := txHistoryService.GetCASATransactionsSummary(ctx, req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
}

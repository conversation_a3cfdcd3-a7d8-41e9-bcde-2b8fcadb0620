package handlers

import (
	context "context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/handlerlogic"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	api "gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetSTM453TransactionInfo : API to fetch STM453 Transaction Info
func (t *TxHistoryService) GetSTM453TransactionInfo(ctx context.Context, req *api.GetSTM453TransactionInfoRequest) (*api.GetSTM453TransactionInfoResponse, error) {
	slog.FromContext(ctx).Info(constants.GetSTM453TransactionInfoLogTag, fmt.Sprintf("Received Request: %+v", req))

	if validationError := handlerlogic.GetSTM453TransactionDetailRequestValidator(req); validationError != nil {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = append(err.Errors, *validationError)
		return nil, err
	}

	response, err := handlerlogic.GetSTM453TransactionDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetSTM453TransactionInfoLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return response, nil
}

package handlers

import (
	"context"
	"errors"
	"testing"

	"gitlab.myteksi.net/dakota/transaction-history/test/utils"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	txnHistortUtils "gitlab.myteksi.net/dakota/transaction-history/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile"
	"gitlab.myteksi.net/dbmy/transaction-history/api"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestGetCASATotalInterestEarned(t *testing.T) {
	locale := utils.GetLocale()
	t.Run("missing-parameter-accountID", func(t *testing.T) {
		txHistoryService := TxHistoryService{}
		req := &api.GetCASAInterestEarnedRequest{
			AccountID: "",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'accountID' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetCASAInterestEarned(context.Background(), req)
		assert.Equal(t, expectedError, err)
		assert.Equal(t, (*api.GetCASAInterestEarnedResponse)(nil), response)
	})
	t.Run("happy path", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		txHistoryService := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASAInterestEarnedRequest{
			AccountID: "**********",
		}
		expectedResp := responses.GetCASAInterestEarnedValidResponse()

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows()[:1], nil)
		storage.InterestAggregateD = mockInterestAggregate

		response, err := txHistoryService.GetCASAInterestEarned(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("empty-response-request", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		txHistoryService := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASAInterestEarnedRequest{
			AccountID: "**********",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.InterestAggregateD = mockInterestAggregate
		expectedResp := responses.GetCASAInterestEarnedEmptyResponse()

		actualResp, err := txHistoryService.GetCASAInterestEarned(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("error-response-request", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		txHistoryService := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetCASAInterestEarnedRequest{
			AccountID: "**********",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregate{}, errors.New("random error message"))
		storage.InterestAggregateD = mockInterestAggregate
		expectedErr := customErr.DefaultInternalServerError

		actualResp, err := txHistoryService.GetCASAInterestEarned(context.Background(), req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})
}

func TestGetCASATotalInterestEarnedUnauthorizedPath(t *testing.T) {
	// Mocking External Services
	mockCustomerMasterServiceClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	txhistoryInterface := TxHistoryService{CustomerMasterClient: mockCustomerMasterServiceClient, AccountServiceClient: mockAccountServiceClient}

	activeProfileObj := activeProfile.ActiveProfile{
		ProfileID:   "test-cif",
		ProfileType: "CIF",
	}
	ctx := txnHistortUtils.AddActiveProfileToHeader(context.Background(), activeProfileObj)

	t.Run("unauthorized-account-request", func(t *testing.T) {
		req := &api.GetTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		}
		mockCustomerMasterServiceClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif2"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_FORBIDDEN}, nil)
		expectedError := customErr.BuildErrorResponse(customErr.Unauthorized, "AccountPermission_FORBIDDEN")

		response, err := txhistoryInterface.GetTransactionDetail(ctx, req)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetTransactionDetailResponse)(nil), response)
	})
}

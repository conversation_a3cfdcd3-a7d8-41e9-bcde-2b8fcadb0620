package handlers

import (
	context "context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/logic/handlerlogic"
	api "gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetAccountTransactionsSearchForCX API to fetch all transactions for customer (currently CASA account only) for CX.
func (t *TxHistoryService) GetAccountTransactionsSearchForCX(ctx context.Context, req *api.GetTransactionsHistoryCXRequest) (*api.GetTransactionsHistoryCXResponse, error) {
	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("Received Request: %+v", req))

	parsedRequest := &dto.TransactionHistorySearchRequest{
		AccountID:          req.AccountID,
		EndDate:            req.EndDateTimeStamp,
		StartDate:          req.StartDateTimeStamp,
		PageSize:           req.PageSize,
		EndingAfter:        req.EndingAfter,
		StartingBefore:     req.StartingBefore,
		TransactionDomain:  req.TransactionDomain,
		TransactionType:    req.TransactionType,
		TransactionSubtype: req.TransactionSubtype,
	}
	if err := handlerlogic.GetCxTransactionListValidator(parsedRequest); err != nil {
		return nil, err
	}

	getAccountTransactionSearchObj := handlerlogic.GetAccountTransactionsSearchStruct{
		AccountServiceClient: t.AccountServiceClient,
	}
	response, err := getAccountTransactionSearchObj.GetAllCxTransactionsFromDB(ctx, parsedRequest)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return handlerlogic.ParseTransactionHistoryForCXResponse(response), err
}

package handlers

import (
	"context"
	"database/sql"
	"net/http"
	"os"
	"testing"

	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	utils2 "gitlab.myteksi.net/dakota/transaction-history/utils"

	"gitlab.myteksi.net/dakota/transaction-history/test/utils"

	accountServiceApi "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestOpsSearch(t *testing.T) {
	locale := utils.GetLocale()
	tests := []struct {
		testDesc         string
		req              *api.GetOpsSearchRequest
		expectedResponse *api.GetOpsSearchResponse

		TransactionDataDBFindResponse []*storage.TransactionsData
		TransactionDataDBFindError    error

		GetAccountDetailsByAccountIDResponse *accountServiceApi.GetAccountResponse
		GetAccountDetailsByAccountIDError    error

		isErrorExpected bool
		expectedErr     error
	}{
		{
			testDesc:                             "Should return error when identifier is missing",
			req:                                  &api.GetOpsSearchRequest{AccountID: "", ExternalID: "", TransactionID: "", BatchID: ""},
			isErrorExpected:                      true,
			TransactionDataDBFindResponse:        nil,
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: nil,
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          customErr.BuildCustomErrorResponse(http.StatusBadRequest, utils2.SafeIntToString(customErr.ErrMissingIdentifier.Code), customErr.ErrMissingIdentifier.Message),
		},
		{
			testDesc:                             "Should return error when account id and external id is passed in",
			req:                                  &api.GetOpsSearchRequest{AccountID: "123", ExternalID: "abc", TransactionID: "", BatchID: ""},
			isErrorExpected:                      true,
			TransactionDataDBFindResponse:        nil,
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: nil,
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          customErr.BuildCustomErrorResponse(http.StatusBadRequest, utils2.SafeIntToString(customErr.ErrInvalidIdentifier.Code), customErr.ErrInvalidIdentifier.Message),
		},
		{
			testDesc:                             "Should return error when external id and any attribute filter is passed in",
			req:                                  &api.GetOpsSearchRequest{AccountID: "", ExternalID: "abc", TransactionID: "", BatchID: "", TransactionType: "TRANSFER_MONEY"},
			isErrorExpected:                      true,
			TransactionDataDBFindResponse:        nil,
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: nil,
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          customErr.BuildCustomErrorResponse(http.StatusBadRequest, utils2.SafeIntToString(customErr.ErrInvalidFilter.Code), customErr.ErrInvalidFilter.Message),
		},
		{
			testDesc:                             "Should return error when transaction id and any attribute filter is passed in",
			req:                                  &api.GetOpsSearchRequest{AccountID: "", ExternalID: "", TransactionID: "abc", BatchID: "", TransactionType: "TRANSFER_MONEY"},
			isErrorExpected:                      true,
			TransactionDataDBFindResponse:        nil,
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: nil,
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          customErr.BuildCustomErrorResponse(http.StatusBadRequest, utils2.SafeIntToString(customErr.ErrInvalidFilter.Code), customErr.ErrInvalidFilter.Message),
		},
		{
			testDesc:                             "Should return error when batch id and any attribute filter is passed in",
			req:                                  &api.GetOpsSearchRequest{AccountID: "", ExternalID: "", TransactionID: "abc", BatchID: "", TransactionSubtype: "INTRABANK"},
			isErrorExpected:                      true,
			TransactionDataDBFindResponse:        nil,
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: nil,
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          customErr.BuildCustomErrorResponse(http.StatusBadRequest, utils2.SafeIntToString(customErr.ErrInvalidFilter.Code), customErr.ErrInvalidFilter.Message),
		},
		{
			testDesc:                             "Should return error when page size is greater than max page size",
			req:                                  &api.GetOpsSearchRequest{AccountID: "test-id", PageSize: 251},
			isErrorExpected:                      true,
			TransactionDataDBFindResponse:        nil,
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: nil,
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          customErr.BuildCustomErrorResponse(http.StatusBadRequest, utils2.SafeIntToString(customErr.ErrInvalidPageSize.Code), customErr.ErrInvalidPageSize.Message),
		},
		{
			testDesc:                             "Should return  error when page size is lesser than min page size",
			req:                                  &api.GetOpsSearchRequest{AccountID: "test-id", PageSize: -1},
			isErrorExpected:                      true,
			TransactionDataDBFindResponse:        nil,
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: nil,
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          customErr.BuildCustomErrorResponse(http.StatusBadRequest, utils2.SafeIntToString(customErr.ErrInvalidPageSize.Code), customErr.ErrInvalidPageSize.Message),
		},
		{
			testDesc:                             "Should return error when error returned from TransactionDataDB call",
			req:                                  &api.GetOpsSearchRequest{AccountID: "test-id", PageSize: 150},
			isErrorExpected:                      true,
			expectedResponse:                     nil,
			TransactionDataDBFindResponse:        nil,
			TransactionDataDBFindError:           customErr.DefaultInternalServerError,
			GetAccountDetailsByAccountIDResponse: nil,
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          customErr.DefaultInternalServerError,
		},
		{
			testDesc: "Should return success for the first page when no error received",
			req: &api.GetOpsSearchRequest{
				AccountID: "**********",
				StartDate: "2021-08-01",
				EndDate:   "2021-08-31",
				PageSize:  2,
			},
			isErrorExpected:                      false,
			TransactionDataDBFindResponse:        resources.GetOpsSearchFirstPageMockDBResponse(),
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          nil,
			expectedResponse:                     responses.GetOpsSearchFirstPageResponseForListTransaction(),
		},
		{
			testDesc: "filter by externalID - Should return success for the first page when no error received",
			req: &api.GetOpsSearchRequest{
				ExternalID: "xyz123",
			},
			isErrorExpected:                      false,
			TransactionDataDBFindResponse:        resources.OpsSearchTransactionsDataByExternalIDMockDBRows(),
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          nil,
			expectedResponse:                     responses.GetOpsSearchByExternalIDForListTransaction(),
		},
		{
			testDesc: "Should return success for the no date filters when no error received",
			req: &api.GetOpsSearchRequest{
				AccountID: "12345",
				PageSize:  3,
			},
			isErrorExpected:                      false,
			TransactionDataDBFindResponse:        resources.OpsSearchTransactionsDataMockDBRows(),
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          nil,
			expectedResponse:                     responses.GetOpsSearchOnlyPageResponse(),
		},
		{
			testDesc: "Should return success for the last page when no error received",
			req: &api.GetOpsSearchRequest{
				AccountID:      "**********",
				StartDate:      "2021-08-01",
				EndDate:        "2021-08-31",
				PageSize:       2,
				StartingBefore: "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==",
			},
			isErrorExpected:                      false,
			TransactionDataDBFindResponse:        resources.GetOpsSearchLastPageMockDBResponse(),
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          nil,
			expectedResponse:                     responses.GetOpsSearchLastPageResponseForListResponse(),
		},
		{
			testDesc: "Should return success for backward scrolling when no error received",
			req: &api.GetOpsSearchRequest{
				AccountID:   "**********",
				StartDate:   "2021-08-01",
				EndDate:     "2021-08-31",
				PageSize:    2,
				EndingAfter: "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=",
			},
			isErrorExpected:                      false,
			TransactionDataDBFindResponse:        resources.OpsSearchGetAllTransactionsBackwardScrollingMockDBResponse(),
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          nil,
			expectedResponse:                     responses.GetOpsSearchBackwardScrollingPrevPageResponse(),
		},
		{
			testDesc: "Should return success for backward scrolling when no error received (only one response)",
			req: &api.GetOpsSearchRequest{
				AccountID:   "**********",
				StartDate:   "2021-08-01",
				EndDate:     "2021-08-31",
				PageSize:    2,
				EndingAfter: "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGVmZzExMTFhYmQ=",
			},
			isErrorExpected:                      false,
			TransactionDataDBFindResponse:        resources.OpsSearchGetAllTransactionsBackwardScrollingMockDBOneResponse(),
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          nil,
			expectedResponse:                     responses.GetOpsSearchBackwardScrollingPrevPageOneResponse(),
		},
		{
			testDesc: "Should return success for prev next page exist when no error received",
			req: &api.GetOpsSearchRequest{
				AccountID:      "**********",
				StartDate:      "2021-08-01",
				EndDate:        "2021-08-31",
				PageSize:       1,
				StartingBefore: "MjAyMS0wOC0yMFQwMjoxMTo0MFosMiwzLGVmZzEyM2FiZA==",
			},
			isErrorExpected:                      false,
			TransactionDataDBFindResponse:        resources.OpsSearchGetAllTransactionsBackwardScrollingMockDBResponse(),
			TransactionDataDBFindError:           nil,
			GetAccountDetailsByAccountIDResponse: responses.GetAccountDetailsByAccountIDResponse(),
			GetAccountDetailsByAccountIDError:    nil,
			expectedErr:                          nil,
			expectedResponse:                     responses.GetOpsSearchPrevNextBothExistResponse(),
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(test.testDesc, func(t *testing.T) {
			defer func() { config.SetTenant("") }()
			config.SetTenant(tenants.TenantMY)
			ctx := context.Background()
			mockAccountServiceClient := &accountServiceMock.AccountService{}
			mockStore := &storage.MockDatabaseStore{}
			mockAppConfig := &config.AppConfig{
				TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
					DefaultCurrency: locale.Currency,
				},
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{
							MysqlMasterSlaveConfig: data.MysqlMasterSlaveConfig{},
						},
					},
				},
				IconConfig: config.IconConfig{},
				Locale:     config.Locale{Language: "en"},
			}
			os.Setenv("LOCALISATION_PATH", "../localise")
			localise.Init(mockAppConfig)
			constants.InitializeDynamicConstants(mockAppConfig)
			presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
			txInterface := TxHistoryService{
				AccountServiceClient: mockAccountServiceClient,
				AppConfig:            mockAppConfig,
				Store:                mockStore,
			}
			mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(test.GetAccountDetailsByAccountIDResponse, test.GetAccountDetailsByAccountIDError)

			// Mocking Datastore
			mockStore.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(&sql.DB{}, nil).Once()
			mockStore.On("GetTxnDataByExternalIDFromDB", mock.Anything, mock.Anything, mock.Anything).Return(test.TransactionDataDBFindResponse, test.TransactionDataDBFindError)

			// Mocking Payment Call
			mockStorageDAO := &storage.MockIPaymentDetailDAO{}
			mockStorageDAO.On("Find",
				mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
			).Return(resources.OpsSearchPaymentDetailMockDBRows(), nil)
			storage.PaymentDetailD = mockStorageDAO

			// mocking TransactionsData call
			mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
			mockTransactionDataStorageDAO.On("Find",
				mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(test.TransactionDataDBFindResponse, test.TransactionDataDBFindError)
			storage.TransactionsDataD = mockTransactionDataStorageDAO

			got, err := txInterface.GetOpsSearch(ctx, test.req)

			if test.isErrorExpected {
				assert.Error(t, err, test.testDesc)
				assert.Equal(t, test.expectedErr, err, test.testDesc)
			} else {
				assert.Equal(t, test.expectedResponse, got, test.testDesc)
			}
		})
	}
}

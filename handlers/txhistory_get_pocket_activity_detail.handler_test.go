package handlers

import (
	"context"
	"errors"
	"os"
	"testing"

	"gitlab.myteksi.net/dakota/transaction-history/test/utils"

	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	accountServiceDBMYApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceDBMYMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	"gitlab.myteksi.net/dbmy/transaction-history/api"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestGetPocketActivityDetailRequestValidation(t *testing.T) {
	locale := utils.GetLocale()
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}

	t.Run("pocketID-missing", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketType:    "SAVINGS",
			TransactionID: "test-transaction-id",
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`pocketID` is a mandatory field."}},
		}

		response, err := txInterface.GetPocketActivityDetail(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivityDetailResponse)(nil), response)
	})
	t.Run("pocketType-missing", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "1234000",
			TransactionID: "test-transaction-id",
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`pocketType` is a mandatory field."}},
		}

		response, err := txInterface.GetPocketActivityDetail(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivityDetailResponse)(nil), response)
	})
	t.Run("pocketType-invalid", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "1234000",
			PocketType:    "SPEND",
			TransactionID: "test-transaction-id",
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'pocketType' is invalid."}},
		}

		response, err := txInterface.GetPocketActivityDetail(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivityDetailResponse)(nil), response)
	})
	t.Run("transaction id missing", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:   "1234000",
			PocketType: "SAVINGS",
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'transactionID' is a mandatory parameter."}},
		}

		response, err := txInterface.GetPocketActivityDetail(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivityDetailResponse)(nil), response)
	})
}

func TestGetPocketActivityDetailHappyPath(t *testing.T) {
	locale := utils.GetLocale()
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/common/images/UserDefault.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/common/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/common/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/common/images/UserDefault.png",
		},
		Locale: config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	// Mocking Methods of External Services
	mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
		&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
	mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
		&accountServiceDBMYApi.GetAccountResponse{
			Account: &accountServiceDBMYApi.Account{
				ParentAccountID: "**********",
				CifNumber:       "test-cif",
			},
		}, nil)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AppConfig: mockAppConfig, AccountServiceDBMYClient: mockAccountServiceDBMYClient,
	}

	t.Run("no-matching-resources", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-txn-id",
		}
		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResp := &api.GetPocketActivityDetailResponse{}

		response, err := txInterface.GetPocketActivityDetail(context.Background(), request)
		assert.Nil(t, err)
		assert.Equal(t, expectedResp, response)
	})
	t.Run("funds-in-transaction", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-client-batch-id1",
		}
		expectedResp := responses.GetPocketActivityDetailResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.PocketTransactions()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData

		response, err := txInterface.GetPocketActivityDetail(context.Background(), request)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("funds-out-transaction", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-client-batch-id4",
		}
		expectedResp := responses.GetPocketActivityDetailResponse()[1]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.PocketTransactions()[2]}, nil)
		storage.TransactionsDataD = mockTransactionData

		response, err := txInterface.GetPocketActivityDetail(context.Background(), request)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("InterestPayout-transaction", func(t *testing.T) {
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-client-batch-id-interest-payout",
		}
		expectedResp := responses.GetPocketActivityDetailResponse()[2]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.PocketInterestPayoutTransactionInput()[1]}, nil)
		storage.TransactionsDataD = mockTransactionData

		response, err := txInterface.GetPocketActivityDetail(context.Background(), request)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
}

func TestGetPocketActivityDetailErrorPath(t *testing.T) {
	t.Run("not a pocket error", func(t *testing.T) {
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "",
					CifNumber:       "test-cif",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceDBMYClient: mockAccountServiceDBMYClient,
		}
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-txn-id",
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, errors.New("random error message"))
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedError := customErr.BuildErrorResponse(customErr.BadRequest, "The pocket in the request is invalid")
		response, err := txInterface.GetPocketActivityDetail(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivityDetailResponse)(nil), response)
	})
	t.Run("db-fetch-error", func(t *testing.T) {
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txInterface := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceDBMYClient: mockAccountServiceDBMYClient,
		}
		request := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-txn-id",
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, errors.New("random error message"))
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response, err := txInterface.GetPocketActivityDetail(context.Background(), request)
		assert.Error(t, err, customErr.DefaultInternalServerError)
		assert.Equal(t, (*api.GetPocketActivityDetailResponse)(nil), response)
	})
}

func TestGetPocketActivityDetailUnauthorizedPath(t *testing.T) {
	locale := utils.GetLocale()
	// Mocking External Services
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AppConfig: mockAppConfig, AccountServiceDBMYClient: mockAccountServiceDBMYClient,
	}

	t.Run("unauthorized-account-request", func(t *testing.T) {
		req := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-transaction-id",
		}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif2"}, nil)
		mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		expectedError := customErr.BuildErrorResponse(customErr.Unauthorized, "AccountPermission_FORBIDDEN")

		response, err := txInterface.GetPocketActivityDetail(context.Background(), req)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivityDetailResponse)(nil), response)
	})

	t.Run("invalid-cif-request", func(t *testing.T) {
		req := &api.GetPocketActivityDetailRequest{
			PocketID:      "*************",
			PocketType:    "SAVINGS",
			TransactionID: "test-transaction-id",
		}
		expectedError := servus.ServiceError{HTTPCode: 401, Code: "unauthorized", Message: "CIF_MAPPING_NOT_FOUND:cif number not found", Errors: []servus.ErrorDetail(nil)}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: ""}, expectedError)

		response, err := txInterface.GetPocketActivityDetail(context.Background(), req)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetPocketActivityDetailResponse)(nil), response)
	})
}

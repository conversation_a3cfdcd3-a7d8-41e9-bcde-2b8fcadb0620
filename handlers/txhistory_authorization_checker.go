package handlers

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
)

// CheckIfAuthorized method calls customerMaster and AccountService to check if account belongs to respective user
// Also returns customerID if the auth is valid.
// This is to avoid duplicated effort in case we also need customerId after auth, i.e. see GetAccountCalendarActivity
func CheckIfAuthorized(ctx context.Context, accountID string, t *TxHistoryService) (string, error) {
	slog.FromContext(ctx).Info(constants.CheckAuthorizationLogTag, "checking request authentication")

	customerID, err := utils.GetCustomerIDWithActiveProfile(ctx, accountID, t.AccountServiceDBMYClient, t.CustomerMasterDBMYClient)
	if err != nil {
		return "", err
	}

	// Check Permission for CIF Number
	slog.FromContext(ctx).Info(constants.CheckAuthorizationLogTag, fmt.Sprintf("checking permission for customerID: %s", customerID))
	status, checkErr := utils.CheckPermissionAccountService(ctx, accountID, customerID, t.AccountServiceClient)
	if checkErr != nil {
		return "", checkErr
	}
	// Return if Not Allowed
	if status != constants.AccountPermissionAllowed {
		return "", customErr.BuildErrorResponse(customErr.Unauthorized, "account permission forbidden")
	}
	return customerID, nil
}

// CheckPermissionAndGetParent method calls customerMaster and AccountService to check if account belongs to respective user and return the parent accountID
func CheckPermissionAndGetParent(ctx context.Context, accountID string, t *TxHistoryService) (string, error) {
	slog.FromContext(ctx).Info(constants.CheckPermissionAndGetParentTag, "checking request authentication")
	// Look Up for CIF Number
	cifNumber, lookUpErr := utils.GetProfileID(ctx, t.CustomerMasterClient)
	if lookUpErr != nil {
		return "", lookUpErr
	}

	// Get account from account-service
	account, getErr := utils.GetAccountFromAccountService(ctx, accountID, t.AccountServiceDBMYClient)
	if getErr != nil {
		return "", getErr
	}
	// Return err if not cif number doesn't match
	if account.CifNumber != cifNumber {
		return "", customErr.BuildErrorResponse(customErr.Unauthorized, "account permission forbidden")
	}
	return account.ParentAccountID, nil
}

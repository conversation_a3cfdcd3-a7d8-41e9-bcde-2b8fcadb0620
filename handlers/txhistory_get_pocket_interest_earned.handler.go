package handlers //nolint:dupl

import (
	context "context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/handlerlogic"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	api "gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetPocketInterestEarned via POST
func (t *TxHistoryService) GetPocketInterestEarned(ctx context.Context, req *api.GetPocketInterestEarnedRequest) (*api.GetPocketInterestEarnedResponse, error) {
	slog.FromContext(ctx).Info(constants.GetPocketInterestEarnedHandlerLogTag, fmt.Sprintf("Received Request: %+v", req))

	//Check whether the request is valid
	if validationErrors := handlerlogic.GetPocketInterestEarnedRequestValidator(req); len(validationErrors) != 0 {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = validationErrors
		return nil, err
	}

	// Check Permission and Get Parent func call to check permission and get the parent account
	account, authErr := CheckPermissionAndGetParent(ctx, req.PocketID, t)
	if authErr != nil {
		return nil, authErr
	}
	//Check whether the pocket is valid
	if getErr := handlerlogic.GetPocketValidator(account); getErr != nil {
		return nil, getErr
	}
	response, err := handlerlogic.GetPocketInterestEarned(ctx, req, account)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetPocketInterestEarnedHandlerLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return response, nil
}

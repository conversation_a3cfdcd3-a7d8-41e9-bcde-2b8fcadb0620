package handlers //nolint:dupl

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/handlerlogic"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetAccountTransactionsSearch implementation of GetAllTransactions via POST
func (t *TxHistoryService) GetAccountTransactionsSearch(ctx context.Context, req *api.GetTransactionsHistoryRequest) (*api.GetTransactionsHistoryResponse, error) {
	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchLogTag, fmt.Sprintf("Received Request: %+v", req))

	if errors := handlerlogic.GetTransactionListValidator(req); len(errors) != 0 {
		return nil, customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
	}

	//Check Authorization via CIF Number
	_, err := CheckIfAuthorized(ctx, req.AccountID, t)
	if err != nil {
		return nil, err
	}

	getAccountTransactionSearchObj := handlerlogic.GetAccountTransactionsSearchStruct{AccountServiceClient: t.AccountServiceClient}
	response, err := getAccountTransactionSearchObj.GetAllTransactionsFromDB(ctx, req, handlerlogic.EmptySearchTransactionLimitFilter)
	if err != nil {
		return nil, err
	}

	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return response, err
}

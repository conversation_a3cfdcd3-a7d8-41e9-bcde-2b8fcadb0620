package handlers

import (
	"context"
	"errors"
	"testing"

	"gitlab.myteksi.net/dakota/transaction-history/test/utils"

	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	accountServiceDBMYApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceDBMYMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	"gitlab.myteksi.net/dbmy/transaction-history/api"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestGetPocketTotalInterestEarned(t *testing.T) {
	locale := utils.GetLocale()
	t.Run("missing-parameter-pocketID", func(t *testing.T) {
		txHistoryService := TxHistoryService{}
		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "SAVINGS",
			PocketID:   "",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'pocketID' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetPocketInterestEarned(context.Background(), req)
		assert.Equal(t, expectedError, err)
		assert.Equal(t, (*api.GetPocketInterestEarnedResponse)(nil), response)
	})
	t.Run("missing-parameter-pocketType", func(t *testing.T) {
		txHistoryService := TxHistoryService{}
		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "",
			PocketID:   "*************",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'pocketType' is a mandatory parameter."}},
		}

		response, err := txHistoryService.GetPocketInterestEarned(context.Background(), req)
		assert.Equal(t, expectedError, err)
		assert.Equal(t, (*api.GetPocketInterestEarnedResponse)(nil), response)
	})
	t.Run("invalid-parameter-pocketType", func(t *testing.T) {
		txHistoryService := TxHistoryService{}
		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "DEPOSITS",
			PocketID:   "*************",
		}
		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "The request has got invalid parameters",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'pocketType' is invalid."}},
		}

		response, err := txHistoryService.GetPocketInterestEarned(context.Background(), req)
		assert.Equal(t, expectedError, err)
		assert.Equal(t, (*api.GetPocketInterestEarnedResponse)(nil), response)
	})
	t.Run("happy path", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txHistoryService := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceDBMYClient: mockAccountServiceDBMYClient, AppConfig: mockAppConfig,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "SAVINGS",
			PocketID:   "*************",
		}
		expectedResp := responses.GetPocketInterestEarnedValidResponse()

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PocketInterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate

		response, err := txHistoryService.GetPocketInterestEarned(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("empty-response-request", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txHistoryService := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceDBMYClient: mockAccountServiceDBMYClient, AppConfig: mockAppConfig,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "SAVINGS",
			PocketID:   "*************",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.InterestAggregateD = mockInterestAggregate
		expectedResp := responses.GetPocketInterestEarnedEmptyResponse()

		actualResp, err := txHistoryService.GetPocketInterestEarned(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("error-response-request", func(t *testing.T) {
		// Mocking Methods of External Services
		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}
		mockAppConfig := &config.AppConfig{
			TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
				DefaultCurrency: locale.Currency,
			},
		}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			&accountServiceDBMYApi.GetAccountResponse{
				Account: &accountServiceDBMYApi.Account{
					ParentAccountID: "**********",
					CifNumber:       "test-cif",
				},
			}, nil)
		txHistoryService := TxHistoryService{
			CustomerMasterClient: mockCustomerMasterClient, AccountServiceDBMYClient: mockAccountServiceDBMYClient, AppConfig: mockAppConfig,
		}
		constants.InitializeDynamicConstants(mockAppConfig)

		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "SAVINGS",
			PocketID:   "*************",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregate{}, errors.New("random error message"))
		storage.InterestAggregateD = mockInterestAggregate
		expectedErr := customErr.DefaultInternalServerError

		actualResp, err := txHistoryService.GetPocketInterestEarned(context.Background(), req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})
}

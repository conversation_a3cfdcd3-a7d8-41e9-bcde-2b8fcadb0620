package handlers

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	accountServiceApi "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	txnhistoryUtils "gitlab.myteksi.net/dakota/transaction-history/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile"
	accountServiceDBMYApi "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceDBMYMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

func TestGetAllLoanTransactionsParameterMissingPath(t *testing.T) {
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: "MYR",
		},
		IconConfig: config.IconConfig{
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig,
	}

	t.Run("accountID-missing", func(t *testing.T) {
		request := &api.GetLendingTransactionSearchRequest{
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`accountID` is a mandatory field."}},
		}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		response, err := txInterface.GetLendingTransactionSearch(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetLendingTransactionSearchResponse)(nil), response)
	})

	t.Run("invalid-productVariantCode", func(t *testing.T) {
		request := &api.GetLendingTransactionSearchRequest{
			AccountID:          accountID,
			StartDate:          "2021-08-01",
			EndDate:            "2021-08-31",
			PageSize:           2,
			ProductVariantCode: "INVALID",
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "request has invalid parameter(s).",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "'productVariantCode' is invalid."}},
		}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

		response, err := txInterface.GetLendingTransactionSearch(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetLendingTransactionSearchResponse)(nil), response)
	})
}

func TestGetAllLoanTransactionsParameterHappyPath(t *testing.T) {
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: "MYR",
		},
		IconConfig: config.IconConfig{
			LendingDrawdown:    "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction: "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			LendingRepayment:   "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsAuthorizationWithProfileIDEnabled").Return(true)
	flagRepo.On("IsTransactionsSearchDurationLimitEnabled").Return(false)
	flagRepo.On("IsBizAuthorisationEnabled").Return(false)
	activeProfileObj := activeProfile.ActiveProfile{
		ProfileID:   "test-cif",
		ProfileType: "CIF",
	}
	ctx := txnhistoryUtils.AddActiveProfileToHeader(context.Background(), activeProfileObj)
	ctx = featureflag.NewContextWithFeatureFlags(ctx, flagRepo)
	mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}
	mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceDBMYApi.GetAccountResponse{
		Account: &accountServiceDBMYApi.Account{
			CifNumber: "test-cif",
		},
	}, nil)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig, AccountServiceDBMYClient: mockAccountServiceDBMYClient, FeatureFlags: flagRepo,
	}

	// Mocking Methods of External Services
	mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
		&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
	mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
		&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

	// Mocking Payment Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find",
		mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
	).Return(resources.PaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockLoanDetailDao := &storage.MockILoanDetailDAO{}
	mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.LoanDetailsMockDBRowsForTxnSearch(), nil)
	storage.LoanDetailD = mockLoanDetailDao

	t.Run("no-matching-resources in txns", func(t *testing.T) {
		request := &api.GetLendingTransactionSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01T00:00:00Z",
			EndDate:   "2021-08-31T15:00:00Z",
			PageSize:  2,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		expectedResp := &api.GetLendingTransactionSearchResponse{
			Links: map[string]string{"next": "", "nextCursorID": ""},
			Data:  []api.LendingTransactionSearchData{},
		}
		response, err := txInterface.GetLendingTransactionSearch(ctx, request)
		assert.Nil(t, err)
		assert.Equal(t, expectedResp, response)
	})

	t.Run("happy-path-firstPage in txns", func(t *testing.T) {
		request := &api.GetLendingTransactionSearchRequest{
			AccountID: "**********",
			StartDate: "2022-10-01T00:00:00Z",
			EndDate:   "2022-10-12T15:00:00Z",
			PageSize:  1,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.TransactionsDataMockDBRowsForLending(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsFirstPageResponseForLending()
		response, err := txInterface.GetLendingTransactionSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-no-date-filters in txns", func(t *testing.T) {
		request := &api.GetLendingTransactionSearchRequest{
			AccountID: "12345",
			PageSize:  3,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.TransactionsDataMockDBRowsForLending(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		expectedResponse := responses.GetAllTransactionsOnlyPageResponseForLendingWithNoDateFilters()
		response, err := txInterface.GetLendingTransactionSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-onlyOnePage in txns", func(t *testing.T) {
		request := &api.GetLendingTransactionSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01T00:00:00Z",
			EndDate:   "2021-08-31T00:00:00Z",
			PageSize:  5,
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.TransactionsDataMockDBRowsForLending(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsNoNextPageResponseForLending()
		response, err := txInterface.GetLendingTransactionSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-lastPage in txns", func(t *testing.T) {
		request := &api.GetLendingTransactionSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01T00:00:00Z",
			EndDate:   "2021-08-31T00:00:00Z",
			PageSize:  2, // Page Size > remaining entries
		}

		// mocking TransactionsData call
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsFirstPageMockDBResponseForLendingRepayment(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{resources.LoanDetailsMockDBRows()[1]}, nil)
		storage.LoanDetailD = mockLoanDetailDao

		expectedResponse := responses.GetAllTransactionsFirstPageResponseForLendingRepayment()
		response, err := txInterface.GetLendingTransactionSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-onlyOnePage with failed txns", func(t *testing.T) {
		request := &api.GetLendingTransactionSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01T00:00:00Z",
			EndDate:   "2021-08-31T00:00:00Z",
			PageSize:  2, // Page Size > remaining entries
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.AuthFailedDrawdownTransactionsForLending(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		mockLoanDetailDao := &storage.MockILoanDetailDAO{}
		mockLoanDetailDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.LoanDetail{resources.FailedLoanDetailsMockDBRowsForLending("DRAWDOWN")[0]}, nil)
		storage.LoanDetailD = mockLoanDetailDao

		expectedResponse := responses.GetTransactionSearchResultsForLendingBeforePayment("DRAWDOWN", "FAILED")
		response, err := txInterface.GetLendingTransactionSearch(ctx, request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})
}

func TestGetAllLoanTransactionsParameterErrorPath(t *testing.T) {
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: "MYR",
		},
		IconConfig: config.IconConfig{
			InterestPayout:        "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer: "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			LendingDrawdown:       "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:    "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsAuthorizationWithProfileIDEnabled").Return(true)
	flagRepo.On("IsTransactionsSearchDurationLimitEnabled").Return(false)
	flagRepo.On("IsBizAuthorisationEnabled").Return(false)
	mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}
	mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceDBMYApi.GetAccountResponse{
		Account: &accountServiceDBMYApi.Account{
			CifNumber: "test-cif",
		},
	}, nil)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig, AccountServiceDBMYClient: mockAccountServiceDBMYClient, FeatureFlags: flagRepo,
	}

	// Mocking Methods of External Services
	mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
		&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
	mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
		&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_ALLOWED}, nil)

	t.Run("db-fetch-error", func(t *testing.T) {
		request := &api.GetLendingTransactionSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01T00:00:00Z",
			EndDate:   "2021-08-31T00:00:00Z",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response, err := txInterface.GetLendingTransactionSearch(context.Background(), request)
		assert.Error(t, err, customErr.DefaultInternalServerError)
		assert.Equal(t, (*api.GetLendingTransactionSearchResponse)(nil), response)
	})
}

func TestGetAllLoanTransactionsUnauthorizedPath(t *testing.T) {
	// Mocking External Services
	mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: "MYR",
		},
		IconConfig: config.IconConfig{
			InterestPayout:        "https://assets.sgbank.dev/dev/txHistory/images/InterestEarned.png",
			SavingsPocketTransfer: "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			Drawdown:              "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:    "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsAuthorizationWithProfileIDEnabled").Return(true)
	flagRepo.On("IsTransactionsSearchDurationLimitEnabled").Return(false)
	flagRepo.On("IsBizAuthorisationEnabled").Return(false)
	mockAccountServiceDBMYClient := &accountServiceDBMYMock.AccountService{}
	mockAccountServiceDBMYClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceDBMYApi.GetAccountResponse{
		Account: &accountServiceDBMYApi.Account{
			CifNumber: "test-cif",
		},
	}, nil)
	txInterface := TxHistoryService{
		CustomerMasterClient: mockCustomerMasterClient, AccountServiceClient: mockAccountServiceClient, AppConfig: mockAppConfig, AccountServiceDBMYClient: mockAccountServiceDBMYClient, FeatureFlags: flagRepo,
	}

	t.Run("unauthorized_path", func(t *testing.T) {
		request := &api.GetLendingTransactionSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01T00:00:00Z",
			EndDate:   "2021-08-31T00:00:00Z",
			PageSize:  2,
		}

		// Mocking Methods of External Services
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif2"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountServiceApi.CheckPermissionsForAccountResponse{Status: accountServiceApi.AccountPermission_FORBIDDEN}, nil)

		expectedError := customErr.BuildErrorResponse(customErr.Unauthorized, "AccountPermission_FORBIDDEN")

		response, err := txInterface.GetLendingTransactionSearch(context.Background(), request)
		assert.Error(t, err, expectedError)
		assert.Equal(t, (*api.GetLendingTransactionSearchResponse)(nil), response)
	})
}

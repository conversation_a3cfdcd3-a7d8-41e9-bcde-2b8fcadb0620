package handlers // nolint: dupl

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/logic/handlerlogic"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetLendingTransactionDetail ...
// nolint: dupl
func (t *TxHistoryService) GetLendingTransactionDetail(ctx context.Context, req *api.GetLendingTransactionDetailRequest) (*api.GetLendingTransactionDetailResponse, error) {
	slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Received Request: %+v", req))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, t.FeatureFlags)

	// Check whether the request is valid
	if validationErrors := handlerlogic.GetLendingTransactionDetailRequestValidator(req); len(validationErrors) != 0 {
		err := customErr.BuildErrorResponse(customErr.BadRequest, "The request has got invalid parameters")
		err.Errors = validationErrors
		return nil, err
	}

	// Check Authorization via CIF Number
	if _, authErr := CheckIfAuthorized(ctx, req.AccountID, t); authErr != nil {
		return nil, authErr
	}

	impl := handlerlogic.GetLendingTransactionDetailStruct{
		AccountServiceClient:            t.AccountServiceClient,
		PairingServiceClient:            t.PairingServiceClient,
		TransactionHistoryServiceConfig: t.AppConfig.TransactionHistoryServiceConfig,
		FeatureFlags:                    t.AppConfig.FeatureFlags,
	}
	response, err := impl.GetLendingTransactionDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetTransactionDetailHandlerLogTag, fmt.Sprintf("Successfully completed Request: %+v, with response: %+v", req, response))
	return response, nil
}

package handlers //nolint: dupl

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/logic/handlerlogic"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetOpsSearch via POST
// nolint: dupl
func (t *TxHistoryService) GetOpsSearch(ctx context.Context, req *api.GetOpsSearchRequest) (*api.GetOpsSearchResponse, error) {
	slog.FromContext(ctx).Info(constants.GetOpsSearchLogTag, fmt.Sprintf("Received Request: %+v", req))

	getOpsSearchObj := handlerlogic.NewGetOpsSearch(t.AccountServiceClient, t.AppConfig, t.Store)

	ctx = featureflag.NewContextWithFeatureFlags(ctx, t.FeatureFlags)
	featureFlags := featureflag.FeatureFlagsFromContext(ctx)
	if featureFlags == nil || !featureFlags.IsExhaustiveCursorPaginationEnabled() {
		//Check whether the identifier in request is valid
		if err := handlerlogic.GetOpsSearchRequestIdentifierValidator(ctx, req); err != nil {
			return nil, err
		}

		//Check whether the attribute filter is valid
		if err := handlerlogic.GetOpsSearchRequestValidator(ctx, req); err != nil {
			return nil, err
		}

		//Check filter combination is valid
		if err := handlerlogic.GetOpsSearchRequestFilterCombinationValidator(ctx, req); err != nil {
			return nil, err
		}

		response, err := getOpsSearchObj.GetOpsSearch(ctx, req)
		if err != nil {
			return nil, err
		}
		slog.FromContext(ctx).Info(constants.GetOpsSearchLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
		return response, nil
	}

	slog.FromContext(ctx).Info(constants.GetOpsSearchLogTag, "Start to fetch transaction with exhaustive pagination")
	response, err := getOpsSearchObj.FetchTxnWithExhaustiveCursorPagination(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetOpsSearchLogTag, fmt.Sprintf("Successfully completed Request: %+v", req))
	return response, nil
}

package featureflag

import "context"

// CtxFlags is the context key for feature flags
type CtxFlags string

const (
	// CtxFlagsKey is the context key for feature flags
	CtxFlagsKey CtxFlags = "FEATURE_FLAGS"
)

// Repo is a readonly feature flags repository
//
//go:generate mockery --name=Repo --inpackage --case=underscore
type Repo interface {
	IsInsertTransactionsByBatchEnabled() bool
	IsReplicaReadEnabled() bool
	IsCheckStatementStatusEnabled() bool
	IsBatchifyDailyInterestAggEnabled() bool
	IsBatchifyOneTimeInterestAggEnabled() bool
	IsRetryableDepositCoreStreamEnabled() bool
	IsRetryableDepositBalanceStreamEnabled() bool
	IsRetryableInterestAggStreamEnabled() bool
	IsDepositCoreStreamEnabled() bool
	IsDepositBalanceStreamEnabled() bool
	IsBizAuthorisationEnabled() bool
	IsUpdatePostingInstructionBatchForLendingEnabled() bool
	IsTransactionsSearchDurationFilterEnabled() bool
	IsBizFlexiCreditEnabled() bool
	IsTransactionsSearchDurationLimitEnabled() bool
	IsExhaustiveCursorPaginationEnabled() bool
	IsAuthzWithProfileIDEnabled() bool
}

// NewContextWithFeatureFlags returns a new context with feature flags
func NewContextWithFeatureFlags(ctx context.Context, flags Repo) context.Context {
	return context.WithValue(ctx, CtxFlagsKey, flags)
}

// FeatureFlagsFromContext returns the feature flags from the context
func FeatureFlagsFromContext(ctx context.Context) Repo {
	val := ctx.Value(CtxFlagsKey)
	// We could have returned a Noop implementation here, but we want to be explicit
	if val == nil {
		return nil
	}
	return val.(Repo)
}

// Code generated by mockery v2.46.3. DO NOT EDIT.

package featureflag

import mock "github.com/stretchr/testify/mock"

// MockRepo is an autogenerated mock type for the Repo type
type MockRepo struct {
	mock.Mock
}

// IsBatchifyDailyInterestAggEnabled provides a mock function with given fields:
func (_m *MockRepo) IsBatchifyDailyInterestAggEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsBatchifyDailyInterestAggEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsBatchifyOneTimeInterestAggEnabled provides a mock function with given fields:
func (_m *MockRepo) IsBatchifyOneTimeInterestAggEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsBatchifyOneTimeInterestAggEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsBizAuthorisationEnabled provides a mock function with given fields:
func (_m *MockRepo) IsBizAuthorisationEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsBizAuthorisationEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsBizFlexiCreditEnabled provides a mock function with given fields:
func (_m *MockRepo) IsBizFlexiCreditEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsBizFlexiCreditEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsCheckStatementStatusEnabled provides a mock function with given fields:
func (_m *MockRepo) IsCheckStatementStatusEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsCheckStatementStatusEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsDepositBalanceStreamEnabled provides a mock function with given fields:
func (_m *MockRepo) IsDepositBalanceStreamEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsDepositBalanceStreamEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsDepositCoreStreamEnabled provides a mock function with given fields:
func (_m *MockRepo) IsDepositCoreStreamEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsDepositCoreStreamEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsExhaustiveCursorPaginationEnabled provides a mock function with given fields:
func (_m *MockRepo) IsExhaustiveCursorPaginationEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsExhaustiveCursorPaginationEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsInsertTransactionsByBatchEnabled provides a mock function with given fields:
func (_m *MockRepo) IsInsertTransactionsByBatchEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsInsertTransactionsByBatchEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsReplicaReadEnabled provides a mock function with given fields:
func (_m *MockRepo) IsReplicaReadEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsReplicaReadEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsRetryableDepositBalanceStreamEnabled provides a mock function with given fields:
func (_m *MockRepo) IsRetryableDepositBalanceStreamEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsRetryableDepositBalanceStreamEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsRetryableDepositCoreStreamEnabled provides a mock function with given fields:
func (_m *MockRepo) IsRetryableDepositCoreStreamEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsRetryableDepositCoreStreamEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsTransactionsSearchDurationFilterEnabled provides a mock function with given fields:
func (_m *MockRepo) IsTransactionsSearchDurationFilterEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsTransactionsSearchDurationFilterEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsTransactionsSearchDurationLimitEnabled provides a mock function with given fields:
func (_m *MockRepo) IsTransactionsSearchDurationLimitEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsTransactionsSearchDurationLimitEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsUpdatePostingInstructionBatchForLendingEnabled provides a mock function with given fields:
func (_m *MockRepo) IsUpdatePostingInstructionBatchForLendingEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsUpdatePostingInstructionBatchForLendingEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsAuthzWithProfileIDEnabled provides a mock function with given fields:
func (_m *MockRepo) IsAuthzWithProfileIDEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsUpdatePostingInstructionBatchForLendingEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsRetryableInterestAggStreamEnabled provides a mock function with given fields:
func (_m *MockRepo) IsRetryableInterestAggStreamEnabled() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsUpdatePostingInstructionBatchForLendingEnabled")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// NewMockRepo creates a new instance of MockRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRepo(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRepo {
	mock := &MockRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

package worker

import (
	"context"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/utils"

	"github.com/go-co-op/gocron"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/workerlogic"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// UpdateInterestAggregateTableForDailyInterestPayout ...
type UpdateInterestAggregateTableForDailyInterestPayout struct {
	Store storage.DatabaseStore `inject:"dBStore"`
}

const (
	// UpdateInterestAggregateTableForDailyInterestPayoutSchedulerID ...
	UpdateInterestAggregateTableForDailyInterestPayoutSchedulerID = "UPDATE_TOTAL_INTEREST_EARNED"
)

// scheduleUpdateInterestAggregateTableForDailyInterestPayoutWorker  schedules workerlogic that will update interest_aggregate table on daily basis after payout
func scheduleUpdateInterestAggregateTableForDailyInterestPayoutWorker(ctx context.Context, appCfg *config.AppConfig, workerObj UpdateInterestAggregateTableForDailyInterestPayout, s *gocron.Scheduler) {
	workerConfig := appCfg.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout
	_, err := s.Cron(workerConfig.CronExpression).Do(updateInterestAggregateTableJob, ctx, appCfg, workerObj)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateInterestAggregateDBForDailyInterestPayout, fmt.Sprintf("Failed to schedule UpdateInterestAggregateTableForDailyInterestPayoutWorker job: %s", err.Error()))
	}
	slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBForDailyInterestPayout, "successfully scheduled UpdateInterestAggregateTableForDailyInterestPayoutWorker job")
}

// updateInterestAggregateTableJob ...
var updateInterestAggregateTableJob = func(ctx context.Context, appConfig *config.AppConfig, workerObj UpdateInterestAggregateTableForDailyInterestPayout) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.JobBatchIDTagKey), utils.NewUUID()))
	lockDuration := time.Minute * time.Duration(appConfig.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.LockDurationInMinutes)
	mysqlConfig := appConfig.Data.MySQL.Master
	if !checkAndUpdateSchedulerLock(ctx, mysqlConfig, workerObj.Store, UpdateInterestAggregateTableForDailyInterestPayoutSchedulerID, lockDuration) {
		return
	}
	endTime := time.Now().UTC()
	startTime := endTime.AddDate(0, 0, -1)
	var err error
	featFlags := featureflag.FeatureFlagsFromContext(ctx)
	if featFlags == nil || !featFlags.IsBatchifyDailyInterestAggEnabled() {
		slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, "Run legacy daily update interest aggregate.")
		workerlogic.LegacyUpdateInterestAggregateTableForDailyInterestPayout(ctx, startTime, endTime)
	} else {
		slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, "Run batchify daily update interest aggregate.")
		err = workerlogic.UpdateInterestAggregateTableForDailyInterestPayout(ctx, startTime, endTime, appConfig)
	}
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("Failed to complete daily interest payout update, err: %s", err.Error()))
		return
	}
	slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("Daily UpdateInterestScheduler completed, startTime: %s, endTime: %s", startTime, endTime))
}

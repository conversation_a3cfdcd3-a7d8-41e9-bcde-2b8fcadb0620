package worker

import (
	"context"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/internal/metrics"
	"gitlab.myteksi.net/dakota/transaction-history/utils"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/logic/workerlogic"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// PopulateInterestAggregateOneTimer ...
type PopulateInterestAggregateOneTimer struct {
	Store storage.DatabaseStore `inject:"dBStore"`
}

const (
	// PopulateInterestAggregateTableOneTimerSchedulerID ...
	PopulateInterestAggregateTableOneTimerSchedulerID = "POPULATE_TOTAL_INTEREST_ONE_TIMER"
)

// populateInterestAggregateOneTimer is one timer method to populate interest aggregate table by overriding.
// Can be reused in future to fix the entries as it will compute since beginning and override.
func populateInterestAggregateOneTimer(ctx context.Context, appCfg *config.AppConfig, workerObj PopulateInterestAggregateOneTimer) {
	lockDuration := time.Minute * time.Duration(appCfg.PopulateInterestAggregateOneTimerConf.LockDurationInMinutes)
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.JobBatchIDTagKey), utils.NewUUID()))
	mysqlConfig := appCfg.Data.MySQL.Master
	if !checkAndUpdateSchedulerLock(ctx, mysqlConfig, workerObj.Store, PopulateInterestAggregateTableOneTimerSchedulerID, lockDuration) {
		return
	}
	currentTime := time.Now()
	featFlags := featureflag.FeatureFlagsFromContext(ctx)
	if featFlags == nil || !featFlags.IsBatchifyOneTimeInterestAggEnabled() {
		slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, "Run legacy one timer for interest aggregate.")
		workerlogic.LegacyPopulateInterestAggregateOneTimerLogic(ctx, appCfg)
	} else {
		slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, "Run batchify one timer for interest aggregate.")
		workerlogic.PopulateInterestAggregateOneTimerLogic(ctx, appCfg)
	}

	statsDClient.Duration(
		metrics.WorkerMetricTag, metrics.InterestAggregateOneTimerDurationMetric, currentTime,
		fmt.Sprintf("%s%s", metrics.WorkerIDTag, PopulateInterestAggregateTableOneTimerSchedulerID),
	)
}

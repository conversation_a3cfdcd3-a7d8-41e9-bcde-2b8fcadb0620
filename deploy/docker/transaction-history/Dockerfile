ARG BASE_IMAGE="gcr.io/distroless/base:nonroot-amd64"
FROM $BASE_IMAGE

LABEL name="transaction-history"
LABEL version="1.0"
LABEL description="Service to provide history and details of all transactions"
LABEL tech_owner="naveen.kewalramani"
LABEL team_alias="Corebanking"
LABEL slack="gxs-deposits-squads"
LABEL gitlab_repo="https://gitlab.myteksi.net/dakota/transaction-history"
LABEL wiki="https://wiki.grab.com/display/Digibank/Transaction+History+Service"

COPY transaction-history .
COPY init/init.sh .
RUN chmod +x init.sh
COPY db .
COPY localise localise/

USER nonroot

#CMD ["./transaction-history"]
CMD ["./init.sh"]

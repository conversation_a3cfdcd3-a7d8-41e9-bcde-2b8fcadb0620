# handleDepositsCoreStreamForInterestAgg Function Documentation

## Overview

The `handleDepositsCoreStreamForInterestAgg` function is a core component of the interest aggregation system that processes deposits core transaction streams to aggregate interest payout and reversal transactions. It maintains real-time interest aggregation data for customer accounts while ensuring data consistency through Redis-based distributed locking.

## Function Signature

```go
func handleDepositsCoreStreamForInterestAgg(
    ctx context.Context, 
    redisClient redis.Client, 
    aggregationConfig config.InterestAggregationConfig, 
    txData *deposits_core_tx.DepositsCoreTx, 
    stats statsd.Client
) (bool, error)
```

### Parameters

- **ctx**: Context for request lifecycle management and logging
- **redisClient**: Redis client for distributed locking
- **aggregationConfig**: Configuration containing retention settings and account exclusions
- **txData**: Transaction data from deposits core stream
- **stats**: StatsD client for metrics publishing

### Return Values

- **bool**: Indicates whether the operation should be retried (true = retry, false = no retry)
- **error**: Error details if the operation failed

## Key Data Structures

### postingInfo
```go
type postingInfo struct {
    posting *deposits_core_tx.Posting
    txnType string
    txnID   string
}
```

### InterestAggregateV2
```go
type InterestAggregateV2 struct {
    ID                  uint64
    AccountID           string
    AccountAddress      string
    TotalInterestEarned InterestEarned        // map[string]int64
    RecentDay           RecentDataCollection  // map[string]map[string]RecentDayData
    MonthlyHistory      MonthlyDataCollection // map[string]map[string]int64
    Currency            string
    CreatedAt           time.Time
    UpdatedAt           time.Time
}
```

## Process Flow

### 1. Time Range Calculation
- Uses Malaysia timezone (MYT, UTC+8)
- Calculates retention window based on `aggregationConfig.RetentionDay`
- Filters out transactions older than `aggregationConfig.StartMonth`

### 2. Transaction Filtering
Processes only specific transaction types:
- **INTEREST_PAYOUT**: Credit postings
- **INTEREST_PAYOUT_REVERSAL**: Debit postings

### 3. Account Filtering
- Excludes internal accounts specified in `aggregationConfig.ExcludeAccounts`
- Processes only customer-facing accounts

### 4. Distributed Locking
- Implements Redis-based distributed locking per account
- Lock key format: `interest_aggregate_{accountID}_{accountAddress}`
- Lock timeout: `2 * number_of_accounts * seconds`

### 5. Data Aggregation
- Retrieves existing aggregation records from database
- Updates or creates new aggregation records
- Maintains three data collections:
  - **TotalInterestEarned**: Cumulative interest by transaction type
  - **RecentDay**: Daily transaction data within retention window
  - **MonthlyHistory**: Monthly aggregated data

### 6. Database Operations
- **Batch Insert**: For new aggregation records
- **Individual Update**: For existing records
- Publishes metrics for each operation

## Configuration

### InterestAggregationConfig
```go
type InterestAggregationConfig struct {
    RetentionDay    int             // Number of days to retain daily data
    StartMonth      string          // Earliest month to process (format: "2006-01")
    ExcludeAccounts ExcludeAccounts // map[string]bool of accounts to exclude
}
```

## Constants Used

- `InterestPayoutTransactionType = "INTEREST_PAYOUT"`
- `InterestPayoutReversalTransactionType = "INTEREST_PAYOUT_REVERSAL"`
- `DayFormat = "2006-01-02"`
- `MonthFormat = "2006-01"`

## Error Handling

The function implements comprehensive error handling:

1. **Lock Acquisition Failures**: Returns retry=true for transient Redis errors
2. **Database Errors**: Returns retry=true for database operation failures
3. **Data Validation**: Skips invalid transactions without failing the entire batch
4. **Timeout Handling**: Graceful handling of lock timeouts

## Metrics and Logging

- Publishes success/failure metrics via StatsD
- Structured logging with context tags
- Tracks processing statistics per account and transaction type

## Performance Considerations

1. **Batch Processing**: Processes multiple postings in a single database transaction
2. **Selective Locking**: Only locks accounts that require updates
3. **Efficient Queries**: Uses OR clauses to fetch multiple records in single query
4. **Memory Management**: Processes transactions in streaming fashion

## Dependencies

- **Redis**: For distributed locking
- **Database**: For persistent storage of aggregation data
- **StatsD**: For metrics collection
- **Helper Functions**:
  - `calculateDelta()`: Updates aggregation data
  - `getLockKey()`: Generates Redis lock keys
  - `generateTrackerKey()`: Creates tracking keys for metrics
  - `publishMetrics()`: Publishes operation metrics

## Usage Context

This function is typically called from:
- Kafka stream consumers processing deposits core events
- Retry mechanisms for failed aggregation operations
- Batch processing jobs for historical data aggregation

## Thread Safety

The function is designed to be thread-safe through:
- Redis distributed locking
- Stateless operation design
- Context-based request isolation

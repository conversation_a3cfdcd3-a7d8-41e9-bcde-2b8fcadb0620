package presenterhelper

import (
	"context"
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

func TestGetCardTransactionDisplayName(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)

	scenarios := []struct {
		desc         string
		transaction  *storage.TransactionsData
		txnDetail    *storage.CardTransactionDetail
		expectedName string
	}{
		{
			desc: "MDT merchant name",
			transaction: &storage.TransactionsData{
				TransactionType: constants.SpendCardPresentTransactionType,
			},
			txnDetail: &storage.CardTransactionDetail{
				MerchantDescription: "  MDT MERCHANT NAME   123456",
				Metadata:            json.RawMessage("{\"networkID\": \"MDT\"}"),
			},
			expectedName: "MDT MERCHANT NAME",
		},
		{
			desc: "MCN merchant name",
			transaction: &storage.TransactionsData{
				TransactionType: constants.SpendCardPresentTransactionType,
			},
			txnDetail: &storage.CardTransactionDetail{
				MerchantDescription: "  MCN MERCHANT NAME123456   123456",
				Metadata:            json.RawMessage("{\"networkID\": \"MCN\"}"),
			},
			expectedName: "MCN MERCHANT NAME123",
		},
		{
			desc: "MCN merchant description",
			transaction: &storage.TransactionsData{
				TransactionType: constants.SpendCardPresentTransactionType,
			},
			txnDetail: &storage.CardTransactionDetail{
				MerchantDescription: "  MDT MERCHANT",
				Metadata:            json.RawMessage("{\"networkID\": \"MCN\"}"),
			},
			expectedName: "MDT MERCHANT",
		},
		{
			desc: "ATM withdrawal",
			transaction: &storage.TransactionsData{
				TransactionType: constants.SpendCardAtmTransactionType,
			},
			expectedName: "ATM withdrawal",
		},
		{
			desc: "ATM withdrawal fee",
			transaction: &storage.TransactionsData{
				TransactionType: constants.DomesticAtmFeeTransactionType,
			},
			expectedName: "ATM withdrawal fee",
		},
		{
			desc: "ATM Withdraw Refund",
			transaction: &storage.TransactionsData{
				TransactionType: constants.ATMCashWithdrawalRefundTransactionType,
			},
			expectedName: "ATM withdrawal reversal",
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			merchantName := GetCardTransactionDisplayName(context.Background(), scenario.transaction, scenario.txnDetail)
			assert.Equal(t, scenario.expectedName, merchantName)
		})
	}
}

func TestGetCardTransactionDisplayNameForListing(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)

	scenarios := []struct {
		desc         string
		transaction  *storage.TransactionsData
		txnDetail    *storage.CardTransactionDetail
		expectedName string
	}{
		{
			desc: "MDT merchant name",
			transaction: &storage.TransactionsData{
				TransactionType: constants.SpendCardPresentTransactionType,
			},
			txnDetail: &storage.CardTransactionDetail{
				MerchantDescription: "  MDT MERCHANT NAME   123456",
				Metadata:            json.RawMessage("{\"networkID\": \"MDT\"}"),
			},
			expectedName: "MDT MERCHANT NAME",
		},
		{
			desc: "MDT merchant name has retailerID must return retailerID",
			transaction: &storage.TransactionsData{
				TransactionType:    constants.SpendCardPresentTransactionType,
				TransactionSubtype: constants.PaynetMydebitTransactionSubtype,
			},
			txnDetail: &storage.CardTransactionDetail{
				MerchantDescription: "  MDT MERCHANT NAME   123456",
				Metadata:            json.RawMessage("{\"networkID\": \"MDT\", \"retailerID\": \"McDonald\" }"),
			},
			expectedName: "McDonald",
		},
		{
			desc: "MDT merchant name has retailerID but empty must return merchant name",
			transaction: &storage.TransactionsData{
				TransactionType:    constants.SpendCardPresentTransactionType,
				TransactionSubtype: constants.PaynetMydebitTransactionSubtype,
			},
			txnDetail: &storage.CardTransactionDetail{
				MerchantDescription: "  MDT MERCHANT NAME   123456",
				Metadata:            json.RawMessage("{\"networkID\": \"MDT\", \"retailerID\": \"\" }"),
			},
			expectedName: "MDT MERCHANT NAME",
		},
		{
			desc: "ATM withdrawal",
			transaction: &storage.TransactionsData{
				TransactionType: constants.SpendCardAtmTransactionType,
			},
			expectedName: "ATM withdrawal",
		},
		{
			desc: "ATM withdrawal fee",
			transaction: &storage.TransactionsData{
				TransactionType: constants.DomesticAtmFeeTransactionType,
			},
			expectedName: "MEPS ATM withdrawal fee",
		},
		{
			desc: "ATM withdrawal refund",
			transaction: &storage.TransactionsData{
				TransactionType: constants.ATMCashWithdrawalRefundTransactionType,
			},
			expectedName: "ATM withdrawal reversal",
		},
		{
			desc: "ATM withdrawal fee waiver",
			transaction: &storage.TransactionsData{
				TransactionType: constants.DomesticAtmFeeWaiverTransactionType,
			},
			expectedName: "MEPS ATM withdrawal fee waived",
		},
		{
			desc: "New card issuance fee",
			transaction: &storage.TransactionsData{
				TransactionType: constants.NewCardIssuanceFeeTransactionType,
			},
			expectedName: "New card issuance fee",
		},
		{
			desc: "New card issuance fee waiver",
			transaction: &storage.TransactionsData{
				TransactionType: constants.NewCardIssuanceFeeWaiverTransactionType,
			},
			expectedName: "New card issuance fee waived",
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			merchantName := GetCardTransactionDisplayNameForListing(context.Background(), scenario.transaction, scenario.txnDetail)
			assert.Equal(t, scenario.expectedName, merchantName)
		})
	}
}

func TestGetFormattedLocalAmount(t *testing.T) {
	scenarios := []struct {
		desc           string
		transaction    *storage.TransactionsData
		expectedAmount string
	}{
		{
			desc: "MYR",
			transaction: &storage.TransactionsData{
				DebitOrCredit:       constants.DEBIT,
				TransactionAmount:   "12.34",
				TransactionCurrency: "MYR",
			},
			expectedAmount: "RM12.34",
		},
		{
			desc: "MYR",
			transaction: &storage.TransactionsData{
				DebitOrCredit:       constants.CREDIT,
				TransactionAmount:   "12.34",
				TransactionCurrency: "MYR",
			},
			expectedAmount: "RM12.34",
		},
		{
			desc: "sad path",
			transaction: &storage.TransactionsData{
				DebitOrCredit:       constants.DEBIT,
				TransactionAmount:   "12.34",
				TransactionCurrency: "",
			},
			expectedAmount: "",
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			amount := GetFormattedLocalAmount(context.Background(), scenario.transaction)
			assert.Equal(t, scenario.expectedAmount, amount)
		})
	}
}

func TestGetFormattedSettlementAmount(t *testing.T) {
	scenarios := []struct {
		desc           string
		txnDetail      *storage.CardTransactionDetail
		expectedAmount string
	}{
		{
			desc: "MYR",
			txnDetail: &storage.CardTransactionDetail{
				Currency:         "MYR",
				Amount:           1234,
				OriginalAmount:   300,
				CaptureAmount:    100,
				OriginalCurrency: "MYR",
			},
			expectedAmount: "RM1.00",
		},
		{
			desc: "sad path",
			txnDetail: &storage.CardTransactionDetail{
				Currency:         "",
				Amount:           1234,
				OriginalAmount:   1234,
				CaptureAmount:    100,
				OriginalCurrency: "MYR",
			},
			expectedAmount: "",
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			amount := GetFormattedSettlementAmount(scenario.txnDetail)
			assert.Equal(t, scenario.expectedAmount, amount)
		})
	}
}

func TestGetFormattedForeignAmount(t *testing.T) {
	scenarios := []struct {
		desc           string
		txnDetail      *storage.CardTransactionDetail
		transaction    *storage.TransactionsData
		expectedAmount string
	}{
		{
			desc: "MYR",
			txnDetail: &storage.CardTransactionDetail{
				Currency:         "MYR",
				Amount:           1234,
				OriginalAmount:   300,
				OriginalCurrency: "USD",
			},
			transaction:    &storage.TransactionsData{DebitOrCredit: constants.DEBIT},
			expectedAmount: "$3.00",
		},
		{
			desc: "same currency",
			txnDetail: &storage.CardTransactionDetail{
				Currency:         "MYR",
				Amount:           1234,
				OriginalAmount:   1234,
				OriginalCurrency: "MYR",
			},
			transaction:    &storage.TransactionsData{DebitOrCredit: constants.DEBIT},
			expectedAmount: "",
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			amount := GetFormattedForeignAmount(scenario.txnDetail)
			assert.Equal(t, scenario.expectedAmount, amount)
		})
	}
}

func TestGetBankFee(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc           string
		txnDetail      *storage.CardTransactionDetail
		transaction    *storage.TransactionsData
		expectedAmount string
	}{
		{
			desc: "MCN FX txn",
			txnDetail: &storage.CardTransactionDetail{
				Metadata: json.RawMessage("{\"networkID\": \"MCN\", \"currencyConvType\": \"FX\"}"),
			},
			transaction:    &storage.TransactionsData{TransactionType: constants.SpendCardAtmTransactionType, TransactionCurrency: "MYR"},
			expectedAmount: "Waived",
		},
		{
			desc: "MCN DCC txn",
			txnDetail: &storage.CardTransactionDetail{
				Metadata: json.RawMessage("{\"networkID\": \"MCN\", \"currencyConvType\": \"DCC\"}"),
			},
			transaction:    &storage.TransactionsData{TransactionType: constants.SpendCardAtmTransactionType, TransactionCurrency: "MYR"},
			expectedAmount: "Waived",
		},
		{
			desc: "MYS transaction ATM fee amount",
			txnDetail: &storage.CardTransactionDetail{
				Metadata: json.RawMessage("{\"networkID\": \"MCN\", \"atmTransactionFees\": 123, \"acceptorCountryCode\": \"MYS\"}"),
			},
			transaction:    &storage.TransactionsData{TransactionType: constants.SpendCardAtmTransactionType, TransactionCurrency: "MYR"},
			expectedAmount: "",
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			amount := GetBankFee(context.Background(), scenario.transaction, scenario.txnDetail)
			assert.Equal(t, scenario.expectedAmount, amount)
		})
	}
}

func TestGetSettlementDate(t *testing.T) {
	scenarios := []struct {
		desc      string
		txnDetail *storage.CardTransactionDetail
		expected  *time.Time
	}{
		{
			desc: "completed txn",
			txnDetail: &storage.CardTransactionDetail{
				Status:         constants.CompletedStatus,
				ValueTimestamp: time.Date(2023, time.June, 26, 10, 30, 0, 30, time.UTC),
			},
			expected: lo.ToPtr(time.Date(2023, time.June, 26, 10, 30, 0, 0, time.UTC)),
		},
		{
			desc: "card issuance fee",
			txnDetail: &storage.CardTransactionDetail{
				TransactionType: constants.NewCardIssuanceFeeTransactionType,
			},
			expected: nil,
		},
		{
			desc: "card issuance fee waiver",
			txnDetail: &storage.CardTransactionDetail{
				TransactionType: constants.NewCardIssuanceFeeWaiverTransactionType,
			},
			expected: nil,
		},
		{
			desc: "card replacement fee",
			txnDetail: &storage.CardTransactionDetail{
				TransactionType: constants.CardReplacementFeeTransactionType,
			},
			expected: nil,
		},
		{
			desc: "card replacement fee waiver",
			txnDetail: &storage.CardTransactionDetail{
				TransactionType: constants.CardReplacementFeeWaiverTransactionType,
			},
			expected: nil,
		},
		{
			desc: "card renewal fee",
			txnDetail: &storage.CardTransactionDetail{
				TransactionType: constants.CardAnnualFeeTransactionType,
			},
			expected: nil,
		},
		{
			desc: "card renewal fee waiver",
			txnDetail: &storage.CardTransactionDetail{
				TransactionType: constants.CardAnnualFeeWaiverTransactionType,
			},
			expected: nil,
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			date := GetSettlementDate(scenario.txnDetail)
			assert.Equal(t, scenario.expected, date)
		})
	}
}

func TestGetExchangeRate(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc           string
		txnDetail      *storage.CardTransactionDetail
		transaction    *storage.TransactionsData
		expectedResult string
	}{
		{
			desc: "convert FX txn exchange fee",
			txnDetail: &storage.CardTransactionDetail{
				OriginalCurrency: "SGD",
				Currency:         "MYR",
				Metadata:         json.RawMessage("{\"networkID\": \"MCN\", \"currencyConvType\": \"FX\", \"mcConversionRate\":69972522}"),
			},
			transaction:    &storage.TransactionsData{TransactionType: constants.SpendCardAtmTransactionType, TransactionCurrency: "MYR"},
			expectedResult: "SGD1.00 = MYR9.972522",
		},
		{
			desc: "convert for LOCAL txn",
			txnDetail: &storage.CardTransactionDetail{
				OriginalCurrency: "SGD",
				Currency:         "MYR",
				Metadata:         json.RawMessage("{\"networkID\": \"MCN\", \"currencyConvType\": \"LOCAL\", \"mcConversionRate\":69972522}"),
			},
			transaction:    &storage.TransactionsData{TransactionType: constants.SpendCardAtmTransactionType, TransactionCurrency: "MYR"},
			expectedResult: "",
		},
		{
			desc: "convert for DCC txn",
			txnDetail: &storage.CardTransactionDetail{
				OriginalCurrency: "SGD",
				Currency:         "MYR",
				Metadata:         json.RawMessage("{\"networkID\": \"MCN\", \"currencyConvType\": \"DCC\", \"mcConversionRate\":61000000}"),
			},
			transaction:    &storage.TransactionsData{TransactionType: constants.SpendCardAtmTransactionType, TransactionCurrency: "MYR"},
			expectedResult: "",
		},
		{
			desc: "convert for FX lower rate ",
			txnDetail: &storage.CardTransactionDetail{
				OriginalCurrency: "SGD",
				Currency:         "MYR",
				Metadata:         json.RawMessage("{\"networkID\": \"MCN\", \"currencyConvType\": \"FX\", \"mcConversionRate\":71000000}"),
			},
			transaction:    &storage.TransactionsData{TransactionType: constants.SpendCardAtmTransactionType, TransactionCurrency: "MYR"},
			expectedResult: "SGD1.00 = MYR0.1",
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			result := GetExchangeRate(context.Background(), scenario.transaction, scenario.txnDetail)
			assert.Equal(t, scenario.expectedResult, result)
		})
	}
}

func TestParseConversionRate(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc           string
		input          int64
		expectedResult decimal.Decimal
		expectedError  error
	}{
		{
			desc:           "parse ConversionRate correctly",
			input:          61040000,
			expectedResult: decimal.NewFromFloat(1.040000).Round(6),
			expectedError:  nil,
		},
		{
			desc:           "parse ConversionRate correctly",
			input:          79972522,
			expectedResult: decimal.NewFromFloat(0.9972522).Round(6),
			expectedError:  nil,
		},
		{
			desc:           "parse ConversionRate correctly",
			input:          43372522,
			expectedResult: decimal.NewFromFloat(337.2522).Round(6),
			expectedError:  nil,
		},
		{
			desc:           "parse invalid ConversionRate when numOfFractionalPart > 7",
			input:          83372522,
			expectedResult: decimal.Zero,
			expectedError:  errInvalidConversionRate,
		},
		{
			desc:           "parse invalid ConversionRate when its length > 8",
			input:          733725226789,
			expectedResult: decimal.Zero,
			expectedError:  errInvalidConversionRate,
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			result, err := parseConversionRate(context.Background(), scenario.input)
			assert.Equal(t, scenario.expectedResult, result)
			assert.Equal(t, scenario.expectedError, err)
		})
	}
}

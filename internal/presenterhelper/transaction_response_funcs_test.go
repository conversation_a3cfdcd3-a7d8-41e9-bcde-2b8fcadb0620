package presenterhelper

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"github.com/enescakir/emoji"
	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

func TestGetDBMYCardDebitTransactionDescription(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc        string
		cardTxn     *storage.CardTransactionDetail
		displayName string
		expected    string
	}{
		{
			desc: "SpendCardPresentTransactionType",
			cardTxn: &storage.CardTransactionDetail{
				TransactionType: constants.SpendCardPresentTransactionType,
			},
			displayName: "test merchant",
			expected:    fmt.Sprintf("%s %s", localise.Translate(constants.DebitCardPaymentTo), "test merchant"),
		},
		{
			desc: "SpendCardAtmTransactionType",
			cardTxn: &storage.CardTransactionDetail{
				TransactionType: constants.SpendCardAtmTransactionType,
			},
			displayName: "ATM",
			expected:    "ATM withdrawal",
		},
		{
			desc: "DomesticAtmFeeTransactionType",
			cardTxn: &storage.CardTransactionDetail{
				TransactionType: constants.DomesticAtmFeeTransactionType,
			},
			expected: localise.Translate(constants.DebitCardAtmFeeCharged),
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			txnDesc, _ := getDBMYCardDebitTransactionDescription(context.Background(), scenario.cardTxn, scenario.displayName)
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

func TestGetDBMYCardCreditTransactionDescription(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc        string
		cardTxn     *storage.CardTransactionDetail
		displayName string
		expected    string
	}{
		{
			desc: "SpendRefundTransactionType",
			cardTxn: &storage.CardTransactionDetail{
				TransactionType: constants.SpendRefundTransactionType,
			},
			displayName: "test merchant",
			expected:    fmt.Sprintf("%s %s", localise.Translate(constants.DebitCardRefundPaymentFrom), "test merchant"),
		},
		{
			desc: "MoneySendTransactionType",
			cardTxn: &storage.CardTransactionDetail{
				TransactionType: constants.MoneySendTransactionType,
			},
			displayName: "test merchant 2",
			expected:    fmt.Sprintf("%s %s", localise.Translate(constants.DebitCardMoneySendReceive), "test merchant 2"),
		},
		{
			desc: "ATMCashWithdrawalRefundTransactionType",
			cardTxn: &storage.CardTransactionDetail{
				TransactionType: constants.ATMCashWithdrawalRefundTransactionType,
			},
			expected: "Reversal from",
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			txnDesc, _ := getDBMYCardCreditTransactionDescription(context.Background(), scenario.cardTxn, scenario.displayName)
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

func TestGetDBMYCardOpsTransactionDescription(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc        string
		txnData     *storage.TransactionsData
		displayName string
		expected    string
	}{
		{
			desc: "ManualDebitReconTransactionType",
			txnData: &storage.TransactionsData{
				TransactionType: constants.ManualDebitReconTransactionType,
			},
			displayName: "Bank adjustment",
			expected:    "Bank adjustment applied",
		},
		{
			desc: "ManualCreditReconTransactionType",
			txnData: &storage.TransactionsData{
				TransactionType: constants.ManualCreditReconTransactionType,
			},
			displayName: "Bank adjustment",
			expected:    "Bank adjustment applied",
		},
		{
			desc: "DisputeChargeBackTransactionType",
			txnData: &storage.TransactionsData{
				TransactionType: constants.DisputeChargeBackTransactionType,
			},
			displayName: "Bank refund",
			expected:    "Bank refund",
		},
		{
			desc: "DisputeBankRefundTransactionType",
			txnData: &storage.TransactionsData{
				TransactionType: constants.DisputeBankRefundTransactionType,
			},
			displayName: "Bank refund",
			expected:    "Bank refund",
		},
		{
			desc: "DisputeBankFraudRefundTransactionType",
			txnData: &storage.TransactionsData{
				TransactionType: constants.DisputeBankFraudRefundTransactionType,
			},
			displayName: "Bank refund",
			expected:    "Bank refund",
		},
		{
			desc: "ProvisionalCreditTransactionType",
			txnData: &storage.TransactionsData{
				TransactionType: constants.ProvisionalCreditTransactionType,
			},
			displayName: "Provisional credit",
			expected:    "Provisional credit",
		},
		{
			desc: "ProvisionalCreditReversalTransactionType",
			txnData: &storage.TransactionsData{
				TransactionType: constants.ProvisionalCreditReversalTransactionType,
			},
			displayName: "Provisional credit",
			expected:    "Provisional credit reversal",
		},
		{
			desc: "OperationalLossTransactionType",
			txnData: &storage.TransactionsData{
				TransactionType: constants.OperationalLossTransactionType,
			},
			displayName: "Bank refund",
			expected:    "Bank refund",
		},
		{
			desc: "ApplyEarmarkTransactionType",
			txnData: &storage.TransactionsData{
				TransactionType: constants.ApplyEarmarkTransactionType,
			},
			displayName: "On hold amount",
			expected:    "On hold amount",
		},
		{
			desc: "ReleaseEarmarkTransactionType",
			txnData: &storage.TransactionsData{
				TransactionType: constants.ReleaseEarmarkTransactionType,
			},
			displayName: "On hold amount",
			expected:    "On hold amount",
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			txnDesc, _ := getDBMYOtherTransactionDetail(context.Background(), scenario.txnData, scenario.displayName)
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

func TestGetDBMYLoanTransactionDescription(t *testing.T) {
	// setup localise initialization
	_ = os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc        string
		txnData     *storage.TransactionsData
		displayName string
		expected    string
	}{
		{
			desc: "Drawdown",
			txnData: &storage.TransactionsData{
				TransactionType: constants.DrawdownTransactionType,
			},
			displayName: "",
			expected:    constants.FlexiCreditDisplayName,
		},
		{
			desc: "Repayment",
			txnData: &storage.TransactionsData{
				TransactionType: constants.RepaymentTransactionType,
			},
			displayName: "",
			expected:    constants.FlexiCreditRepaymentDisplayName,
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			txnDesc, _ := getDBMYLoanTransactionDescription(context.Background(), scenario.txnData, scenario.displayName)
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

func TestGetBizLendingTransactionDescription(t *testing.T) {
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc        string
		txnData     *storage.TransactionsData
		displayName string
		expected    string
	}{
		{
			desc: "Drawdown",
			txnData: &storage.TransactionsData{
				TransactionType: constants.DrawdownTransactionType,
			},
			displayName: "",
			expected:    constants.BizFlexiLoanDisplayName,
		},
		{
			desc: "Repayment",
			txnData: &storage.TransactionsData{
				TransactionType: constants.RepaymentTransactionType,
			},
			displayName: "",
			expected:    constants.BizFlexiLoanRepaymentDisplayName,
		},
		{
			desc: "WriteOff",
			txnData: &storage.TransactionsData{
				TransactionType: constants.WriteOffTransactionType,
			},
			displayName: "",
			expected:    constants.BizFlexiLoanRepaymentDisplayName,
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			txnDesc, _ := getBizLendingTransactionDescription(context.Background(), scenario.txnData, scenario.displayName)
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

func TestGetInterestDescription(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc        string
		txnData     *storage.TransactionsData
		displayName string
		expected    string
	}{
		{
			desc: "NormalInterest",
			txnData: &storage.TransactionsData{
				TransactionDetails: nil,
			},
			displayName: "",
			expected:    fmt.Sprintf("%s %s", "Interest earned", emoji.PartyPopper),
		},
		{
			desc: "Campaign Interest",
			txnData: &storage.TransactionsData{
				TransactionDetails: json.RawMessage("{\"campaignName\": \"CNY interest earned\"}"),
			},
			displayName: "",
			expected:    fmt.Sprintf("%s %s", "CNY interest earned", emoji.RedEnvelope),
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			txnDesc, _ := getInterestDescription(context.Background(), scenario.txnData, scenario.displayName)
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

func TestGetDBMYQrTransactionDescription(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc        string
		paymentTxn  *storage.PaymentDetail
		displayName string
		expected    string
	}{
		{
			desc: "Qr Payment",
			paymentTxn: &storage.PaymentDetail{
				TransactionType: constants.QrPaymentTxType,
				Amount:          -100,
			},
			displayName: "test merchant",
			expected:    fmt.Sprintf("%s %s %s", localise.Translate(constants.QrPayment), "to", "test merchant"),
		},
		{
			desc: "Qr Payment Reversal",
			paymentTxn: &storage.PaymentDetail{
				TransactionType: constants.QrPaymentReversalTxType,
				Amount:          100,
			},
			displayName: "test merchant",
			expected:    fmt.Sprintf("%s %s %s", localise.Translate(constants.QrPaymentReversal), "from", "test merchant"),
		},
		{
			desc: "Qr Transfer - interbank - outgoing fund",
			paymentTxn: &storage.PaymentDetail{
				TransactionType: constants.SendMoneyTxType,
				Amount:          -100,
				Metadata:        []byte(`{"remarks": "", "transferType": "", "serviceType": "QR_TRANSFER"}`),
			},
			displayName: "Georgia",
			expected:    fmt.Sprintf("%s %s %s", localise.Translate(constants.QrTransfer), "to", "Georgia"),
		},
		{
			desc: "Qr Transfer - interbank - incoming fund",
			paymentTxn: &storage.PaymentDetail{
				TransactionType: constants.ReceiveMoneyTxType,
				Amount:          100,
				Metadata:        []byte(`{"remarks": "", "transferType": "", "serviceType": "QR_TRANSFER"}`),
			},
			displayName: "Georgia",
			expected:    fmt.Sprintf("%s %s %s", localise.Translate(constants.QrTransfer), "from", "Georgia"),
		},
		{
			desc: "Qr Transfer - intrabank - outgoing fund",
			paymentTxn: &storage.PaymentDetail{
				TransactionType: constants.TransferMoneyTxType,
				Amount:          -100,
				Metadata:        []byte(`{"remarks": "", "transferType": "", "serviceType": "QR_TRANSFER"}`),
			},
			displayName: "Georgia",
			expected:    fmt.Sprintf("%s %s %s", localise.Translate(constants.QrTransfer), "to", "Georgia"),
		},
		{
			desc: "Qr Transfer - intrabank - incoming fund",
			paymentTxn: &storage.PaymentDetail{
				TransactionType: constants.TransferMoneyTxType,
				Amount:          100,
				Metadata:        []byte(`{"remarks": "", "transferType": "", "serviceType": "QR_TRANSFER"}`),
			},
			displayName: "Georgia",
			expected:    fmt.Sprintf("%s %s %s", localise.Translate(constants.QrTransfer), "from", "Georgia"),
		},
		{
			desc: "Qr Transfer reversal - interbank - incoming fund",
			paymentTxn: &storage.PaymentDetail{
				TransactionType: constants.SendMoneyRevTxType,
				Amount:          100,
				Metadata:        []byte(`{"remarks": "", "transferType": "", "serviceType": "QR_TRANSFER"}`),
			},
			displayName: "Georgia",
			expected:    fmt.Sprintf("%s %s %s", localise.Translate(constants.QrTransferReversal), "from", "Georgia"),
		},
		{
			desc: "Qr Transfer reversal - interbank - outgoing fund",
			paymentTxn: &storage.PaymentDetail{
				TransactionType: constants.ReceiveMoneyRevTxType,
				Amount:          -100,
				Metadata:        []byte(`{"remarks": "", "transferType": "", "serviceType": "QR_TRANSFER"}`),
			},
			displayName: "Georgia",
			expected:    fmt.Sprintf("%s %s %s", localise.Translate(constants.QrTransferReversal), "to", "Georgia"),
		},
		{
			desc: "Qr Transfer reversal - intrabank - outgoing fund",
			paymentTxn: &storage.PaymentDetail{
				TransactionType: constants.TransferMoneyRevTxType,
				Amount:          -100,
				Metadata:        []byte(`{"remarks": "", "transferType": "", "serviceType": "QR_TRANSFER"}`),
			},
			displayName: "Georgia",
			expected:    fmt.Sprintf("%s %s %s", localise.Translate(constants.QrTransferReversal), "to", "Georgia"),
		},
		{
			desc: "Qr Transfer - intrabank - incoming fund",
			paymentTxn: &storage.PaymentDetail{
				TransactionType: constants.TransferMoneyRevTxType,
				Amount:          100,
				Metadata:        []byte(`{"remarks": "", "transferType": "", "serviceType": "QR_TRANSFER"}`),
			},
			displayName: "Georgia",
			expected:    fmt.Sprintf("%s %s %s", localise.Translate(constants.QrTransferReversal), "from", "Georgia"),
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			ctx := context.Background()
			txnDesc, _ := getQrTransactionDetail(ctx, scenario.paymentTxn, scenario.displayName)
			switch scenario.paymentTxn.TransactionType {
			case constants.SendMoneyTxType, constants.ReceiveMoneyTxType:
				txnDesc, _ = GetRPPTransactionDetail(ctx, scenario.paymentTxn, scenario.displayName)
			case constants.TransferMoneyTxType:
				txnDesc, _ = getDBMYIntraBankTransactionDetail(ctx, scenario.paymentTxn, scenario.displayName)
			case constants.SendMoneyRevTxType:
				txnDesc, _ = getSendMoneyRevTransactionDetail(ctx, scenario.paymentTxn, scenario.displayName)
			case constants.ReceiveMoneyRevTxType:
				txnDesc, _ = getReceiveMoneyRevTransactionDetail(ctx, scenario.paymentTxn, scenario.displayName)
			case constants.TransferMoneyRevTxType:
				txnDesc, _ = getTransferMoneyRevTransactionDetail(ctx, scenario.paymentTxn, scenario.displayName)
			}
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

func TestGetFundInRevTransactionDetail(t *testing.T) {
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc         string
		dbData       interface{}
		counterParty string
		expected     string
	}{
		{
			desc: "happy-path",
			dbData: &storage.PaymentDetail{
				TransactionType: constants.FundInRevTxType,
				Amount:          -100,
			},
			counterParty: "test merchant",
			expected:     fmt.Sprintf("%s %s %s", localise.Translate(constants.RPPNormalTransferRev), "to", "test merchant"),
		},
		{
			desc: "error",
			dbData: &storage.TransactionsData{
				TransactionType: constants.FundInRevTxType,
			},
			counterParty: "test merchant",
			expected:     "",
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			ctx := context.Background()
			txnDesc, _ := getFundInRevTransactionDetail(ctx, scenario.dbData, scenario.counterParty)
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

func TestGrabTransactionDetail(t *testing.T) {
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc         string
		dbData       interface{}
		counterParty string
		expected     string
	}{
		{
			desc: "payment",
			dbData: &storage.PaymentDetail{
				TransactionType: constants.SpendMoneyTxType,
				Amount:          -100,
			},
			counterParty: "test merchant",
			expected:     fmt.Sprintf("%s %s %s", localise.Translate(constants.Payment), "to", "test merchant"),
		},
		{
			desc: "refund",
			dbData: &storage.PaymentDetail{
				TransactionType: constants.SpendMoneyRevTxType,
				Amount:          100,
			},
			counterParty: "test merchant",
			expected:     fmt.Sprintf("%s %s %s", localise.Translate(constants.Refund), "from", "test merchant"),
		}, {
			desc: "error",
			dbData: &storage.TransactionsData{
				TransactionType: constants.SpendMoneyRevTxType,
			},
			counterParty: "test merchant",
			expected:     "",
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			ctx := context.Background()
			txnDesc, _ := getGrabTransactionDetail(ctx, scenario.dbData, scenario.counterParty)
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

func TestGetRPPProxyType(t *testing.T) {
	scenarios := []struct {
		desc          string
		accountDetail *dto.AccountDetail
		expected      string
	}{
		{
			desc: "mobile",
			accountDetail: &dto.AccountDetail{
				Proxy: dto.ProxyObject{
					Type: constants.MobileNumberRail,
				},
			},
			expected: "Mobile number",
		},
		{
			desc: "NRIC",
			accountDetail: &dto.AccountDetail{
				Proxy: dto.ProxyObject{
					Type: constants.NRICRail,
				},
			},
			expected: "MyKad",
		},
		{
			desc: "Army",
			accountDetail: &dto.AccountDetail{
				Proxy: dto.ProxyObject{
					Type: constants.ArmyIDRail,
				},
			},
			expected: "Army/Police ID",
		},
		{
			desc: "Passport",
			accountDetail: &dto.AccountDetail{
				Proxy: dto.ProxyObject{
					Type: constants.PassportNumberRail,
				},
			},
			expected: "Passport",
		},
		{
			desc: "Business",
			accountDetail: &dto.AccountDetail{
				Proxy: dto.ProxyObject{
					Type: constants.BusinessRegistrationNumberRail,
				},
			},
			expected: "Business Registration",
		},
		{
			desc: "default",
			accountDetail: &dto.AccountDetail{
				Proxy: dto.ProxyObject{
					Type: "test",
				},
			},
			expected: "Account number",
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			txnDesc := getRPPProxyType(scenario.accountDetail)
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

func Test_getDBMYOtherTransactionDetail(t *testing.T) {
	// setup localise initialization
	os.Setenv("LOCALISATION_PATH", "../../localise")
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)
	scenarios := []struct {
		desc            string
		transactionData *storage.TransactionsData
		expected        string
	}{
		{
			desc: "unclaimed monies transfer",
			transactionData: &storage.TransactionsData{
				TransactionType: constants.UnclaimedMoniesTransactionType,
			},
			expected: fmt.Sprintf("%s %s %s", "Transfer", "to", "GX Unclaimed Balance Account"),
		},
	}

	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			ctx := context.Background()
			txnDesc, _ := getDBMYOtherTransactionDetail(ctx, scenario.transactionData, "")
			assert.Equal(t, scenario.expected, txnDesc)
		})
	}
}

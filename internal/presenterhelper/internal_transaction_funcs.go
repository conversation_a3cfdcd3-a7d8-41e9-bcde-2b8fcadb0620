package presenterhelper

import (
	"fmt"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
)

// InternalTransactionFuncTemplate ...
// string formatter for internal transaction
// eg: Fund Moved from Interest Earned
// str[1] localise eg: from/to
// str[0] localise eg: Interest Earned/Tax on Interest
type InternalTransactionFuncTemplate func(str ...string) string

// GetPocketFundingTransactionDetail ...
func GetPocketFundingTransactionDetail(str ...string) string {
	response := fmt.Sprintf("%s %s %s", localise.Translate(constants.FundMoved), str[1], str[0])
	return response
}

// GetPocketWithdrawalTransactionDetail ...
func GetPocketWithdrawalTransactionDetail(str ...string) string {
	response := fmt.Sprintf("%s %s %s", localise.Translate(constants.WithdrawalDesc), str[1], str[0])
	return response
}

// GetOPSTransactionDetail ...
func GetOPSTransactionDetail(...string) string {
	return localise.Translate(constants.TransactionAdjustment)
}

// GetInterestOrTaxPayoutTransactionDetail ...
func GetInterestOrTaxPayoutTransactionDetail(str ...string) string {
	return str[2]
}

package presenterhelper

import (
	"context"
	"errors"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// PaymentsTransactionFuncTemplate ...
type PaymentsTransactionFuncTemplate func(tx context.Context, dbData interface{}, counterParty string) (string, error)

// GetIntraBankTransactionDetail ...
func GetIntraBankTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var desc string
	paymentData := dbData.(*storage.PaymentDetail)
	desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.BankTransfer), getPaymentDirection(paymentData.Amount), counterParty)
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Intrabank Transaction description is completed")
	return desc, nil
}

// GetRtolTransactionDetail ...
func GetRtolTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var desc string
	paymentData := dbData.(*storage.PaymentDetail)
	desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.OnlineTransfer), getPaymentDirection(paymentData.Amount), counterParty)
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Rtol Transaction description is completed")
	return desc, nil
}

// GetRPPTransactionDetail ...
func GetRPPTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var transferTerm string
	paymentData, ok := dbData.(*storage.PaymentDetail)
	if !ok {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Error getting transaction description for RPP transaction")
		return "", errors.New("error getting transaction description")
	}
	transferTerm = localise.Translate(constants.RPPNormalTransfer)
	switch paymentData.TransactionType {
	// will there be a case for FundIn and QR at the same time ?
	case constants.FundIn:
	default:
		if paymentData.IsQrTxn(ctx) {
			transferTerm = localise.Translate(constants.QrTransfer)
		}
	}
	desc := fmt.Sprintf("%s %s %s", transferTerm, getPaymentDirection(paymentData.Amount), counterParty)
	slog.FromContext(ctx).Debug(constants.GetTransactionDetailLogTag, "RPP Transaction description is completed")
	return desc, nil
}

package consumers

import (
	"context"
	"testing"

	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/snd/streamsdk/kafka"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"

	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/servus/v2/inject"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"

	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"

	"github.com/stretchr/testify/assert"
	paymentExperienceMock "gitlab.myteksi.net/dakota/payment/payment-experience/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
)

func TestShutdownKafkaReader(t *testing.T) {
	mockKafkaReader := &kafkareader.MockClient{}
	mockKafkaReader.On("Shutdown").Return(nil)
	peConsumer = mockKafkaReader
	dcConsumer = mockKafkaReader
	dbConsumer = mockKafkaReader
	interestAggConsumer = mockKafkaReader
	digicardTxConsumer = mockKafkaReader
	conf := &config.AppConfig{
		LoanCoreTxKafka:  &config.KafkaConfig{},
		LoanExpTxKafka:   &config.KafkaConfig{},
		InterestAggKafka: &config.KafkaConfig{},
	}

	assert.NotPanics(t, func() {
		Stop(conf)
	})
}

func TestInit(t *testing.T) {
	mockRedis := &redisMock.Client{}
	mockKafkaReader := &kafkareader.MockClient{}
	mockFeatureFlag := &featureflag.MockRepo{}
	mockFeatureFlag.On("IsRetryableDepositCoreStreamEnabled").Return(false)
	mockFeatureFlag.On("IsRetryableDepositBalanceStreamEnabled").Return(false)
	mockFeatureFlag.On("IsDepositCoreStreamEnabled").Return(false)
	mockFeatureFlag.On("IsDepositBalanceStreamEnabled").Return(false)
	mockFeatureFlag.On("IsInsertTransactionsByBatchEnabled").Return(true)
	mockFeatureFlag.On("IsRetryableInterestAggStreamEnabled").Return(false)
	mockKafkaReader.On("GetDataChan").Return(nil)
	streamStaticReader = func(ctx context.Context, streamID streams.StreamID, conf sndconfig.KafkaConfig, entity kafka.Entity, opts ...kafkareader.Option) (kafkareader.Client, error) {
		return mockKafkaReader, nil
	}
	app := &servus.Application{}
	app.Container = inject.New()
	app.MustRegister("featureFlags", mockFeatureFlag)
	conf := &config.AppConfig{
		PaymentsEngineKafka:  &config.KafkaConfig{},
		DepositsBalanceKafka: &config.KafkaConfig{},
		DepositsCoreKafka:    &config.KafkaConfig{},
		DigicardTxnKafka:     &config.KafkaConfig{},
		LoanCoreTxKafka:      &config.KafkaConfig{},
		LoanExpTxKafka:       &config.KafkaConfig{},
		InterestAggKafka:     &config.KafkaConfig{},
	}
	paymentExp := &paymentExperienceMock.PaymentExperience{}
	assert.NotPanics(t, func() {
		Init(app, conf, StatsDClient, paymentExp, mockRedis)
	})
}

package consumers // nolint:dupl

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"

	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/internal/metrics"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/deposits"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

const (
	depositsCoreDTO = "DepositsCoreTx"
)

var (
	dcConsumer kafkareader.Client
	dcTopic    string
)

// startConsumeDepositsCoreStream creates Kafka Read and Register the method responsible to consumer
func startConsumeDepositsCoreStream(conf *config.KafkaConfig, featureFlags featureflag.Repo) {
	reader, err := streamStaticReader(context.Background(), DepositsCoreStream,
		convertConfig(conf, depositsCoreDTO), &deposits_core_tx.DepositsCoreTx{}, func(o *kafkareader.StreamConfig) *kafkareader.StreamConfig {
			o.Logger = &streamLogger{slogger: logger}
			return o
		})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", conf, err))
	}

	dcConsumer = reader
	dcTopic = conf.TopicName
	registerDepositsCoreHandler(constants.DepositsCoreTag, dcConsumer.GetDataChan(), featureFlags)
}

var registerDepositsCoreHandler = func(tag string, ch <-chan *kafkareader.Entity, featureflags featureflag.Repo) {
	ctx := slog.NewContextWithLogger(context.Background(), logger)
	ctx = featureflag.NewContextWithFeatureFlags(ctx, featureflags)
	wg.Go(tag, func() {
		consumeDepositsCoreStream(ctx, ch)
	})
}

// consumeDepositsCoreStream receives event from stream and pass it to processing methods
// deprecated: use retryable stream instead
func consumeDepositsCoreStream(ctx context.Context, ch <-chan *kafkareader.Entity) {
	for event := range ch {
		data, ok := event.Event.(*deposits_core_tx.DepositsCoreTx)
		logTags := append(commonTags(dcTopic, depositsCoreDTO), eventMetaTags(event)...)
		eventCtx := slog.AddTagsToContext(ctx, logTags...)
		if !ok {
			StatsDClient.Count1(string(servicename.TransactionHistory), metrics.DepositsCoreKafkaMessage,
				metrics.FailedTag,
				"errorOccured:wrong_entity")
			slog.FromContext(eventCtx).Fatal(constants.DepositsCoreTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", event.Event),
				commonTags(dcTopic, depositsCoreDTO)...)
			continue
		}
		eventCtx = slog.AddTagsToContext(eventCtx, slog.CustomTag("tm_batch_id", data.ID))

		slog.FromContext(eventCtx).Info(constants.DepositsCoreTag, fmt.Sprintf("%+v", data))
		err := deposits.HandleDepositsCoreStream(eventCtx, data, StatsDClient, storage.DB)
		if err != nil {
			slog.FromContext(eventCtx).Warn(constants.DepositsCoreTag, fmt.Sprintf("handling stream event failed, %s", err.Error()),
				commonTags(dcTopic, depositsCoreDTO)...)
		}
	}
}

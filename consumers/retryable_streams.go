package consumers

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"

	retryablestream "gitlab.myteksi.net/dakota/common/retryable-stream"
	rshandler "gitlab.myteksi.net/dakota/common/retryable-stream/handler"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	servusStatsD "gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
)

var (
	retryableStream                        *retryablestream.RetryableStream
	isRetryableDepositCoreStreamEnabled    bool
	isRetryableDepositBalanceStreamEnabled bool
	isRetryableInterestAggStreamEnabled    bool
	retryableRegisters                     = make(map[string]func(context.Context, *config.AppConfig, *retryablestream.RetryableStream))
)

// startConsumeRetryableStream registers handlers and starts to consume messages with RetryableStream
// Only call this function after all the handlers registration is done
func startConsumeRetryableStream(ctx context.Context, conf *config.AppConfig, statsDClient servusStatsD.Client) {
	if len(retryableRegisters) == 0 {
		return
	}
	stream, err := retryablestream.NewRetryableStream(ctx, conf.SQSConfig, statsDClient)
	retryableStream = stream
	if err != nil {
		panic(fmt.Sprintf("failed to create new RetryableStream, sqsConfig=[%+v], err=[%+v]", conf.SQSConfig, err))
	}
	for k, register := range retryableRegisters {
		register(ctx, conf, stream)
		slog.FromContext(ctx).Info("startConsumeRetryableStream", fmt.Sprintf("Start consuming retryable %s", k))
	}
	retryableStream.StartConsuming(ctx)
}

func registerDepositCoreRetryableHandler(ctx context.Context) {
	featFlag := featureflag.FeatureFlagsFromContext(ctx)
	retryableRegisters[depositsCoreDTO] = func(ctx context.Context, conf *config.AppConfig, stream *retryablestream.RetryableStream) {
		registerConsumerHandler(ctx, depositsCoreDTO, stream, *conf.DepositsCoreKafka, DepositsCoreHandler{
			featFlags: featFlag,
		})
	}
}

func registerDepositBalanceRetryableHandler(ctx context.Context) {
	featFlag := featureflag.FeatureFlagsFromContext(ctx)
	retryableRegisters[depositsBalanceDTO] = func(ctx context.Context, conf *config.AppConfig, stream *retryablestream.RetryableStream) {
		registerConsumerHandler(ctx, depositsBalanceDTO, stream, *conf.DepositsBalanceKafka, DepositsBalanceHandler{
			historyConfig: conf.TransactionHistoryServiceConfig, featFlags: featFlag})
	}
}

func registerInterestAggRetryableHandler(ctx context.Context) {
	featFlag := featureflag.FeatureFlagsFromContext(ctx)
	retryableRegisters[InterestAggStreamDTO] = func(ctx context.Context, conf *config.AppConfig, stream *retryablestream.RetryableStream) {
		registerConsumerHandler(ctx, InterestAggStreamDTO, stream, *conf.InterestAggKafka, InterestAggregateHandler{
			featFlags: featFlag,
		})
	}
}

func registerConsumerHandler(ctx context.Context, dtoName string, stream *retryablestream.RetryableStream, kafkaConfig config.KafkaConfig, handler rshandler.Handler) {
	sndKafkaConfig := convertConfig(&kafkaConfig, dtoName)

	err := stream.RegisterKafkaConsumer(ctx, sndKafkaConfig, handler, func(o *kafkareader.StreamConfig) *kafkareader.StreamConfig {
		o.Logger = &streamLogger{slogger: logger}
		return o
	})
	if err != nil {
		panic(fmt.Sprintf("failed to register handler for RetryableStream, kafkaConfig=[%+v], err=[%+v]", kafkaConfig, err))
	}
}

// handleErrorForRetryableStream return error if retryable stream is enabled and err is retryable
func handleErrorForRetryableStream(ctx context.Context, err error, enableRetryableStream bool, isRetryable bool, logTag string) error {
	if !enableRetryableStream {
		return err
	}

	if isRetryable {
		return err
	}

	slog.FromContext(ctx).Debug(logTag, "ExecuteNotifyEvent returns non-retryable err, not pushed to SQS for retry", slog.Error(err))
	return nil
}

func stopConsumeRetryableStream(ctx context.Context) {
	if retryableStream == nil {
		return
	}

	err := retryableStream.Stop(ctx)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error shutting down retryable stream", slog.Error(err))
	}
}

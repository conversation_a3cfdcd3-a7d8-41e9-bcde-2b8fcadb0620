package consumers // nolint:dupl

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/loan_core_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/lending"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

const (
	loanCoreTxDTO = "LoanCoreTx"
)

var (
	loanCoreTxConsumer kafkareader.Client
	loanCoreTxTopic    string
)

// startConsumeLoanCoreTxStream creates Kafka Read and Register the method responsible to consumer
func startConsumeLoanCoreTxStream(conf *config.KafkaConfig) {
	reader, err := streams.NewStaticReader(context.Background(), LoanCoreTxStream,
		convertConfig(conf, loanCoreTxDTO), &loan_core_tx.LoanCoreTx{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", conf, err))
	}

	loanCoreTxConsumer = reader
	loanCoreTxTopic = conf.TopicName
	registerLoanCoreTxHandler(constants.LoanCoreTxStreamLogTag, loanCoreTxConsumer.GetDataAckChan())
}

var registerLoanCoreTxHandler = func(tag string, ch <-chan *kafkareader.AckEntity) {
	wg.Go(tag, func() {
		consumeLoanCoreTxStream(context.Background(), ch)
	})
}

// consumeLoanCoreTxStream receives event from stream and pass it to processing methods
func consumeLoanCoreTxStream(ctx context.Context, ch <-chan *kafkareader.AckEntity) {
	for event := range ch {
		data, ok := event.Event.(*loan_core_tx.LoanCoreTx)
		if !ok {
			slog.FromContext(ctx).Fatal(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", event.Event),
				commonTags(loanCoreTxTopic, loanCoreTxDTO)...)
			continue
		}

		slog.FromContext(ctx).Info(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("%+v", data))
		err := lending.HandleLoanCoreTxStream(ctx, data, StatsDClient)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("handling stream event failed, %s", err.Error()),
				commonTags(loanCoreTxTopic, loanCoreTxDTO)...)
		}

		if err = event.Ack(); err != nil {
			slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, "failed to ack message from LoanCoreTxStream", slog.Error(err))
		}
	}
}

package consumers // nolint:dupl

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/common/servicename"
	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api"
	"gitlab.myteksi.net/dakota/transaction-history/internal/metrics"

	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
)

const (
	paymentEngineDTO = "PaymentEngineTx"
)

var (
	peConsumer kafkareader.Client
	peTopic    string
)

// startConsumePaymentEngineStream creates Kafka Read and Register the method responsible to consumer
func startConsumePaymentEngineStream(conf *config.KafkaConfig, client paymentExperience.PaymentExperience) {
	ctx := slog.NewContextWithLogger(context.Background(), logger)
	reader, err := streamStaticReader(ctx, PaymentEngineStream,
		convertConfig(conf, paymentEngineDTO), &payment_engine_tx.PaymentEngineTx{},
		func(o *kafkareader.StreamConfig) *kafkareader.StreamConfig {
			o.Logger = &streamLogger{slogger: logger}
			return o
		})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", conf, err))
	}

	peConsumer = reader
	peTopic = conf.TopicName
	registerPEHandler(constants.PaymentEngineTag, peConsumer.GetDataChan(), client)
}

var registerPEHandler = func(tag string, ch <-chan *kafkareader.Entity, client paymentExperience.PaymentExperience) {
	wg.Go(tag, func() {
		consumePEStream(slog.NewContextWithLogger(context.Background(), logger), ch, client)
	})
}

// consumePEStream receives event from stream and pass it to processing methods
func consumePEStream(ctx context.Context, ch <-chan *kafkareader.Entity, client paymentExperience.PaymentExperience) {
	for event := range ch {
		data, ok := event.Event.(*payment_engine_tx.PaymentEngineTx)
		logTags := append(commonTags(peTopic, paymentEngineDTO), eventMetaTags(event)...)
		eventCtx := slog.AddTagsToContext(ctx, logTags...)
		if !ok {
			StatsDClient.Count1(string(servicename.TransactionHistory), metrics.PaymentEngineKafkaMessage,
				metrics.FailedTag,
				"errorOccured:wrong_entity")
			slog.FromContext(eventCtx).Fatal(constants.PaymentEngineTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", event.Event),
				commonTags(peTopic, paymentEngineDTO)...)
			continue
		}

		slog.FromContext(eventCtx).Info(constants.PaymentEngineTag, fmt.Sprintf("%+v", data), slog.CustomTag("offset",
			event.Meta.Offset), slog.CustomTag("partition", event.Meta.Partition),
		)
		err := payments.HandlePEStream(eventCtx, data, StatsDClient, client)
		if err != nil {
			slog.FromContext(eventCtx).Warn(constants.PaymentEngineTag, fmt.Sprintf("handling stream event failed, %s", err.Error()),
				commonTags(peTopic, paymentEngineDTO)...)
		}
	}
}

package consumers // nolint:dupl

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/loan_exp_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/lending"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

const (
	loanExpTxDTO = "LoanExpTx"
)

var (
	loanExpTxConsumer kafkareader.Client
	loanExpTxTopic    string
)

// startConsumeLoanExpTxStream creates Kafka Read and Register the method responsible to consumer
func startConsumeLoanExpTxStream(conf *config.KafkaConfig) {
	reader, err := streams.NewStaticReader(context.Background(), LoanExpTxStream,
		convertConfig(conf, loanExpTxDTO), &loan_exp_tx.LoanExpTx{})
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", conf, err))
	}

	loanExpTxConsumer = reader
	loanExpTxTopic = conf.TopicName
	registerLoanExpTxHandler(constants.LoanExpTxStreamLogTag, loanExpTxConsumer.GetDataChan())
}

var registerLoanExpTxHandler = func(tag string, ch <-chan *kafkareader.Entity) {
	wg.Go(tag, func() {
		consumeLoanExpTxStream(context.Background(), ch)
	})
}

// nolint:dupl
// consumeLoanExpTxStream receives event from stream and pass it to processing methods
func consumeLoanExpTxStream(ctx context.Context, ch <-chan *kafkareader.Entity) {
	for event := range ch {
		data, ok := event.Event.(*loan_exp_tx.LoanExpTx)
		if !ok {
			slog.FromContext(ctx).Fatal(constants.LoanExpTxStreamLogTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", event.Event),
				commonTags(loanExpTxTopic, loanExpTxDTO)...)
			continue
		}

		slog.FromContext(ctx).Info(constants.LoanExpTxStreamLogTag, fmt.Sprintf("%+v", data))
		err := lending.HandleLoanExpTxStream(ctx, data)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.LoanExpTxStreamLogTag, fmt.Sprintf("handling stream event failed, %s", err.Error()),
				commonTags(loanExpTxTopic, loanExpTxDTO)...)
		}
	}
}

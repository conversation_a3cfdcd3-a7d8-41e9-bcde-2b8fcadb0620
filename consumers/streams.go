package consumers

import (
	"context"
	"fmt"
	"time"

	"gitlab.myteksi.net/gophers/go/staples/statsd/statsdapi"
	kafkastreaminfo "gitlab.myteksi.net/snd/streamsdk/kafka/core/streaminfo"

	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"

	"gitlab.myteksi.net/dakota/servus/v2"

	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api"

	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"

	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
)

const (
	streamDefaultStopTimeout = 8 * time.Second
	topicTag                 = "topic"
	logTag                   = "stream.consumers"
	dtoNameTag               = "dtoName"
	offsetTag                = "offset"
	partitionTag             = "partition"
)

var (
	wg = gconcurrent.NewExecutionGroup()
	// StatsDClient ...
	StatsDClient statsd.Client = statsd.NewNoop()
	// initialise with default logger for null safety
	logger = slog.FallbackLogger()

	streamStaticReader = streams.NewStaticReader
	redisClient        redis.Client
	interestAggConfig  config.InterestAggregationConfig
)

// Config ...
type Config struct {
	depositBalanceKafkaConf *config.KafkaConfig
	historyConfig           config.TransactionHistoryServiceConfig
}

// Init initializes stream consumers.
// nolint
func Init(app *servus.Application, appConfig *config.AppConfig, stats statsd.Client, client paymentExperience.PaymentExperience, redisCli redis.Client) {
	redisClient = redisCli
	consumerConf := Config{
		depositBalanceKafkaConf: appConfig.DepositsBalanceKafka,
		historyConfig:           appConfig.TransactionHistoryServiceConfig,
	}
	logger = app.GetLogger()
	ctx := slog.NewContextWithLogger(context.Background(), logger)
	if stats != nil {
		StatsDClient = stats
	}
	// todo: upstream servus statsd should implement the full statsdapi.Client interface
	// it doesnt make sense to implement Grab statsd interface partially while hoping to use the common libraries from Grab
	if gostatsd, ok := StatsDClient.(statsdapi.Client); ok {
		slog.FromContext(ctx).Debug("consumer.Init", "Configure go concurrent statsd client")
		gconcurrent.Instrument(gostatsd)
	}
	featureFlagsDep, err := app.Dependency("featureFlags")
	if err != nil {
		panic(fmt.Sprintf("failed to get [featureFlags] dependency: %v", err))
	}
	featureFlags, ok := featureFlagsDep.(featureflag.Repo)
	if !ok {
		panic(fmt.Sprintf("failed to cast %T [featureFlags] dependency", featureFlagsDep))
	}
	ctx = featureflag.NewContextWithFeatureFlags(ctx, featureFlags)

	if appConfig.PaymentsEngineKafka.Enable {
		slog.FromContext(ctx).Info(logTag, "PaymentsEngine Stream is enabled")
		slog.FromContext(ctx).Info(logTag, "starting to consume PaymentsEngine Stream")
		startConsumePaymentEngineStream(appConfig.PaymentsEngineKafka, client)
	} else {
		slog.FromContext(ctx).Info(logTag, "PaymentsEngine Stream is disabled")
	}

	if featureFlags.IsDepositCoreStreamEnabled() {
		if featureFlags.IsRetryableDepositCoreStreamEnabled() {
			slog.FromContext(ctx).Info(logTag, "register Retryable Deposits Core Stream")
			registerDepositCoreRetryableHandler(ctx)
		} else {
			slog.FromContext(ctx).Info(logTag, "starting to consume Deposits Core Stream")
			startConsumeDepositsCoreStream(appConfig.DepositsCoreKafka, featureFlags)
		}
	}
	if featureFlags.IsDepositBalanceStreamEnabled() {
		if featureFlags.IsRetryableDepositBalanceStreamEnabled() {
			slog.FromContext(ctx).Info(logTag, "register Retryable Deposits Balance Stream")
			registerDepositBalanceRetryableHandler(ctx)
		} else {
			slog.FromContext(ctx).Info(logTag, "starting to consume Deposits Balance Stream")
			startConsumeDepositsBalanceStream(consumerConf)
		}
	}
	if appConfig.DigicardFeatureFlag.Enabled {
		slog.FromContext(ctx).Info(logTag, "starting to consume Digicard Txn Stream")
		startDigicardTxnStream(appConfig.DigicardTxnKafka)
	}
	startConsumeRetryableStream(ctx, appConfig, app.GetStatsD())
	if appConfig.LoanCoreTxKafka.Enable {
		slog.FromContext(ctx).Info(logTag, "starting to consume Loan Core Tx Stream")
		startConsumeLoanCoreTxStream(appConfig.LoanCoreTxKafka)
	}
	if appConfig.LoanExpTxKafka.Enable {
		slog.FromContext(ctx).Info(logTag, "starting to consume Loan Exp Tx Stream")
		startConsumeLoanExpTxStream(appConfig.LoanExpTxKafka)
	}
	if appConfig.InterestAggKafka.Enable {
		if featureFlags.IsRetryableInterestAggStreamEnabled() {
			slog.FromContext(ctx).Info(logTag, "register Retryable Interest Aggregation Stream")
			registerInterestAggRetryableHandler(ctx)
		} else {
			slog.FromContext(ctx).Info(logTag, "starting to consume Interest Aggregation Stream")
			startConsumeInterestAgg(appConfig.InterestAggKafka)
		}
	}
}

// Stop ...
func Stop(conf *config.AppConfig) {
	ctx, timeout := context.WithTimeout(context.Background(), streamDefaultStopTimeout)
	defer timeout()
	stopConsumer(peConsumer) // Stop Payment Engine Consumer
	if dcConsumer != nil {
		stopConsumer(dcConsumer) // Stop Deposits Core Consumer
	}
	if dbConsumer != nil {
		stopConsumer(dbConsumer) // Stop Deposits Balance Consumer
	}
	if conf.DigicardFeatureFlag.Enabled {
		stopConsumer(digicardTxConsumer)
	}
	go stopConsumeRetryableStream(ctx)

	if conf.LoanCoreTxKafka.Enable {
		stopConsumer(loanCoreTxConsumer) // Stop Loan Core Tx Consumer
	}
	if conf.LoanExpTxKafka.Enable {
		stopConsumer(loanExpTxConsumer) // Stop Loan Exp Tx Consumer
	}
	if conf.InterestAggKafka.Enable {
		slog.FromContext(ctx).Info(logTag, "stopping interest aggregation consumer")
		stopConsumer(interestAggConsumer) // Stop Interest Aggregation Consumer
	}

	timeoutErr := wg.WaitForDone(streamDefaultStopTimeout)
	if timeoutErr != nil {
		slog.FromContext(context.Background()).Fatal(logTag, "timeout on stopping streams, it could lead to a data loss")
	}
}

// convertConfig...
func convertConfig(conf *config.KafkaConfig, dtoName string) sndconfig.KafkaConfig {
	return sndconfig.KafkaConfig{
		Brokers:         conf.Brokers,
		ClientID:        conf.ClientID,
		ClusterType:     conf.ClusterType,
		EnableTLS:       conf.EnableTLS,
		Stream:          conf.TopicName,
		OffsetType:      conf.InitOffset,
		ConsumerVersion: sndconfig.ConsumerV2,
		PackageName:     "pb",
		DtoName:         dtoName,
		ConsumerGroupID: conf.ConsumerGroupID,
	}
}

func commonTags(topic, dtoName string) []tags.Tag {
	tagList := []tags.Tag{
		tags.T(topicTag, topic),
		tags.T(dtoNameTag, dtoName),
	}
	return tagList
}

// stopConsumer shutdown the kafkareader Client
func stopConsumer(consumer kafkareader.Client) {
	err := consumer.Shutdown()
	if err != nil {
		slog.FromContext(context.Background()).Error(logTag, fmt.Sprintf("error in stopping kafka consumer client: %v", consumer))
	}
}

func eventMetaTags(event *kafkareader.Entity) []tags.Tag {
	if event == nil {
		return nil
	}
	return []tags.Tag{
		tags.T(offsetTag, event.Meta.Offset),
		tags.T(partitionTag, event.Meta.Partition),
	}
}

func infoMetaTags(entity kafkastreaminfo.Info) []tags.Tag {
	return []tags.Tag{
		tags.T(partitionTag, entity.StreamPartitionID),
	}
}

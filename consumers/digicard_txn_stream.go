package consumers

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/transaction-history/internal/metrics"

	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/digicard_transaction_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/digicard"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
)

const (
	digicardTxDTO = "DigicardTransactionTx"
)

var (
	digicardTxConsumer kafkareader.Client
	digicardTxTopic    string
)

var registerDigicardTxnHandler = func(tag string, ch <-chan *kafkareader.Entity) {
	wg.Go(tag, func() {
		consumeDigicardTxnStream(slog.NewContextWithLogger(context.Background(), logger), ch)
	})
}

// startDigicardTxnStream creates Kafka Read and Register the method responsible to consumer
func startDigicardTxnStream(conf *config.KafkaConfig) {
	reader, err := streamStaticReader(context.Background(), DigicardTxStream,
		convertConfig(conf, digicardTxDTO), &digicard_transaction_tx.DigicardTransactionTx{},
		func(o *kafkareader.StreamConfig) *kafkareader.StreamConfig {
			o.Logger = &streamLogger{slogger: logger}
			return o
		},
	)
	if err != nil {
		panic(fmt.Sprintf("failed to create new DigicardTxnStream Reader, config=[%+v], err=[%+v]", conf, err))
	}
	digicardTxConsumer = reader
	digicardTxTopic = conf.TopicName
	registerDigicardTxnHandler(constants.DigicardTxStreamLogTag, digicardTxConsumer.GetDataChan())
}

// consumeDigicardTxnStream receives event from stream and pass it to processing methods
func consumeDigicardTxnStream(ctx context.Context, ch <-chan *kafkareader.Entity) {
	for event := range ch {
		data, ok := event.Event.(*digicard_transaction_tx.DigicardTransactionTx)
		logTags := append(commonTags(digicardTxTopic, digicardTxDTO), eventMetaTags(event)...)
		eventCtx := slog.AddTagsToContext(ctx, logTags...)

		if !ok {
			StatsDClient.Count1(string(servicename.TransactionHistory), metrics.DigicardTxnKafkaMessage,
				metrics.FailedTag,
				"errorOccured:wrong_entity")
			slog.FromContext(eventCtx).Fatal(constants.DigicardTxStreamLogTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", event.Event))
			continue
		}
		slog.FromContext(eventCtx).Info(constants.DigicardTxStreamLogTag, fmt.Sprintf("received message from digicard-txn transactionID: %s", data.TransactionID))
		err := digicard.HandleDigicardTxStream(eventCtx, data, StatsDClient)
		if err != nil {
			slog.FromContext(eventCtx).Warn(constants.DigicardTxStreamLogTag, fmt.Sprintf("handling stream event failed, %s", err.Error()))
		}
	}
}

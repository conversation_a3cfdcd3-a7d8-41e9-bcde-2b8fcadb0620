package consumers

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/deposits"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

const (
	InterestAggStreamDTO = "DepositsCoreTx"
)

var (
	interestAggConsumer kafkareader.Client
	interestAggTopic    string
)

// startConsumeInterestAgg creates Kafka Read and Register the method responsible to consumer
func startConsumeInterestAgg(conf *config.KafkaConfig) {
	reader, err := streams.NewStaticReader(
		context.Background(),
		DepositsCoreForInterestAggregationStream,
		convertConfig(conf, InterestAggStreamDTO),
		&deposits_core_tx.DepositsCoreTx{},
		func(o *kafkareader.StreamConfig) *kafkareader.StreamConfig {
			o.Logger = &streamLogger{slogger: logger}
			return o
		},
	)
	if err != nil {
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", conf, err))
	}

	interestAggConsumer = reader
	registerInterestAggHandler(constants.InterestAggStreamLogTag, interestAggConsumer.GetDataChan())
}

// consumeDepositsCoreStreamForInterestAgg receives event from stream and pass it to processing methods
func consumeDepositsCoreStreamForInterestAgg(ctx context.Context, ch <-chan *kafkareader.Entity) {
	for event := range ch {
		data, ok := event.Event.(*deposits_core_tx.DepositsCoreTx)
		if !ok {
			slog.FromContext(ctx).Fatal(constants.InterestAggStreamLogTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", event))
		}
		slog.FromContext(ctx).Info(constants.InterestAggStreamLogTag, fmt.Sprintf("%+v", data))
		err := deposits.HandleDepositsCoreStreamForInterestAgg(ctx, redisClient, interestAggConfig, data, StatsDClient)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.InterestAggStreamLogTag, fmt.Sprintf("handling stream event failed, %s", err.Error()),
				commonTags(interestAggTopic, InterestAggStreamDTO)...)
		}
	}
}

var registerInterestAggHandler = func(tag string, ch <-chan *kafkareader.Entity) {
	wg.Go(tag, func() {
		consumeDepositsCoreStreamForInterestAgg(slog.NewContextWithLogger(context.Background(), logger), ch)
	})
}

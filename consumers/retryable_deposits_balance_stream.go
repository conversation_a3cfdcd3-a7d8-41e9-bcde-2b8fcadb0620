package consumers

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/storage"

	"gitlab.myteksi.net/snd/streamsdk/kafka"

	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_balance_event"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/internal/metrics"
	depositsBalance "gitlab.myteksi.net/dakota/transaction-history/logic/processor/deposits"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
)

// DepositsBalanceHandler ...
type DepositsBalanceHandler struct {
	historyConfig config.TransactionHistoryServiceConfig
	featFlags     featureflag.Repo
}

// GetEventSchema ...
func (h DepositsBalanceHandler) GetEventSchema() kafka.Entity {
	return &deposits_balance_event.DepositsBalanceEvent{}
}

// Handle ...
func (h DepositsBalanceHandler) Handle(ctx context.Context, entity kafka.Entity) error {
	StatsDClient.Duration(logTag, metrics.StreamConsumeLatencyMetric, entity.GetStreamInfo().StreamTime, constants.DepositsBalanceTag)
	defer StatsDClient.Duration(logTag, metrics.StreamProcessLatencyMetric, time.Now(), constants.DepositsBalanceTag)

	ctx = featureflag.NewContextWithFeatureFlags(ctx, h.featFlags)

	data, ok := entity.(*deposits_balance_event.DepositsBalanceEvent)
	logTags := append(commonTags(dbTopic, depositsBalanceDTO), infoMetaTags(entity.GetStreamInfo())...)
	eventCtx := slog.AddTagsToContext(ctx, logTags...)
	if !ok {
		StatsDClient.Count1(string(servicename.TransactionHistory), metrics.DepositsCoreBalanceKafkaMessage,
			metrics.FailedTag,
			"errorOccured:wrong_entity")
		slog.FromContext(eventCtx).Fatal(constants.DepositsBalanceTag, fmt.Sprintf("wrong entity in reader, event=[%+v]", entity),
			commonTags(dbTopic, depositsBalanceDTO)...)
		return errors.New("wrong entity in reader")
	}
	eventCtx = slog.AddTagsToContext(ctx, slog.CustomTag("tm_balance_id", data.Event.BalanceID), slog.CustomTag("tm_batch_id", data.Event.PostingInstructionBatchID))

	slog.FromContext(eventCtx).Info(constants.DepositsBalanceTag, fmt.Sprintf("%+v", data))
	err := depositsBalance.HandleDBStream(eventCtx, data, storage.DB, StatsDClient, h.historyConfig)

	if err != nil {
		handledErr := handleErrorForRetryableStream(eventCtx, err, isRetryableDepositBalanceStreamEnabled, true, constants.DepositsBalanceTag)
		if handledErr == nil {
			slog.FromContext(eventCtx).Warn(constants.DepositsBalanceTag, fmt.Sprintf("handling stream event failed, %s", err.Error()),
				commonTags(dbTopic, depositsBalanceDTO)...)
		}
		return handledErr
	}
	return nil
}

// Package mockservices provides the necessary mocks to mock external services in staging environment
package mockservices

import (
	"context"
	"encoding/json"
	"flag"
	"net/http"
	"os"

	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.myteksi.net/dakota/servus/v2"
)

var isTestEnv = flag.Lookup("test.v") != nil

// TRUE ...
const TRUE = "true"

// UseMock is a flag to control whether the configured should be use or not.
var UseMock = !isTestEnv && os.Getenv("USE_MOCK") == "true"

// UseMockAuth is a temp toggle to enable simple auth in routes
var UseMockAuth = os.Getenv("MOCK_ENABLE_AUTH_MOCK") == TRUE

// WithAuth secure the endpoints with simple token authentication
func WithAuth() servus.MiddlewareFunc {
	return func(h servus.HandlerFunc) servus.HandlerFunc {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			headers := servus.HeaderFromCtx(ctx)
			token := os.Getenv("AUTH_TOKEN")
			if token == "" {
				return nil, servus.ServiceError{
					HTTPCode: http.StatusForbidden,
					Code:     "FORBIDDEN",
				}
			}
			auth := headers.Get("x-auth-token")
			if auth != token {
				return nil, servus.ServiceError{
					HTTPCode: http.StatusForbidden,
					Code:     "FORBIDDEN",
				}
			}
			return h(ctx, req)
		}
	}
}

// LogKafkaMsg ...
func LogKafkaMsg(ctx context.Context, eventType string, msg interface{}) {
	msgBytes, err := json.Marshal(msg)
	if err != nil {
		slog.FromContext(ctx).Error(eventType, err.Error())
		return
	}
	slog.FromContext(ctx).Info(eventType, string(msgBytes), slog.CustomTag("debug", "kafka"))
}

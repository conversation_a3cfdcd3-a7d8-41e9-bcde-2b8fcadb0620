package helper

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

func TestFetchLoanDetailForLoanTransactionID(t *testing.T) {
	type args struct {
		loanTransactionID string
		request           *api.GetLendingTransactionDetailRequest
	}
	tests := []struct {
		name        string
		args        args
		databaseErr error
		data        []*storage.LoanDetail
		want        *storage.LoanDetail
		wantErr     bool
	}{
		{
			name: "should return transactions when database has data",
			args: args{
				request:           &api.GetLendingTransactionDetailRequest{AccountID: "123345"},
				loanTransactionID: "abc123efg",
			},
			data: []*storage.LoanDetail{{
				ID:                101,
				LoanTransactionID: "1001",
				Amount:            200,
				Currency:          "MYR",
			}},
			want: &storage.LoanDetail{
				ID:                101,
				LoanTransactionID: "1001",
				Amount:            200,
				Currency:          "MYR",
			},
		},
		{
			name: "should return error when loan details is not present",
			args: args{
				request:           &api.GetLendingTransactionDetailRequest{AccountID: "123345"},
				loanTransactionID: "abc123efg",
			},
			databaseErr: data.ErrNoData,
			wantErr:     true,
		},
		{
			name: "should return error when database returns error",
			args: args{
				request:           &api.GetLendingTransactionDetailRequest{AccountID: "123345"},
				loanTransactionID: "abc123efg",
			},
			databaseErr: errors.New("transaction timed out"),
			wantErr:     true,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockLoan := &storage.MockILoanDetailDAO{}
			storage.LoanDetailD = mockLoan
			mockLoan.On("Find", mock.Anything, mock.Anything, mock.Anything).
				Return(test.data, test.databaseErr)

			transactions, err := FetchLoanDetailForLoanTransactionID(context.Background(), test.args.request, test.args.loanTransactionID)
			if test.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.want, transactions)
			}
		})
	}
}

func TestFetchLoanDetailsForTransactions(t *testing.T) {
	type args struct {
		loanTxnIDs   []string
		accountID    string
		hasLoanTxnID bool
	}
	tests := []struct {
		name        string
		args        args
		databaseErr error
		data        []*storage.LoanDetail
		want        map[string]*storage.LoanDetail
		wantErr     bool
	}{
		{
			name: "should return transactions when database has data",
			args: args{
				loanTxnIDs:   []string{"1001", "1002"},
				accountID:    "123456",
				hasLoanTxnID: true,
			},
			data: []*storage.LoanDetail{{
				ID:                101,
				LoanTransactionID: "1001",
				Amount:            200,
				Currency:          "MYR",
			}, {
				ID:                102,
				LoanTransactionID: "1002",
				Amount:            500,
				Currency:          "MYR",
			}},
			want: map[string]*storage.LoanDetail{
				"1001": {
					ID:                101,
					LoanTransactionID: "1001",
					Amount:            200,
					Currency:          "MYR",
				},
				"1002": {
					ID:                102,
					LoanTransactionID: "1002",
					Amount:            500,
					Currency:          "MYR",
				},
			},
		},
		{
			name: "should return error when database returns error",
			args: args{
				loanTxnIDs: []string{"101", "102"},
				accountID:  "1001",
			},
			databaseErr: errors.New("transaction timed out"),
			wantErr:     true,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockLoan := &storage.MockILoanDetailDAO{}
			storage.LoanDetailD = mockLoan
			mockLoan.On("Find", mock.Anything, mock.Anything, mock.Anything).
				Return(test.data, test.databaseErr)

			transactions, err := FetchLoanDetails(context.Background(), test.args.accountID)
			if test.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.want, transactions)
			}
		})
	}
}

func TestFetchLoanDetail(t *testing.T) {
	type args struct {
		req *api.GetLendingTransactionDetailRequest
	}
	tests := []struct {
		name        string
		args        args
		databaseErr error
		data        []*storage.LoanDetail
		wantErr     bool
	}{
		{
			name: "should return transactions when database has data",
			args: args{req: &api.GetLendingTransactionDetailRequest{
				AccountID:     "123456",
				TransactionID: "123123",
			}},
			data: []*storage.LoanDetail{{
				ID:                101,
				LoanTransactionID: "1001",
				Amount:            200,
				Currency:          "MYR",
			}},
		},
		{
			name: "should return error when database returns error",
			args: args{req: &api.GetLendingTransactionDetailRequest{
				AccountID:     "123456",
				TransactionID: "123123",
			}},
			databaseErr: errors.New("transaction timed out"),
			wantErr:     true,
		},
	}
	for _, tt := range tests {
		test := tt
		t.Run(tt.name, func(t *testing.T) {
			mockLoan := &storage.MockILoanDetailDAO{}
			storage.LoanDetailD = mockLoan
			mockLoan.On("Find", mock.Anything, mock.Anything, mock.Anything).
				Return(test.data, test.databaseErr)

			loanDetail, err := FetchLoanDetailForTransactionID(context.Background(), test.args.req)
			if test.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.data, loanDetail)
			}
		})
	}
}

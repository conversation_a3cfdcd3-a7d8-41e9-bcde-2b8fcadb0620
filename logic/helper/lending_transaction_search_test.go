package helper

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

func Test_sortTransactions(t *testing.T) {
	type args struct {
		transactions []*storage.TransactionsData
	}
	tests := []struct {
		name         string
		args         args
		expectedType string
	}{
		{
			name: "should return OUTBOUND_AUTHORISATION transaction first for a processing drawdown transaction",
			args: args{transactions: []*storage.TransactionsData{
				{
					TmTransactionType:  "OUTBOUND_AUTHORISATION",
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				}},
			},
			expectedType: "OUTBOUND_AUTHORISATION",
		},
		{
			name: "should return DRAWDOWN transaction first for a successful drawdown transaction",
			args: args{transactions: []*storage.TransactionsData{
				{
					TmTransactionType:  "OUTBOUND_AUTHORISATION",
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				},
				{
					TmTransactionType:  "TRANSFER",
					TransactionDomain:  "LENDING",
					TransactionType:    "DRAWDOWN",
					TransactionSubtype: "INTRABANK",
				},
				{
					TmTransactionType:  "SETTLEMENT",
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				},
				{
					TmTransactionType:  "SETTLEMENT",
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				}},
			},
			expectedType: "TRANSFER",
		},
		{
			name: "should return AUTH transaction first for a failed drawdown transaction",
			args: args{transactions: []*storage.TransactionsData{
				{
					TmTransactionType:  "RELEASE",
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				}, {
					TmTransactionType:  "OUTBOUND_AUTHORISATION",
					TransactionDomain:  "LENDING",
					TransactionType:    "LINE_OF_CREDIT",
					TransactionSubtype: "UTILISE_LIMIT",
				}},
			},
			expectedType: "OUTBOUND_AUTHORISATION",
		},
		{
			name: "should return TRANSFER transaction first for a successful repayment transaction",
			args: args{transactions: []*storage.TransactionsData{
				{
					TmTransactionType:  "TRANSFER",
					TransactionDomain:  "LENDING",
					TransactionType:    "REPAYMENT",
					TransactionSubtype: "INTRABANK",
				}},
			},
			expectedType: "TRANSFER",
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			SortTransactions(tt.args.transactions)
			assert.Equal(t, tt.expectedType, tt.args.transactions[0].TmTransactionType)
		})
	}
}

package helper

import (
	"context"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/server/config"

	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// FetchTransactionsForCursor return transactions for a particular cursor
func FetchTransactionsForCursor(ctx context.Context, req *dto.TransactionHistorySearchRequest, cursor *dto.PaginationCursor, transactionHistoryServiceConfig config.TransactionHistoryServiceConfig, featureFlags config.FeatureFlags) ([]*storage.TransactionsData, error) {
	filters := lendingTransactionSearchDBFilters(req, cursor, transactionHistoryServiceConfig, featureFlags)
	transactions, err := storage.TransactionsDataD.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		return nil, err
	}
	return transactions, nil
}

// lendingTransactionSearchDBFilters creates filter condition
// nolint: funlen
func lendingTransactionSearchDBFilters(req *dto.TransactionHistorySearchRequest, cursorData *dto.PaginationCursor, transactionHistoryServiceConfig config.TransactionHistoryServiceConfig, featureFlags config.FeatureFlags) []data.Condition {
	// add default filters
	filters := []data.Condition{
		data.Limit(int(6 * req.PageSize)),
		data.EqualTo("AccountID", req.AccountID),
		data.UnEqualTo("ClientBatchID", cursorData.TransactionID),
	}

	if req.StartingBefore != "" {
		filters = append(filters,
			data.LessThan("ID", cursorData.ID),
			data.DescendingOrder("BatchValueTimestamp"),
			data.DescendingOrder("ID"),
		)
	} else if req.EndingAfter != "" {
		filters = append(filters,
			data.GreaterThan("ID", cursorData.ID),
			data.AscendingOrder("BatchValueTimestamp"),
			data.AscendingOrder("ID"),
		)
	} else {
		filters = append(filters,
			data.DescendingOrder("BatchValueTimestamp"),
			data.DescendingOrder("ID"))
	}

	// feature flag for most recent 6 month transactions filter
	if featureFlags.EnableTransactionsSearchDurationFilter.Enabled && transactionHistoryServiceConfig.TransactionsSearchFilterDurationInMonths > 0 {
		filters = append(filters,
			data.GreaterThanOrEqualTo("BatchValueTimestamp", time.Now().AddDate(0, -1*transactionHistoryServiceConfig.TransactionsSearchFilterDurationInMonths, 0)))
	}

	if featureFlags.EnableBizFlexiCredit.Enabled {
		if req.ProductVariantCode == constants.FlexiBizLOCProductVariantCode {
			filters = append(filters, data.EqualTo("TransactionDomain", constants.BizLendingDomain))
		} else {
			filters = append(filters, data.UnEqualTo("TransactionDomain", constants.BizLendingDomain))
		}
	}

	startingDate, endingDate := ComputeDateRange(req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, *cursorData)
	if startingDate != "" {
		filters = append(filters, data.GreaterThanOrEqualTo("BatchValueTimestamp", startingDate))
	}
	if endingDate != "" {
		filters = append(filters, data.LessThanOrEqualTo("BatchValueTimestamp", endingDate))
	}

	if req.TransactionType != "" {
		filters = append(filters,
			data.EqualTo("TransactionType", req.TransactionType))
	} else {
		filters = append(filters,
			data.ContainedIn("TransactionType", utils.ConvertToInterface(constants.LendingTransactionTypes)...))
	}
	if req.TransactionSubtype != "" {
		filters = append(filters,
			data.EqualTo("TransactionSubtype", req.TransactionSubtype))
	} else {
		filters = append(filters,
			data.ContainedIn("TransactionSubtype", utils.ConvertToInterface(constants.LendingTransactionSubtypes)...))
	}

	return filters
}

// FetchTransaction returns transaction for an account and transaction ...
func FetchTransaction(ctx context.Context, req *api.GetLendingTransactionDetailRequest, transactionHistoryServiceConfig config.TransactionHistoryServiceConfig, featureFlags config.FeatureFlags) ([]*storage.TransactionsData, error) {
	filters := lendingTransactionDetailDBFilters(req, transactionHistoryServiceConfig, featureFlags)
	transactions, err := storage.TransactionsDataD.Find(ctx, filters...)
	if err != nil {
		return nil, err
	}
	return transactions, nil
}

// Prepares filter for fetching data from the TransactionData
func lendingTransactionDetailDBFilters(req *api.GetLendingTransactionDetailRequest, transactionHistoryServiceConfig config.TransactionHistoryServiceConfig, featureFlags config.FeatureFlags) []data.Condition {
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.EqualTo("ClientBatchID", req.TransactionID),
	}

	// feature flag for most recent 6 month transactions filter
	if featureFlags.EnableTransactionsSearchDurationFilter.Enabled && transactionHistoryServiceConfig.TransactionsSearchFilterDurationInMonths > 0 {
		filters = append(filters,
			data.GreaterThanOrEqualTo("BatchValueTimestamp", time.Now().AddDate(0, -1*transactionHistoryServiceConfig.TransactionsSearchFilterDurationInMonths, 0)))
	}

	return filters
}

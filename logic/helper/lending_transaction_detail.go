package helper

import (
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// GetPaymentTransactionID returns paymentTransactionID from transaction when loanDetail has the field missing for PROCESSING transaction ...
func GetPaymentTransactionID(transaction *storage.TransactionsData, loanDetail *storage.LoanDetail) string {
	if loanDetail.PaymentTransactionID != "" {
		return loanDetail.PaymentTransactionID
	}
	return transaction.ClientBatchID
}

package helper

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// FetchLoanDetails returns a map of loanTransactionID and loan detail for an account ...
func FetchLoanDetails(ctx context.Context, accountID string) (map[string]*storage.LoanDetail, error) {
	response := make(map[string]*storage.LoanDetail)

	// create filters and search in loan detail table
	filters := []data.Condition{
		data.EqualTo("AccountID", accountID),
	}

	loanDetails, err := storage.LoanDetailD.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Error while fetching data from loanDetail, err: %s", err.Error()))
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Successfully fetched loanDetails data: %s", utils.ToJSON(loanDetails)))
	// creating map
	for _, loanDetail := range loanDetails {
		response[loanDetail.LoanTransactionID] = loanDetail
	}
	return response, nil
}

// FetchLoanDetailForTransactionID returns loan detail for an account and transaction ...
func FetchLoanDetailForTransactionID(ctx context.Context, req *api.GetLendingTransactionDetailRequest) ([]*storage.LoanDetail, error) {
	filters := lendingTransactionWriteOffDBFilters(req)
	loanDetails, err := storage.LoanDetailD.Find(ctx, filters...)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Error while fetching write off transaction data from loan detail, err: %s", err.Error()))
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Successfully fetched loanDetail data: %s", utils.ToJSON(loanDetails)))
	return loanDetails, nil
}

func lendingTransactionWriteOffDBFilters(req *api.GetLendingTransactionDetailRequest) []data.Condition {
	filters := []data.Condition{
		data.ContainedIn("AccountID", req.AccountID),
		data.EqualTo("PaymentTransactionID", req.TransactionID),
	}
	return filters
}

// FetchLoanDetailForLoanTransactionID ...
func FetchLoanDetailForLoanTransactionID(ctx context.Context, req *api.GetLendingTransactionDetailRequest, loanTxnID string) (*storage.LoanDetail, error) {
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.EqualTo("LoanTransactionID", loanTxnID),
	}
	response, err := storage.LoanDetailD.Find(ctx, filters...)
	if err != nil && err == data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, "No data present in the loanDetailDB")
		return nil, customErr.BuildErrorResponse(customErr.ResourceNotFound, "No loan detail data found")
	}
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Error while fetching data from loanDetail, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, "Successfully fetched data from loanDetailDB")
	return response[0], nil
}

package helper

import (
	"sort"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// SortTransactions will sort transactions in the order of tmTransactionType priority defined ...
func SortTransactions(transactions []*storage.TransactionsData) {
	mapTypeToPriority := map[string]int64{
		constants.Transfer:              1,
		constants.Custom:                1,
		constants.CustomInstruction:     1,
		constants.OutboundAuthorisation: 2,
		constants.Release:               3,
		constants.Settlement:            4,
	}
	sort.Slice(transactions, func(i, j int) bool {
		return mapTypeToPriority[transactions[i].TmTransactionType] < mapTypeToPriority[transactions[j].TmTransactionType]
	})
}

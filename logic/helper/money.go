package helper

import (
	"math"

	"github.com/shopspring/decimal"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// ConvertToMoneyObj return a money Object
func ConvertToMoneyObj(amount decimal.Decimal) *api.Money {
	return &api.Money{
		CurrencyCode: "MYR",
		Val:          ConvertAmountToCents(amount),
	}
}

// ConvertAmountToCents Returns decimal amount converted to cents with the given precision
func ConvertAmountToCents(amount decimal.Decimal) int64 {
	floatValue, _ := amount.Float64()
	formattedAmountInCents := int64(math.Round(floatValue * 100))
	return formattedAmountInCents
}

// ConvertValueToFloat Returns decimal amount converted to cents with the given precision
func ConvertValueToFloat(value decimal.Decimal) float32 {
	floatValue, _ := value.Float64()
	return float32(floatValue)
}

package helper

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// FetchPaymentDetailForTransaction returns payment detail for an account and transaction ...
func FetchPaymentDetailForTransaction(ctx context.Context, req *api.GetLendingTransactionDetailRequest, paymentTransactionID string) (*storage.PaymentDetail, error) {
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.EqualTo("TransactionID", paymentTransactionID),
	}
	paymentDetails, err := storage.PaymentDetailD.Find(ctx, filters...)
	if err != nil && err == data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, "No data present in the paymentDetail")
		return nil, err
	}
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Error while fetching data from paymentDetail, err: %s", err.Error()))
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Successfully fetched paymentDetail data: %s", utils.ToJSON(paymentDetails)))
	return paymentDetails[0], nil
}

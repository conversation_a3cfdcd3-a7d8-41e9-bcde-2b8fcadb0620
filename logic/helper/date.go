package helper

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
)

var (
	// TimeZoneLocation denotes the timezone of Malaysia
	TimeZoneLocation = time.FixedZone("MYT", 8*60*60)
)

// GetDateMonthYear returns the date in words
func GetDateMonthYear(period string) string {
	splitDate := strings.Split(period, "-")
	year := splitDate[0]
	monthString, _ := strconv.Atoi(splitDate[1])
	month := time.Month(monthString).String()
	splitMonth := month[0:3]
	date := splitDate[2]
	timePeriodInString := fmt.Sprintf("%v %v %v", date, splitMonth, year)
	return timePeriodInString
}

// GetMonth ...
func GetMonth(eventTime time.Time, location *time.Location) string {
	localTime := eventTime.In(location)
	return localTime.Format(constants.MonthFormat)
}

// GetDay ...
func GetDay(eventTime time.Time, location *time.Location) string {
	localTime := eventTime.In(location)
	return localTime.Format(constants.DayFormat)
}

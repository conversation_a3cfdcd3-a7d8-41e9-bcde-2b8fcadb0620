package helper

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

func TestGetPaymentTransactionID(t *testing.T) {
	type args struct {
		transaction   *storage.TransactionsData
		paymentDetail *storage.LoanDetail
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "should return paymentTransactionID when paymentDetail has transactionID",
			args: args{
				transaction:   &storage.TransactionsData{ClientBatchID: "456"},
				paymentDetail: &storage.LoanDetail{PaymentTransactionID: "123"},
			},
			want: "123",
		},
		{
			name: "should return paymentTransactionID when paymentDetail does not have transactionID",
			args: args{
				transaction:   &storage.TransactionsData{ClientBatchID: "456"},
				paymentDetail: &storage.LoanDetail{},
			},
			want: "456",
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, GetPaymentTransactionID(tt.args.transaction, tt.args.paymentDetail), "GetPaymentTransactionID(%v, %v)", tt.args.transaction, tt.args.paymentDetail)
		})
	}
}

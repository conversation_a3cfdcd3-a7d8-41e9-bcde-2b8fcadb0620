package handlerlogic

import (
	"context"
	"os"
	"testing"

	"gitlab.myteksi.net/dakota/transaction-history/logic"
	"gitlab.myteksi.net/dakota/transaction-history/utils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"

	"gitlab.myteksi.net/dakota/common/tenants"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

func TestInvertDBResponseForBackwardScrolling(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		response := invertDBResponseForBackwardScrolling(resources.TransactionsDataMockDBRows())
		assert.Equal(t, responses.InvertDBResponseForBackwardScrollingSampleResponse(), response)
	})
}

func TestFormattedAmount(t *testing.T) {
	t.Run("debit", func(t *testing.T) {
		txData := &storage.TransactionsData{
			TransactionAmount: "0.56",
			DebitOrCredit:     "debit",
			TransactionType:   "SEND_MONEY",
		}

		response := logic.FormattedAmount(context.Background(), txData)
		assert.Equal(t, utils.MustConvertToInt64(-56), response)
	})

	t.Run("credit", func(t *testing.T) {
		txData := &storage.TransactionsData{
			TransactionAmount: "0.56",
			DebitOrCredit:     "credit",
			TransactionType:   "RECEIVE_MONEY",
		}

		response := logic.FormattedAmount(context.Background(), txData)
		assert.Equal(t, utils.MustConvertToInt64(56), response)
	})

	t.Run("debit_reversal", func(t *testing.T) {
		txData := &storage.TransactionsData{
			TransactionAmount: "0.56",
			DebitOrCredit:     "credit",
			TransactionType:   "SEND_MONEY_REVERSAL",
		}

		response := logic.FormattedAmount(context.Background(), txData)
		assert.Equal(t, utils.MustConvertToInt64(56), response)
	})

	t.Run("credit_reversal", func(t *testing.T) {
		txData := &storage.TransactionsData{
			TransactionAmount: "0.56",
			DebitOrCredit:     "debit",
			TransactionType:   "RECEIVE_MONEY_REVERSAL",
		}

		response := logic.FormattedAmount(context.Background(), txData)
		assert.Equal(t, utils.MustConvertToInt64(-56), response)
	})
}

func TestGetTransactionStatus(t *testing.T) {
	t.Run("payment-transaction", func(t *testing.T) {
		txRow := resources.GetCounterPartyDisplayNameData()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{"abc123efg": resources.PaymentDetailMockDBRows()[0]}

		response := getTransactionStatus(txRow, transactionsPaymentDetail)
		assert.Equal(t, "COMPLETED", response)
	})

	t.Run("internal-transaction-interest-paid", func(t *testing.T) {
		txRow := resources.GetCounterPartyDisplayNameData()[1]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}

		response := getTransactionStatus(txRow, transactionsPaymentDetail)
		assert.Equal(t, "COMPLETED", response)
	})

	t.Run("Interest-payout-rejected", func(t *testing.T) {
		txnData := resources.InterestPayoutReversalTransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[3]
		response := getTransactionStatus(txnData, map[string]*storage.PaymentDetail{txnData.ClientBatchID: paymentData})
		assert.Equal(t, "FAILED", response)
	})
	t.Run("Interest-payout-completed", func(t *testing.T) {
		txnData := resources.InterestPayoutTransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[1]
		response := getTransactionStatus(txnData, map[string]*storage.PaymentDetail{txnData.ClientBatchID: paymentData})
		assert.Equal(t, "COMPLETED", response)
	})

	t.Run("insurance-payment-transaction", func(t *testing.T) {
		txRow := resources.InsuranceTransactions()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{txRow.ClientBatchID: resources.PaymentDetailMockDBRows()[5]}

		response := getTransactionStatus(txRow, transactionsPaymentDetail)
		assert.Equal(t, "PROCESSING", response)
	})
}

func TestGetCounterPartyDisplayName(t *testing.T) {
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
		resources.GetAccountDetailsByAccountIDResponseParent(), nil)
	t.Run("payment-transaction", func(t *testing.T) {
		txRow := resources.GetCounterPartyDisplayNameData()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{"abc123efg": resources.PaymentDetailMockDBRows()[0]}

		response := getCounterPartyDisplayName(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "UserNo2", response)
	})
	t.Run("manual-transactions-sender-view", func(t *testing.T) {
		txRow := resources.GetCounterPartyDisplayNameData()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{"randomClientID": resources.PaymentDetailMockDBRows()[0]}

		response := getCounterPartyDisplayName(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "ManualUser2", response)
	})
	t.Run("manual-transactions-receiver-view", func(t *testing.T) {
		txRow := resources.GetCounterPartyDisplayNameData()[2]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{"randomClientID": resources.PaymentDetailMockDBRows()[2]}

		response := getCounterPartyDisplayName(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "ManualUser1", response)
	})
	t.Run("internal-transaction-interest-paid-in-english", func(t *testing.T) {
		mockAppConfig := &config.AppConfig{
			Locale: config.Locale{Language: "en"},
		}
		os.Setenv("LOCALISATION_PATH", "../../localise")
		localise.Init(mockAppConfig)
		txRow := resources.GetCounterPartyDisplayNameData()[1]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}

		response := getCounterPartyDisplayName(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Interest Earned", response)
	})
	t.Run("internal-transaction-interest-paid-in-bahasa", func(t *testing.T) {
		mockAppConfig := &config.AppConfig{
			Locale: config.Locale{Language: "id"},
		}
		os.Setenv("LOCALISATION_PATH", "../../localise")
		localise.Init(mockAppConfig)

		txRow := resources.GetCounterPartyDisplayNameData()[1]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}

		response := getCounterPartyDisplayName(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Bunga Didapat", response)
	})
	t.Run("child_account_transactions_funding", func(t *testing.T) {
		txRow := resources.PocketTransactions()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}

		response := getCounterPartyDisplayName(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "MAIN_ACCOUNT", response)
	})
	t.Run("child_account_transactions_withdrawal", func(t *testing.T) {
		txRow := resources.PocketTransactions()[3]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}

		response := getCounterPartyDisplayName(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Paris1", response)
	})
	t.Run("grab-transaction", func(t *testing.T) {
		txRow := resources.GetCounterPartyDisplayNameData()[3]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{"abcd12345": resources.PaymentDetailMockDBRows()[3]}

		response := getCounterPartyDisplayName(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Grab Ride", response)
	})
}

func TestGetAccountDisplayNameFromPaymentDetail(t *testing.T) {
	t.Run("payment-transaction", func(t *testing.T) {
		txRow := resources.GetAccountDisplayNameData()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{"abc123efg": resources.PaymentDetailMockDBRowsWithOriginAccount()[0]}

		response := getAccountDisplayNameFromPaymentDetail(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "OriginDisplayName", response)
	})
}

func TestGetCounterPartyDisplayNameDBMY(t *testing.T) {
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)

	t.Run("internal-transaction-interest-paid", func(t *testing.T) {
		txRow := resources.GetCounterPartyDisplayNameData()[1]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		response := getCounterPartyDisplayName(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Interest earned", response)
	})
	t.Run("payment-transaction", func(t *testing.T) {
		txRow := resources.GetCounterPartyDisplayNameData()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{"abc123efg": resources.PaymentDetailMockDBRowsDBMY()[0]}

		response := getCounterPartyDisplayName(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "User No2", response)
	})
	t.Run("list-pocketView-credit", func(t *testing.T) {
		txRow := resources.PocketTransactions()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			resources.GetAccountDetailsByAccountIDResponseChild(), nil)
		g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}
		response := g.listCounterPartyDisplayNamePocketView(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Money added", response)
	})
	t.Run("list-pocketView-debit", func(t *testing.T) {
		txRow := resources.PocketTransactions()[2]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			resources.GetAccountDetailsByAccountIDResponseChild(), nil)
		g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}
		response := g.listCounterPartyDisplayNamePocketView(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Money withdrawn", response)
	})
	t.Run("get-pocketView-credit", func(t *testing.T) {
		txRow := resources.PocketTransactions()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			resources.GetAccountDetailsByAccountIDResponseChild(), nil)
		g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}
		response := g.getCounterPartyDisplayNamePocketView(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Main Account", response)
	})
	t.Run("get-pocketView-debit", func(t *testing.T) {
		txRow := resources.PocketTransactionsDBMY()[1]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			resources.GetAccountDetailsByAccountIDResponseChild(), nil)
		g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}
		response := g.getCounterPartyDisplayNamePocketView(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Main Account", response)
	})
	t.Run("get-mainAccView-ops", func(t *testing.T) {
		txRow := resources.OPSTransactionsMockDBRows()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			resources.GetAccountDetailsByAccountIDResponseParent(), nil)
		g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}
		response := g.getCounterPartyDisplayNamePocketView(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Bank adjustment", response)
	})
	t.Run("get-pocketView-ops", func(t *testing.T) {
		txRow := resources.OPSTransactionsMockDBRows()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			resources.GetAccountDetailsByAccountIDResponseChild(), nil)
		g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}
		response := g.getCounterPartyDisplayNamePocketView(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Bank adjustment", response)
	})
	t.Run("list-mainAccView-ops", func(t *testing.T) {
		txRow := resources.OPSTransactionsMockDBRows()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			resources.GetAccountDetailsByAccountIDResponseParent(), nil)
		g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}
		response := g.listCounterPartyDisplayNamePocketView(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Bank adjustment", response)
	})
	t.Run("list-pocketView-ops", func(t *testing.T) {
		txRow := resources.OPSTransactionsMockDBRows()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			resources.GetAccountDetailsByAccountIDResponseChild(), nil)
		g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}
		response := g.listCounterPartyDisplayNamePocketView(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Bank adjustment", response)
	})
	t.Run("get-mainAccView-pocket", func(t *testing.T) {
		txRow := resources.PocketTransactionsDBMY()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			resources.GetAccountDetailsByAccountIDResponseParent(), nil)
		g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}
		response := g.getCounterPartyDisplayNamePocketView(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Paris1", response)
	})
	t.Run("list-mainAccView-pocket", func(t *testing.T) {
		txRow := resources.PocketTransactionsDBMY()[0]
		transactionsPaymentDetail := map[string]*storage.PaymentDetail{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(
			resources.GetAccountDetailsByAccountIDResponseParent(), nil)
		g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}
		response := g.listCounterPartyDisplayNamePocketView(context.Background(), txRow, transactionsPaymentDetail)
		assert.Equal(t, "Paris1", response)
	})
	t.Run("grab-DEFAULT", func(t *testing.T) {
		transactionsPaymentDetail := &storage.PaymentDetail{
			Metadata: []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "DEFAULT"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForGrabTxn(context.Background(), transactionsPaymentDetail)
		assert.Equal(t, "Grab", response)
	})
	t.Run("grab-CANCELLATIONFEE", func(t *testing.T) {
		transactionsPaymentDetail := &storage.PaymentDetail{
			Metadata: []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "CANCELLATIONFEE"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForGrabTxn(context.Background(), transactionsPaymentDetail)
		assert.Equal(t, "Grab", response)
	})
	t.Run("grab-OUTSTANDINGFEE", func(t *testing.T) {
		transactionsPaymentDetail := &storage.PaymentDetail{
			Metadata: []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "OUTSTANDINGFEE"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForGrabTxn(context.Background(), transactionsPaymentDetail)
		assert.Equal(t, "Grab", response)
	})
	t.Run("grab-NOSHOWFEE", func(t *testing.T) {
		transactionsPaymentDetail := &storage.PaymentDetail{
			Metadata: []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "NOSHOWFEE"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForGrabTxn(context.Background(), transactionsPaymentDetail)
		assert.Equal(t, "Grab", response)
	})
	t.Run("grab-TRANSPORT", func(t *testing.T) {
		transactionsPaymentDetail := &storage.PaymentDetail{
			Metadata: []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "TRANSPORT"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForGrabTxn(context.Background(), transactionsPaymentDetail)
		assert.Equal(t, "Grab Ride", response)
	})
	t.Run("grab-EXPRESS", func(t *testing.T) {
		transactionsPaymentDetail := &storage.PaymentDetail{
			Metadata: []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "EXPRESS"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForGrabTxn(context.Background(), transactionsPaymentDetail)
		assert.Equal(t, "Grab Express", response)
	})
	t.Run("grab-FOOD", func(t *testing.T) {
		transactionsPaymentDetail := &storage.PaymentDetail{
			Metadata: []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "FOOD"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForGrabTxn(context.Background(), transactionsPaymentDetail)
		assert.Equal(t, "Grab Food", response)
	})
	t.Run("grab-TOPUP", func(t *testing.T) {
		transactionsPaymentDetail := &storage.PaymentDetail{
			Metadata: []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "TOPUP"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForGrabTxn(context.Background(), transactionsPaymentDetail)
		assert.Equal(t, "GrabPay Wallet", response)
	})
	t.Run("grab-TIPPING", func(t *testing.T) {
		transactionsPaymentDetail := &storage.PaymentDetail{
			Metadata: []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "TIPPING"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForGrabTxn(context.Background(), transactionsPaymentDetail)
		assert.Equal(t, "Grab Tips", response)
	})
	t.Run("grab-REFUND", func(t *testing.T) {
		transactionsPaymentDetail := &storage.PaymentDetail{
			TransactionType: constants.SpendMoneyRevTxType,
			Metadata:        []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "TRANSPORT"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForGrabTxn(context.Background(), transactionsPaymentDetail)
		assert.Equal(t, "Grab", response)
	})
	t.Run("rewards", func(t *testing.T) {
		txnData := &storage.TransactionsData{
			TransactionType:    constants.RewardsCashback,
			TransactionDetails: []byte(`{"campaignName": "Grab Unlimited Reward", "campaignDescription": "Grab Unlimited Cashback"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForRewardsTxn(context.Background(), txnData)
		assert.Equal(t, "Grab Unlimited Reward", response)
	})
	t.Run("card rewards", func(t *testing.T) {
		txnData := &storage.TransactionsData{
			TransactionDomain:  constants.DebitCardDomain,
			TransactionType:    constants.RewardsCashback,
			TransactionDetails: []byte(`{"campaignName": "Cashback earned"}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForRewardsTxn(context.Background(), txnData)
		assert.Equal(t, "Cashback earned", response)
	})
	t.Run("sad path-rewards", func(t *testing.T) {
		txnData := &storage.TransactionsData{
			TransactionType:    constants.RewardsCashback,
			TransactionDetails: []byte(`{}`),
		}
		response := presenterhelper.GetCounterPartyDisplayNameForRewardsTxn(context.Background(), txnData)
		assert.Equal(t, "Reward", response)
	})
}

func TestGetTransactionRemarks(t *testing.T) {
	t.Run("remarks from payment metadata", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedRemarks := "Thanks for the dinner"
		actualRemarks := getTransactionRemarks(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedRemarks, actualRemarks)
	})
	t.Run("remarks from batch", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		expectedRemarks := "TEST REMARKS"
		actualRemarks := getTransactionRemarks(context.Background(), txnData, nil)
		assert.Equal(t, expectedRemarks, actualRemarks)
	})
}

func TestGetTransactionCategoryAndIconURL(t *testing.T) {
	mockAppConfig := &config.AppConfig{
		IconConfig: config.IconConfig{
			SavingsPocketTransfer:   "https://assets.dev.bankfama.net/dev/transfer_money.png",
			SavingsPocketWithdrawal: "https://assets.dev.bankfama.net/dev/withdrawal.png",
			DefaultTransaction:      "https://assets.dev.bankfama.net/dev/UserDefault.png",
			Withdrawal:              "https://assets.dev.bankfama.net/dev/withdrawal.png",
			TransferIn:              "https://assets.dev.bankfama.net/dev/receive_money.png",
			TransferOut:             "https://assets.dev.bankfama.net/dev/transfer_money.png",
			TransferFee:             "https://assets.dev.bankfama.net/dev/transfer_money.png",
			InterestPayout:          "https://assets.dev.bankfama.net/dev/interest_earned.png",
			TaxOnInterest:           "https://assets.dev.bankfama.net/dev/tax_on_interest.png",
			Adjustment:              "https://assets.dev.bankfama.net/dev/transfer_money.png",
		},
		Tenant: "",
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	t.Run("should return correct category and icon for pocket funding transactions when debit on main account", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.PocketFundingTransactionSubType
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, "Withdrawal", category.Name)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/transfer_money.png", iconURL)
	})
	t.Run("should return correct category and icon for pocket funding transactions when credit on pocket", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.PocketFundingTransactionSubType
		txnData.DebitOrCredit = constants.CREDIT
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, "Transfer In", category.Name)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/receive_money.png", iconURL)
	})
	t.Run("should return correct category and icon for pocket withdrawal transactions when debit on pocket ", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.PocketWithdrawalTransactionSubType
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, "Withdrawal", category.Name)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/withdrawal.png", iconURL)
	})
	t.Run("should return correct category and icon for pocket withdrawal transactions when credit on main", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.PocketWithdrawalTransactionSubType
		txnData.DebitOrCredit = constants.CREDIT
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, "Transfer In", category.Name)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/receive_money.png", iconURL)
	})
	t.Run("should return correct category and icon for intrabank debit transactions ", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.IntraBank
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, "Transfer Out", category.Name)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/transfer_money.png", iconURL)
	})
	t.Run("should return correct category and icon for intrabank credit transactions", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.IntraBank
		txnData.DebitOrCredit = constants.CREDIT
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, "Transfer In", category.Name)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/receive_money.png", iconURL)
	})
	t.Run("should return correct category and icon for unknown transaction subtype", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.UnknownTransactionSubtype
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, &api.Category{}, category)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/UserDefault.png", iconURL)
	})
	t.Run("should return correct category and icon for send money fee", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.SendMoneyFeeTransactionType
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, "Transfer Fee", category.Name)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/transfer_money.png", iconURL)
	})
	t.Run("should return correct category and icon for send money", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.SendMoneyTxType
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, "Transfer Out", category.Name)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/transfer_money.png", iconURL)
	})
	t.Run("should return correct category and icon for receive money", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.ReceiveMoneyTxType
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, "Transfer In", category.Name)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/receive_money.png", iconURL)
	})
	t.Run("should return correct category and icon for interest payout", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.InterestPayoutTransactionType
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, &api.Category{}, category)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/interest_earned.png", iconURL)
	})
	t.Run("should return correct category and icon for tax payout", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.TaxPayoutTransactionTypes[0]
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, &api.Category{}, category)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/tax_on_interest.png", iconURL)
	})
	t.Run("should return correct category and icon for adjustment", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.Adjustment
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, "Adjustment Transaction", category.Name)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/transfer_money.png", iconURL)
	})
	t.Run("should return correct category and icon for other transactions", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = "DEFAULT"
		category, iconURL := getTransactionCategoryAndIconURL(txnData)
		assert.Equal(t, &api.Category{}, category)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/UserDefault.png", iconURL)
	})
}

func TestGetTransactionCategoryAndIconURLDBMY(t *testing.T) {
	mockAppConfig := &config.AppConfig{
		IconConfig: config.IconConfig{
			DefaultTransaction:     "https://assets.dev.bankfama.net/dev/UserDefault.png",
			InterestEarn:           "https://assets.dev.g-bank.app/txn-history/interest_earn.png",
			BankAdjustment:         "https://assets.dev.g-bank.app/txn-history/bank_adjustment.png",
			PocketFunding:          "https://assets.dev.g-bank.app/txn-history/pocket_funding.png",
			MainMoneyIn:            "https://assets.dev.g-bank.app/txn-history/main_money_in.png",
			MainMoneyOut:           "https://assets.dev.g-bank.app/txn-history/main_money_out.png",
			MainPocketWithdrawal:   "https://assets.dev.g-bank.app/txn-history/main_pocket_withdrawal.png",
			PocketPocketWithdrawal: "https://assets.dev.g-bank.app/txn-history/pocket_pocket_withdrawal.png",
		},
		Tenant: "MY",
	}
	defer func() { config.SetTenant("") }()
	config.SetTenant(mockAppConfig.Tenant)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
	t.Run("should return correct category and icon for pocket funding transactions when debit on main account", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.PocketFundingTransactionSubType
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, "Withdrawal", category.Name)
		assert.Equal(t, constants.IconURLMap[constants.PocketFundingTransactionSubType], iconURL)
	})
	t.Run("should return correct category and icon for pocket funding transactions when credit on pocket", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.PocketFundingTransactionSubType
		txnData.DebitOrCredit = constants.CREDIT
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, "Transfer In", category.Name)
		assert.Equal(t, constants.IconURLMap[constants.PocketFundingTransactionSubType], iconURL)
	})
	t.Run("should return correct category and icon for pocket withdrawal transactions when debit on pocket ", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.PocketWithdrawalTransactionSubType
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, "Withdrawal", category.Name)
		assert.Equal(t, constants.IconURLMap[constants.PocketWithdrawalIconPocketView], iconURL)
	})
	t.Run("should return correct category and icon for pocket withdrawal transactions when credit on main", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.PocketWithdrawalTransactionSubType
		txnData.DebitOrCredit = constants.CREDIT
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, "Transfer In", category.Name)
		assert.Equal(t, constants.IconURLMap[constants.PocketWithdrawalIconMainAccountView], iconURL)
	})
	t.Run("should return correct category and icon for intrabank debit transactions ", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.IntraBank
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, "Transfer Out", category.Name)
		assert.Equal(t, constants.IconURLMap[constants.TransferOut], iconURL)
	})
	t.Run("should return correct category and icon for intrabank credit transactions", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.IntraBank
		txnData.DebitOrCredit = constants.CREDIT
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, "Transfer In", category.Name)
		assert.Equal(t, constants.IconURLMap[constants.TransferIn], iconURL)
	})
	t.Run("should return correct category and icon for unknown transaction subtype", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[0]
		txnData.TransactionSubtype = constants.UnknownTransactionSubtype
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, &api.Category{}, category)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/UserDefault.png", iconURL)
	})
	t.Run("should return correct category and icon for fund in", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.FundInTxType
		txnData.DebitOrCredit = constants.CREDIT
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, "Transfer In", category.Name)
		assert.Equal(t, constants.IconURLMap[constants.TransferIn], iconURL)
	})
	t.Run("should return correct category and icon for send money", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.SendMoneyTxType
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, "Transfer Out", category.Name)
		assert.Equal(t, constants.IconURLMap[constants.TransferOut], iconURL)
	})
	t.Run("should return correct category and icon for receive money", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.ReceiveMoneyTxType
		txnData.DebitOrCredit = constants.CREDIT
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, "Transfer In", category.Name)
		assert.Equal(t, constants.IconURLMap[constants.TransferIn], iconURL)
	})
	t.Run("should return correct category and icon for interest payout", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.InterestPayoutTransactionType
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, &api.Category{}, category)
		assert.Equal(t, constants.IconURLMap[constants.InterestEarned], iconURL)
	})
	t.Run("should return correct category and icon for adjustment", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = constants.Adjustment
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, "Adjustment Transaction", category.Name)
		assert.Equal(t, constants.IconURLMap[constants.BankAdjustment], iconURL)
	})
	t.Run("should return correct category and icon for other transactions", func(t *testing.T) {
		txnData := resources.TransactionsDataMockDBRows()[0]
		txnData.TransactionType = "DEFAULT"
		var key = getTxnScenarioKey(txnData)
		iconURL := presenterhelper.TransactionResponseHelperFuncs[key].Icon
		category := presenterhelper.TransactionResponseHelperFuncs[key].Category
		assert.Equal(t, &api.Category{}, category)
		assert.Equal(t, "https://assets.dev.bankfama.net/dev/UserDefault.png", iconURL)
	})
}

func TestGrabTxnRecipientReference(t *testing.T) {
	t.Run("remarks from payment metadata", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedRemarks := "Thanks for the dinner"
		actualRemarks := getTransactionRemarks(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedRemarks, actualRemarks)
	})

	t.Run("remarks from batch", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		expectedRemarks := "TEST REMARKS"
		actualRemarks := getTransactionRemarks(context.Background(), txnData, nil)
		assert.Equal(t, expectedRemarks, actualRemarks)
	})
}

func TestGetCounterPartyDisplayNameForLendingTransactions(t *testing.T) {
	t.Run("different transaction-type", func(t *testing.T) {
		transaction := &storage.TransactionsData{
			TransactionType: constants.OPSTransactionType,
		}
		expected := ""
		result := getCounterPartyDisplayNameForLendingTransactions(transaction)
		assert.Equal(t, expected, result, "Expected and actual values do not match")
	})
	t.Run("happy-path", func(t *testing.T) {
		transaction := &storage.TransactionsData{
			TransactionType: constants.DrawdownTransactionType,
		}
		mockAppConfig := &config.AppConfig{
			Locale: config.Locale{Language: "en"},
		}
		os.Setenv("LOCALISATION_PATH", "../../localise/localisetest")
		localise.Init(mockAppConfig)
		expected := localise.Translate("drawdown")
		result := getCounterPartyDisplayNameForLendingTransactions(transaction)
		assert.Equal(t, expected, result, "Expected and actual values match")
	})
}

package handlerlogic

import (
	"context"
	"database/sql"
	"net/http"
	"os"
	"strconv"
	"testing"

	"github.com/stretchr/testify/mock"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"

	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"

	"github.com/stretchr/testify/assert"
)

func TestGetOpsForNonResidentTransfer(t *testing.T) {
	locale := utils.GetLocale()
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetParentAccountDetailsByAccountIDResponse(), nil)
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		Locale: config.Locale{Language: "en"},
	}

	mockStore := &storage.MockDatabaseStore{}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := NewGetOpsSearch(mockAccountServiceClient, mockAppConfig, mockStore)

	t.Run("Transfer to Non-resident for ops list", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
		}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.TransferNonResidentTransactions(), nil)
		storage.TransactionsDataD = mockTransactionData
		mockPaymentTxnDetailStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockPaymentTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferNonResidentTrxDetails(), nil)
		storage.PaymentDetailD = mockPaymentTxnDetailStorageDAO

		response, err := g.GetOpsSearch(context.Background(), request)
		assert.Nil(t, err)
		assert.Len(t, response.Data, 1)
		counterPartyTransactionDetails := response.Data[0].CounterParty.TransactionDetails
		assert.Equal(t, "2", counterPartyTransactionDetails["residentStatus"])
		assert.Equal(t, "16830", counterPartyTransactionDetails["purposeCode"])
		assert.Equal(t, "MY", counterPartyTransactionDetails["beneficiaryCountry"])
		assert.Equal(t, "NR", counterPartyTransactionDetails["relationshipCode"])
	})
}

func TestGetOpsSpendCardReversal(t *testing.T) {
	locale := utils.GetLocale()
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetParentAccountDetailsByAccountIDResponse(), nil)
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		Locale: config.Locale{Language: "en"},
	}

	mockStore := &storage.MockDatabaseStore{}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := NewGetOpsSearch(mockAccountServiceClient, mockAppConfig, mockStore)

	t.Run("Type SPEND_CARD_ATM_REVERSAL for ops list", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
		}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.SpendCardATMReversalTransactions(), nil)
		storage.TransactionsDataD = mockTransactionData
		mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
		mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardATMReversalTrxDetails(), nil)
		storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

		response, err := g.GetOpsSearch(context.Background(), request)
		assert.Nil(t, err)
		assert.Len(t, response.Data, 1)
		transactionItem := response.Data[0]
		assert.Equal(t, false, transactionItem.IsPartialSettlement)
		assert.Equal(t, int64(1), transactionItem.CapturedOriginalAmountTillDate)
		assert.Equal(t, int64(0), transactionItem.CapturedAmountTillDate)
		assert.Equal(t, "ATM withdrawal reversal", transactionItem.TransactionDescription)
	})

	t.Run("Type SPEND_CARD_PRESENT_REVERSAL for ops list", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
		}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.SpendCardPresentReversalTransactions(), nil)
		storage.TransactionsDataD = mockTransactionData
		mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
		mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardPresentReversalTrxDetails(), nil)
		storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

		response, err := g.GetOpsSearch(context.Background(), request)
		assert.Nil(t, err)
		assert.Len(t, response.Data, 1)
		transactionItem := response.Data[0]
		assert.Equal(t, "Refund from Amazon", transactionItem.TransactionDescription)
	})

	t.Run("Type SPEND_CARD_NOT_PRESENT_REVERSAL for ops list", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
		}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.SpendCardNotPresentReversalTransactions(), nil)
		storage.TransactionsDataD = mockTransactionData
		mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
		mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardNotPresentReversalTrxDetails(), nil)
		storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

		response, err := g.GetOpsSearch(context.Background(), request)
		assert.Nil(t, err)
		assert.Len(t, response.Data, 1)
		transactionItem := response.Data[0]
		assert.Equal(t, "Refund from Amazon", transactionItem.TransactionDescription)
	})
}

func TestGetOpsSearchValidator(t *testing.T) {
	ctx := context.Background()
	t.Run("identifier-parameter-missing", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrMissingIdentifier.Code), 10), customErr.ErrMissingIdentifier.Message)

		errorResponse := GetOpsSearchRequestIdentifierValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("identifier-invalid-combination-accountID,externalID", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID:  "12345",
			ExternalID: "123xyz",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidIdentifier.Code), 10), customErr.ErrInvalidIdentifier.Message)

		errorResponse := GetOpsSearchRequestIdentifierValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("identifier-invalid-combination-externalID,txnID", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			ExternalID:    "123xyz",
			TransactionID: "abc123",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidIdentifier.Code), 10), customErr.ErrInvalidIdentifier.Message)

		errorResponse := GetOpsSearchRequestIdentifierValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("identifier-invalid-combination-externalID,batchID", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			ExternalID: "123xyz",
			BatchID:    "abc123",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidIdentifier.Code), 10), customErr.ErrInvalidIdentifier.Message)

		errorResponse := GetOpsSearchRequestIdentifierValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("identifier-invalid-combination-accountID,batchID,txnID", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID:     "12345",
			TransactionID: "123xyz",
			BatchID:       "abc123",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidIdentifier.Code), 10), customErr.ErrInvalidIdentifier.Message)

		errorResponse := GetOpsSearchRequestIdentifierValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("identifier-invalid-combination-batchID,txnID", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			TransactionID: "123xyz",
			BatchID:       "abc123",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidIdentifier.Code), 10), customErr.ErrInvalidIdentifier.Message)

		errorResponse := GetOpsSearchRequestIdentifierValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("identifier-happy-path", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID:     "12345",
			TransactionID: "123xyz",
		}

		errorResponse := GetOpsSearchRequestIdentifierValidator(ctx, request)
		assert.Equal(t, nil, errorResponse)
	})

	t.Run("attribute-invalid-status", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "12345",
			Status:    "PENDING",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidTxnStatus.Code), 10), customErr.ErrInvalidTxnStatus.Message)

		errorResponse := GetOpsSearchRequestValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("attribute-invalid-txn-type", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID:       "12345",
			TransactionType: "SENT_MONEY",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidTxnType.Code), 10), customErr.ErrInvalidTxnType.Message)

		errorResponse := GetOpsSearchRequestValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("attribute-invalid-txn-subtype", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID:          "12345",
			TransactionSubtype: "RPP",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidTxnSubtype.Code), 10), customErr.ErrInvalidTxnSubtype.Message)

		errorResponse := GetOpsSearchRequestValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("attribute-invalid-amount-range", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID:  "12345",
			FromAmount: 1000,
			ToAmount:   100,
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidTxnAmountRange.Code), 10), customErr.ErrInvalidTxnAmountRange.Message)

		errorResponse := GetOpsSearchRequestValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("attribute-negative-fromAmount", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID:  "12345",
			FromAmount: -1000,
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidTxnAmount.Code), 10), customErr.ErrInvalidTxnAmount.Message)

		errorResponse := GetOpsSearchRequestValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("attribute-negative-toAmount", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "12345",
			ToAmount:  -1000,
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidTxnAmount.Code), 10), customErr.ErrInvalidTxnAmount.Message)

		errorResponse := GetOpsSearchRequestValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("page-size-negative", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "12345",
			PageSize:  -1,
		}

		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidPageSize.Code), 10), customErr.ErrInvalidPageSize.Message)

		errorResponse := GetOpsSearchRequestValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("page-size-greater-than-max", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "12345",
			PageSize:  MaxPageSize + 1,
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidPageSize.Code), 10), customErr.ErrInvalidPageSize.Message)

		errorResponse := GetOpsSearchRequestValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("attribute-happy-path", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID:          "12345",
			Status:             "PROCESSING",
			TransactionType:    "TRANSFER_MONEY",
			TransactionSubtype: "INTRABANK",
			FromAmount:         100,
			ToAmount:           1000,
			PageSize:           5,
		}

		errorResponse := GetOpsSearchRequestValidator(ctx, request)
		assert.Equal(t, nil, errorResponse)
	})

	t.Run("filter-invalid-combination-externalID", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			ExternalID: "12345",
			StartDate:  "2023-01-01",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidFilter.Code), 10), customErr.ErrInvalidFilter.Message)

		errorResponse := GetOpsSearchRequestFilterCombinationValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("filter-invalid-combination-txnID", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			TransactionID: "12345",
			StartDate:     "2023-01-01",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidFilter.Code), 10), customErr.ErrInvalidFilter.Message)

		errorResponse := GetOpsSearchRequestFilterCombinationValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("filter-invalid-combination-batchID", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			BatchID:   "12345",
			StartDate: "2023-01-01",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrInvalidFilter.Code), 10), customErr.ErrInvalidFilter.Message)

		errorResponse := GetOpsSearchRequestFilterCombinationValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("filter-invalid-combination-status,timestamp", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "12345",
			Status:    "PROCESSING",
		}
		expectedError := customErr.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(customErr.ErrMissingTimestampFilter.Code), 10), customErr.ErrMissingTimestampFilter.Message)

		errorResponse := GetOpsSearchRequestFilterCombinationValidator(ctx, request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("filter-happy-path", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "12345",
			Status:    "PROCESSING",
			StartDate: "2023-01-01",
		}

		errorResponse := GetOpsSearchRequestFilterCombinationValidator(ctx, request)
		assert.Equal(t, nil, errorResponse)
	})
}

func TestGetOpsSearchResponseGenerator(t *testing.T) {
	locale := utils.GetLocale()
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	mockStore := &storage.MockDatabaseStore{}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := NewGetOpsSearch(mockAccountServiceClient, mockAppConfig, mockStore)

	t.Run("happy-path-next-page-exist", func(t *testing.T) {
		dbRows := resources.OpsSearchTransactionsDataMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail, 0)
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		assert.Equal(t, responses.GetOpsSearchFirstPageResponseForListTransaction(), response)
	})

	t.Run("happy-path-no-next-page", func(t *testing.T) {
		dbRows := resources.OpsSearchTransactionsDataMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail, 0)
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		assert.Equal(t, responses.GetOpsSearchOnlyPageResponse(), response)
	})

	t.Run("happy-path-prev-page-scrolling", func(t *testing.T) {
		dbRows := resources.OpsSearchGetAllTransactionsBackwardScrollingMockDBResponse()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail, 0)
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		request := &api.GetOpsSearchRequest{
			AccountID:   "**********",
			StartDate:   "2021-08-01",
			EndDate:     "2021-08-31",
			PageSize:    2,
			EndingAfter: "MjAyMS0wOC0yMCAwNzo0MDowMCArMDUzMCBJU1QsMSwz",
		}
		cursorData := dto.PaginationCursor{
			ID:                 1,
			FirstTransactionID: 3,
			Date:               "2021-08-20 07:40:00 +0530",
		}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		expected := responses.GetOpsSearchBackwardScrollingPrevPageResponse()
		expected.Links = map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		}
		assert.Equal(t, expected, response)
	})

	t.Run("happy-path-prev-next-page-exits", func(t *testing.T) {
		dbRows := resources.OpsSearchGetAllTransactionsPrevNextExistMockDBResponse()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail, 0)
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		request := &api.GetOpsSearchRequest{
			AccountID:      "**********",
			StartDate:      "2021-08-01",
			EndDate:        "2021-08-31",
			PageSize:       1,
			StartingBefore: "MjAyMS0wOC0yMCAwNzo0MTo0MCArMDUzMCBJU1QsMiwy",
		}
		cursorData := dto.PaginationCursor{
			ID:                 1,
			FirstTransactionID: 3,
			Date:               "2021-08-20 07:40:00 +0530",
		}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		expected := responses.GetOpsSearchPrevNextBothExistResponse()
		expected.Links = map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=1&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "/v1/accounts/**********/transactions?pageSize=1&endingAfter=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
		}
		assert.Equal(t, expected, response)
	})

	t.Run("happy-path-no-next-page-with-cards-txn", func(t *testing.T) {
		dbRows := resources.OpsSearchTransactionsCardsDataMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := resources.OpsSearchCardDetailData()
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		assert.Equal(t, responses.GetOpsSearchCardsTxnOnlyPageResponse(), response)
	})

	t.Run("happy-path-no-next-page-with-grab-txn", func(t *testing.T) {
		dbRows := resources.OpsSearchTransactionsGrabDataMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailGrabData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		assert.Equal(t, responses.GetOpsSearchGrabTxnOnlyPageResponse(), response)
	})

	t.Run("moomoo-trans", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchMooMooPaymentDetailMockDBRows(), nil)
		storage.PaymentDetailD = mockStorageDAO

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.OpsSearchTransactionsMooMooDataMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetOpsSearchResponseForListMooMooTransaction()
		response, err := g.GetOpsSearch(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})
}

func TestGetOpsSearchFromDB(t *testing.T) {
	locale := utils.GetLocale()
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetParentAccountDetailsByAccountIDResponse(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		Locale: config.Locale{Language: "en"},
	}

	mockStore := &storage.MockDatabaseStore{}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := NewGetOpsSearch(mockAccountServiceClient, mockAppConfig, mockStore)

	t.Run("no-matching-resources", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "12345",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		expectedResp := &api.GetOpsSearchResponse{
			Links: map[string]string{"prev": "", "prevCursorID": "", "next": "", "nextCursorID": ""},
			Data:  []api.OpsSearchResponse{},
		}

		response, err := g.GetOpsSearch(context.Background(), request)
		assert.Nil(t, err)
		assert.Equal(t, expectedResp, response)
	})

	t.Run("happy-path-firstPage", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetOpsSearchFirstPageMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetOpsSearchFirstPageResponseForListTransaction()
		response, err := g.GetOpsSearch(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-with-pocket-txns", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "",
			EndDate:   "",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.OpsSearchPocketTransactions(), nil).Once()
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.OpsSearchCounterPartyTransactionEntryForPocketTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetOpsSearchPocketTxnsResponse()
		response, err := g.GetOpsSearch(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("error-path-when-transaction-data-db-throws-error", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "",
			EndDate:   "",
			PageSize:  1,
		}
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, customErr.DefaultInternalServerError)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows()[3:], nil)
		storage.PaymentDetailD = mockStorageDAO

		_, err := g.GetOpsSearch(context.Background(), request)
		assert.Error(t, customErr.DefaultInternalServerError, err)
	})
}

func TestOpsSearchDBFilters(t *testing.T) {
	t.Run("happyPath", func(t *testing.T) {
		request := &api.GetOpsSearchRequest{
			AccountID: "12345",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		var cursorData dto.PaginationCursor
		param := mapGetOpsSearchRequestToOpsSearchParam(request, false)
		filters := buildOpsSearchTxnDataFilters(param, cursorData)
		assert.Equal(t, 8, len(filters))
	})
}

func TestGetTxnDataByExternalID(t *testing.T) {
	locale := utils.GetLocale()
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetParentAccountDetailsByAccountIDResponse(), nil)

	mockStore := &storage.MockDatabaseStore{}
	mockStore.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(&sql.DB{}, nil).Once()
	mockStore.On("GetTxnDataByExternalIDFromDB", mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchTransactionsDataByExternalIDMockDBRows(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		DefaultAppConfig: servus.DefaultAppConfig{
			Data: &servus.DataConfig{
				MySQL: &data.MysqlConfig{
					MysqlMasterSlaveConfig: data.MysqlMasterSlaveConfig{},
				},
			},
		},
		Locale: config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := NewGetOpsSearch(mockAccountServiceClient, mockAppConfig, mockStore)

	expectedResponse := resources.OpsSearchTransactionsDataByExternalIDMockDBRows()
	response, err := g.Store.GetTxnDataByExternalIDFromDB(context.Background(), "xyz123", &sql.DB{})
	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, response)
}

func TestOpsSearchFilterTransactionsOnAccountPhaseAndType(t *testing.T) {
	t.Run("all-posting-committed-phase", func(t *testing.T) {
		req := &api.GetOpsSearchRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.AllCommittedPosting()
		finalDBResponse, clientBatchTxIDs := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"efg123abd", "abc123efg", "efg1111abd"}}, clientBatchTxIDs)
		assert.Equal(t, resources.AllCommittedPosting(), finalDBResponse)
	})

	t.Run("reversal posting, only show outgoing cancelled and not incoming cancelled", func(t *testing.T) {
		req := &api.GetOpsSearchRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.AllReleasePosting()
		finalDBResponse, clientBatchTxIDs := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string(nil)}, clientBatchTxIDs)
		assert.Equal(t, []*storage.TransactionsData(nil), finalDBResponse)
	})

	t.Run("reversal posting, only show outgoing cancelled", func(t *testing.T) {
		req := &api.GetOpsSearchRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.FailedFASTPosting()
		finalDBResponse, clientBatchTxIDs := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"abc123efg"}}, clientBatchTxIDs)
		assert.Equal(t, []*storage.TransactionsData{resources.FailedFASTPosting()[0]}, finalDBResponse)
	})

	t.Run("committed-and-pending-outgoing-phase", func(t *testing.T) {
		req := &api.GetOpsSearchRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.CommittedAndPendingOutgoingPosting()
		finalDBResponse, clientBatchTxIDs := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"efg123abd", "efg1111abd"}}, clientBatchTxIDs)
		assert.Equal(t, responses.FilterTransactionsCommittedAndPendingOutgoingPostingResponse(), finalDBResponse)
	})

	t.Run("all-pending-incoming-phase", func(t *testing.T) {
		req := &api.GetOpsSearchRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.AllPendingIncomingPosting()
		finalDBResponse, clientBatchTxIDs := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string(nil)}, clientBatchTxIDs)
		assert.Equal(t, []*storage.TransactionsData(nil), finalDBResponse)
	})

	t.Run("committed-and-pending-incoming-phase", func(t *testing.T) {
		req := &api.GetOpsSearchRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.CommittedAndPendingIncomingPosting()
		finalDBResponse, clientBatchTxIDs := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"efg123abd"}}, clientBatchTxIDs)
		assert.Equal(t, responses.FilterTransactionsCommittedAndPendingIncomingPostingResponse(), finalDBResponse)
	})

	t.Run("all-posting-outgoing-phase", func(t *testing.T) {
		req := &api.GetOpsSearchRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.AllPendingOutgoingPosting()
		finalDBResponse, clientBatchTxIDs := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"efg123abd", "abc123efg", "efg1111abd"}}, clientBatchTxIDs)
		assert.Equal(t, resources.AllPendingOutgoingPosting(), finalDBResponse)
	})

	t.Run("interest_payout_filtering_reverse_order", func(t *testing.T) {
		req := &api.GetOpsSearchRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.InterestPayoutTransactionReverseOrderInput()
		finalDBResponse, clientBatchTxIDs := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"APPLY_ACCRUED_DEPOSIT_INTEREST_8880986487_5_APPLY_ACCRUED_DEPOSIT_INTEREST_1651248060000000000"}}, clientBatchTxIDs)
		assert.Equal(t, constants.CREDIT, finalDBResponse[0].DebitOrCredit)
		assert.Equal(t, "DEFAULT", finalDBResponse[0].AccountAddress)
	})

	t.Run("interest_payout_filtering_ordered_input", func(t *testing.T) {
		req := &api.GetOpsSearchRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.InterestPayoutTransactionOrderedInput()
		finalDBResponse, clientBatchTxIDs := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"APPLY_ACCRUED_DEPOSIT_INTEREST_8880986487_5_APPLY_ACCRUED_DEPOSIT_INTEREST_1651248060000000000"}}, clientBatchTxIDs)
		assert.Equal(t, constants.CREDIT, finalDBResponse[0].DebitOrCredit)
		assert.Equal(t, "DEFAULT", finalDBResponse[0].AccountAddress)
	})

	t.Run("pocket_transactions", func(t *testing.T) {
		req := &api.GetOpsSearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.OpsSearchPocketTransactions()
		finalDBResponse, _ := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, constants.DEBIT, finalDBResponse[0].DebitOrCredit)
		assert.Equal(t, "DEFAULT", finalDBResponse[0].AccountAddress)
	})
}

func TestOpsSearchFetchPaymentDetail(t *testing.T) {
	t.Run("empty-transactions-data", func(t *testing.T) {
		var clientBatchTxIDs []string
		request := &api.GetOpsSearchRequest{AccountID: "**********"}
		response := fetchOpsSearchPaymentDetail(context.Background(), clientBatchTxIDs, request)
		assert.Equal(t, map[storage.PaymentDataKey]*storage.PaymentDetail{}, response)
	})

	t.Run("no-matching-transaction", func(t *testing.T) {
		// mocking PaymentDetail Call
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockStorageDAO

		clientBatchTxIDs := []string{"abc123efg"}
		request := &api.GetOpsSearchRequest{AccountID: "**********"}
		response := fetchOpsSearchPaymentDetail(context.Background(), clientBatchTxIDs, request)
		assert.Equal(t, map[storage.PaymentDataKey]*storage.PaymentDetail{}, response)
	})

	t.Run("happy-path", func(t *testing.T) {
		// mocking PaymentDetail Call
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows()[:3], nil)
		storage.PaymentDetailD = mockStorageDAO

		expectedResponse := responses.FetchOpsSearchPaymentDetailHappyPath()
		clientBatchTxIDs := []string{"abc123efg", "efg123abd", "efg1111abd"}
		request := &api.GetOpsSearchRequest{AccountID: "**********"}
		response := fetchOpsSearchPaymentDetail(context.Background(), clientBatchTxIDs, request)
		assert.Equal(t, expectedResponse, response)
	})
}

func TestOpsSearchGetCustomerTxnRows(t *testing.T) {
	t.Run("happy-path: RPP txn subtype", func(t *testing.T) {
		txnData := resources.GetCustomerTxnRowsTxnData()[2:]
		paymentData := resources.GetCustomerTxnRowsPaymentDetailRPPData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
		var txnDetailsData transactionDetails
		txnDetailsData.paymentDetails = paymentData
		txnDetailsData.cardDetails = cardData

		expectedResponse := responses.FetchOpsSearchGetCustomerTxnRowsForRPPTxn()
		response := getCustomerAccountTxnRows(txnData, txnDetailsData)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path: intrabank txn subtype", func(t *testing.T) {
		txnData := resources.GetCustomerTxnRowsTxnData()[0:2]
		paymentData := resources.GetCustomerTxnRowsPaymentDetailIntrabankData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
		var txnDetailsData transactionDetails
		txnDetailsData.paymentDetails = paymentData
		txnDetailsData.cardDetails = cardData

		expectedResponse := responses.FetchOpsSearchGetCustomerTxnRowsForIntrabankTxn()
		response := getCustomerAccountTxnRows(txnData, txnDetailsData)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path: loan txn type", func(t *testing.T) {
		txnData := resources.GetCustomerTxnRowsTxnDataForLending()[:]
		paymentData := resources.GetCustomerTxnRowsPaymentDetailLendingData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
		loanData := resources.GetCustomerTxnRowsLoanDetailLendingData()
		var txnDetailsData transactionDetails
		txnDetailsData.paymentDetails = paymentData
		txnDetailsData.cardDetails = cardData
		txnDetailsData.loanDetails = loanData

		expectedResponse := responses.FetchOpsSearchGetCustomerTxnRowsForLendingTxn()
		response := getCustomerAccountTxnRows(txnData, txnDetailsData)
		assert.Equal(t, expectedResponse, response)
	})
}

func TestGetOpsSearchResponseGeneratorForDifferentTxnType(t *testing.T) {
	locale := utils.GetLocale()
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.OpsSearchPaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	mockStore := &storage.MockDatabaseStore{}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := NewGetOpsSearch(mockAccountServiceClient, mockAppConfig, mockStore)

	t.Run("happy-path: with rewards txn", func(t *testing.T) {
		dbRows := resources.OpsSearchTransactionsDataWithRewardsMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		assert.Equal(t, responses.GetOpsSearchFirstPageResponseWithRewardsForListTransaction(), response)
	})

	t.Run("happy-path: with qr payment txn", func(t *testing.T) {
		dbRows := resources.OpsSearchTransactionsDataWithQrPaymentMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData}
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		assert.Equal(t, responses.GetOpsSearchFirstPageResponseWithQrPaymentForListTransaction(), response)
	})

	t.Run("happy-path: with lending txn, searched by account id", func(t *testing.T) {
		dbRows := resources.OpsSearchTransactionsDataWithLendingMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
		loanData := resources.OpsSearchLoanDetailData()
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData, loanDetails: loanData}
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		assert.Equal(t, responses.GetOpsSearchFirstPageResponseWithLendingForListTransaction(), response)
	})

	t.Run("happy-path: with lending txn, searched by client batch id", func(t *testing.T) {
		dbRows := resources.OpsSearchTransactionsDataWithLendingByBatchIDMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
		loanData := resources.OpsSearchLoanDetailData()
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData, loanDetails: loanData}
		request := &api.GetOpsSearchRequest{
			BatchID:  "lending-txn-1",
			PageSize: 5,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		assert.Equal(t, responses.GetOpsSearchFirstPageResponseWithLendingForListByBatchIDTransaction(), response)
	})

	t.Run("happy-path: with BIZ txn", func(t *testing.T) {
		dbRows := resources.OpsSearchTransactionsDataWithBizMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		txnDetailsData := transactionDetails{paymentDetails: paymentData}
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		assert.Equal(t, responses.GetOpsSearchFirstPageResponseWithBizForListTransaction(), response)
	})

	t.Run("happy-path: with BIZ Lending txn", func(t *testing.T) {
		dbRows := resources.OpsSearchTransactionsDataWithBizLendingMockDBRows()
		paymentData := resources.OpsSearchPaymentDetailData()
		cardData := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
		loanData := resources.OpsSearchLoanDetailData()
		txnDetailsData := transactionDetails{paymentDetails: paymentData, cardDetails: cardData, loanDetails: loanData}
		request := &api.GetOpsSearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getOpsSearchResponseGenerator(context.Background(), dbRows, txnDetailsData, request, cursorData)
		assert.Equal(t, responses.GetOpsSearchFirstPageResponseWithBizLendingForListTransaction(), response)
	})
}

package handlerlogic

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/logic/helper"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetLendingTransactionsSearchImpl ...
type GetLendingTransactionsSearchImpl struct {
	AccountServiceClient            accountService.AccountService
	FeatureFlags                    config.FeatureFlags
	TransactionHistoryServiceConfig config.TransactionHistoryServiceConfig
}

// GetLendingTransactionSearchValidator validates the request parameters
func GetLendingTransactionSearchValidator(req *dto.TransactionHistorySearchRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.AccountID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "`accountID` is a mandatory field",
		})
	}

	if req.PageSize > MaxPageSize {
		errors = append(errors, servus.ErrorDetail{
			Message: "`pageSize` greater than maxPageSize",
		})
	}

	if req.PageSize < MinPageSize {
		errors = append(errors, servus.ErrorDetail{
			Message: "`pageSize` less than minPageSize",
		})
	}

	if !utils.IsValidTimeStampFormat(req.StartDate) {
		errors = append(errors, servus.ErrorDetail{
			Message: "startDateTimeStamp does not follow the format yyyy-mm-ddTHH:MM:SSZ",
		})
	}
	if !utils.IsValidTimeStampFormat(req.EndDate) {
		errors = append(errors, servus.ErrorDetail{
			Message: "endDateTimeStamp does not follow the format yyyy-mm-ddTHH:MM:SSZ",
		})
	}

	if req.Status != "" {
		if _, ok := utils.TxnStatusMap[req.Status]; !ok {
			errors = append(errors, servus.ErrorDetail{
				Message: "Invalid status",
			})
		}
	}
	if req.TransactionType != "" {
		if _, ok := utils.TxnTypeMap[req.TransactionType]; !ok {
			errors = append(errors, servus.ErrorDetail{
				Message: "Invalid transaction type",
			})
		}
	}
	if req.TransactionSubtype != "" {
		if _, ok := utils.TxnSubtypeMap[req.TransactionSubtype]; !ok {
			errors = append(errors, servus.ErrorDetail{
				Message: "Invalid transaction subtype",
			})
		}
	}
	if req.ProductVariantCode != "" {
		allowedProductVariants := []string{constants.FlexiLOCProductVariantCode, constants.FlexiBizLOCProductVariantCode}
		err := validateProductVariant(req.ProductVariantCode, allowedProductVariants...)
		if err != nil {
			errors = append(errors, *err)
		}
	}

	return errors
}

// GetAllTransactionsFromDBForLending : prepare filters, fetch transactions and format response
// nolint:dupl
func (g *GetLendingTransactionsSearchImpl) GetAllTransactionsFromDBForLending(ctx context.Context, req *dto.TransactionHistorySearchRequest) (*api.GetLendingTransactionSearchResponse, error) {
	if req.PageSize == MinPageSize {
		req.PageSize = DefaultPageSize
	}
	// parsing cursor if present
	cursorData, err := parsePaginationCursorData(req.StartingBefore, req.EndingAfter)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Error parsing pagination cursor, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Parsed cursor data: %s", utils.ToJSON(cursorData)))

	startTimeForFiltering := time.Now()
	transactions, err := helper.FetchTransactionsForCursor(ctx, req, &cursorData, g.TransactionHistoryServiceConfig, g.FeatureFlags)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Error getting normal transactions in search, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Successfully fetched transactions data: %s", utils.ToJSON(transactions)))

	// Fetching the recovery transactions
	recoveryTransactions, loanTxIDToLoanDetailMap, err := fetchRecoveryTransactions(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Error getting recovery transactions in search, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	// filtering recovery transactions based on date to avoid duplicate values
	recoveryTransactions = filterRecoveryTransactionBasedOnCursor(recoveryTransactions, cursorData)
	transactions = helper.MergeTransactions(transactions, recoveryTransactions)

	// when no transactions found
	if len(transactions) == 0 || err == data.ErrNoData {
		slog.FromContext(ctx).Info(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("No transactions found for the account :%s", req.AccountID))
		return emptyGetLendingTransactionSearchResponse(), nil
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Succesfully fetched data from DB : time :%s", time.Since(startTimeForFiltering)))

	response, err := g.getTransactionsResponseGenerator(ctx, transactions, req, cursorData, loanTxIDToLoanDetailMap)
	slog.FromContext(ctx).Info(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("time taken filter the DB response  :%s", time.Since(startTimeForFiltering)))
	if err != nil {
		return nil, err
	}
	return response, nil
}

// getTransactionsResponseGenerator contains logic to generate response from DB data after filtering.
func (g *GetLendingTransactionsSearchImpl) getTransactionsResponseGenerator(ctx context.Context, transactions []*storage.TransactionsData, req *dto.TransactionHistorySearchRequest, cursorData dto.PaginationCursor, loanDetails map[string]*storage.LoanDetail) (*api.GetLendingTransactionSearchResponse, error) {
	// fetch previous page last transaction to compare and remove duplicates.
	prevPageLastTxn, findErr := fetchPreviousPageLastTxDataIfCursorExist(ctx, cursorData, req)
	if findErr != nil {
		return nil, customErr.DefaultInternalServerError
	}

	filteredTransactions := filterTransactionsForLending(ctx, transactions, loanDetails, req, prevPageLastTxn)
	slog.FromContext(ctx).Info(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Filtered transactions data: %s", utils.ToJSON(filteredTransactions)))
	if len(filteredTransactions) == 0 {
		return emptyGetLendingTransactionSearchResponse(), nil
	}

	var transactionsList []api.LendingTransactionSearchData
	transactionsPerPage := utils.MinInt(int(req.PageSize), len(filteredTransactions))

	// iterating over all transactions
	minimumDBIDForPage := uint64(0) // Hacky solution to ensure cursor logic is not impact. TODO: fix it.
	transactionPage := getTransactionPage(filteredTransactions, transactionsPerPage, req)
	for _, transaction := range transactionPage {
		if transaction.ID != 0 {
			if minimumDBIDForPage == 0 {
				minimumDBIDForPage = transaction.ID
			} else if minimumDBIDForPage > transaction.ID {
				minimumDBIDForPage = transaction.ID
			}
		} else if transaction.ID == 0 {
			transaction.ID = minimumDBIDForPage
		}
		loanTxnID, err := helper.GetLoanTxnIDFromTransaction(transaction)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Error while extracting loanTransactionID from transaction, err: %s", err.Error()))
			return nil, customErr.DefaultInternalServerError
		}
		loanDetailData := loanDetails[loanTxnID]
		if loanDetailData == nil {
			slog.FromContext(ctx).Warn(constants.GetLendingTransactionSearchLogTag, fmt.Sprintf("Missing loan detail for a transaction, loanTransactionID: %s", loanTxnID))
			continue
		}
		txnDescription, txnLoanNames := generateTransactionDescription(loanDetailData)

		// format to response structure
		transactionsList = append(transactionsList, api.LendingTransactionSearchData{
			TransactionID:        transaction.ClientBatchID,
			Description:          txnDescription,
			LoanNames:            txnLoanNames,
			IconURL:              getIconURLByTransactionType(transaction),
			Amount:               getAmountWithSign(transaction, loanDetailData),
			Status:               getLendingTransactionStatus(loanDetailData),
			TransactionTimestamp: transaction.BatchValueTimestamp,
			TransactionType:      transaction.TransactionType,
			TransactionSubtype:   transaction.TransactionSubtype,
		})
	}

	paginationParams := utils.MapPaginationParameters(req.AccountID, req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, req.PageSize)
	links := buildPaginationLinksForLending(filteredTransactions, transactionPage, paginationParams, cursorData)
	return &api.GetLendingTransactionSearchResponse{Links: links, Data: transactionsList}, nil
}

// fetchPreviousPageLastTxDataIfCursorExist: fetch transaction data for the previous page last transaction if cursor exist to avoid duplicates
func fetchPreviousPageLastTxDataIfCursorExist(ctx context.Context, cursorData dto.PaginationCursor, req *dto.TransactionHistorySearchRequest) (*storage.TransactionsData, error) {
	if cursorData.TransactionID == "" {
		return nil, nil
	}
	txData, err := storage.TransactionsDataD.Find(ctx, []data.Condition{
		data.EqualTo("AccountID", req.AccountID), data.EqualTo("ClientBatchID", cursorData.TransactionID),
	}...)
	if err != nil && err != data.ErrNoData {
		return nil, err
	}
	if len(txData) == 0 {
		return nil, nil
	}
	return txData[0], nil
}

// filterTransactionsForLending filters out valid transactions
// nolint: unparam
func filterTransactionsForLending(ctx context.Context, dbResponse []*storage.TransactionsData, loanDetails map[string]*storage.LoanDetail, req *dto.TransactionHistorySearchRequest, prevPageLastTxn *storage.TransactionsData) []*storage.TransactionsData {
	prevPageLastTxnLoanTransactionID := helper.ExtractPrevPageLastTxnLoanTransactionID(prevPageLastTxn)

	// Filter out all historic LINE_OF_CREDIT transactions without loanTransactionID
	filteredTransactions := helper.FilterHistoricTransactionsAndUpdateClientBatchID(dbResponse, prevPageLastTxnLoanTransactionID)

	filteredTransactions = helper.FilterDefaultOrDebitTransactions(filteredTransactions)

	// Filter out transaction based on status
	if req.Status != "" {
		filteredTransactions = FilterTransactionBasedOnStatus(filteredTransactions, loanDetails, req.Status)
	}

	if req.EndingAfter != "" {
		if len(filteredTransactions) > int(req.PageSize) {
			filteredTransactions = invertDBResponseForBackwardScrolling(filteredTransactions)
		}
	}

	// Storing clientBatchID to maintain the order in which fetched from DB
	clientBatchIDOrder, clientBatchIDToTransactionsDataMap := createClientBatchIDToTransactionsDataMap(filteredTransactions)

	for clientBatchID, transactions := range clientBatchIDToTransactionsDataMap {
		// Picking a valid transaction, out of the transactions for that clientBatchID
		transactionsData := helper.FilterInvalidTransactions(transactions)
		clientBatchIDToTransactionsDataMap[clientBatchID] = []*storage.TransactionsData{}

		if transactionsData != nil {
			clientBatchIDToTransactionsDataMap[clientBatchID] = []*storage.TransactionsData{transactionsData}
		}
	}

	// computing number of rows to be added
	filteredRowsCount := computeNumberOfTransactionsInFilteredDBResponse(req.StartingBefore, req.EndingAfter, req.PageSize)

	// Select transactions as per required pageSize
	finalTransactionsList := createFinalDBLendingTransactionList(clientBatchIDOrder, clientBatchIDToTransactionsDataMap, filteredRowsCount)

	return finalTransactionsList
}

func fetchRecoveryTransactions(ctx context.Context, req *dto.TransactionHistorySearchRequest) ([]*storage.TransactionsData, map[string]*storage.LoanDetail, error) {
	var recoveryTransactions []*storage.TransactionsData
	loanTxIDToLoanDetailMap, err := helper.FetchLoanDetails(ctx, req.AccountID)
	if err != nil {
		return nil, nil, err
	}
	for _, loanDetail := range loanTxIDToLoanDetailMap {
		if loanDetail.TransactionType != constants.RepaymentTransactionType {
			continue
		}
		_, repaymentDetails, detailsErr := getDrawdownAndRepaymentDetailsDTO(loanDetail)
		if detailsErr != nil {
			return nil, nil, detailsErr
		}
		var hasWriteOffRepayment bool
		for _, repaymentDetail := range repaymentDetails.LoanRepaymentDetail {
			if repaymentDetail.Status == constants.WrittenOffStatus {
				hasWriteOffRepayment = true
				break
			}
		}
		if hasWriteOffRepayment {
			transaction, txnErr := helper.CreateTransactionFromLoanDetailForWriteOff(loanDetail, req.AccountID, repaymentDetails)
			if txnErr != nil {
				return nil, nil, txnErr
			}
			recoveryTransactions = append(recoveryTransactions, transaction)
		}
	}
	return recoveryTransactions, loanTxIDToLoanDetailMap, nil
}

// filterRecoveryTransactionBasedOnCursor: method to filter recovery transactions based on cursor date(** of the prev page tx**)
func filterRecoveryTransactionBasedOnCursor(recoveryTransactions []*storage.TransactionsData, cursorData dto.PaginationCursor) []*storage.TransactionsData {
	if cursorData.Date == "" {
		return recoveryTransactions // first page query
	}
	cursorDateTimestamp, _ := time.Parse(time.RFC3339, cursorData.Date)
	var filteredRecoveryTx []*storage.TransactionsData
	for _, recoveryTransaction := range recoveryTransactions {
		// since txs are shown in descending order
		if recoveryTransaction.BatchValueTimestamp.Before(cursorDateTimestamp) {
			filteredRecoveryTx = append(filteredRecoveryTx, recoveryTransaction)
		}
		// TODO: Evaluate the condition for equality.
	}

	return filteredRecoveryTx
}

// generateTransactionDescription returns the transaction description based on drawdown or repayment
func generateTransactionDescription(loanDetailData *storage.LoanDetail) (string, []string) {
	var txnDescription string
	var txnLoanNames []string
	if loanDetailData.TransactionType == constants.DrawdownTransactionType {
		var drawDownDetails dto.DrawdownDetailsDTO
		_ = json.Unmarshal(loanDetailData.DisbursementDetail, &drawDownDetails)
		txnDescription = fmt.Sprintf("Drawdown for %v", drawDownDetails.LoanName)
		txnLoanNames = append(txnLoanNames, drawDownDetails.LoanName)
		return txnDescription, txnLoanNames
	}
	var repaymentDetails dto.RepaymentDetailsDTO
	_ = json.Unmarshal(loanDetailData.RepaymentDetail, &repaymentDetails)
	for _, repaymentDetail := range repaymentDetails.LoanRepaymentDetail {
		txnLoanNames = append(txnLoanNames, repaymentDetail.LoanName)
	}
	if len(txnLoanNames) == 1 {
		txnDescription = fmt.Sprintf("Payment for %v", txnLoanNames[0])
	} else {
		txnDescription = "Payment for drawdowns"
	}
	return txnDescription, txnLoanNames
}

// emptyGetLendingTransactionSearchResponse will generate empty response structure in case no matching transactions are found
func emptyGetLendingTransactionSearchResponse() *api.GetLendingTransactionSearchResponse {
	response := &api.GetLendingTransactionSearchResponse{}
	response.Links = map[string]string{"next": "", "nextCursorID": ""}
	response.Data = []api.LendingTransactionSearchData{}
	return response
}

// FilterTransactionBasedOnStatus ... method to filter transactions based on requested transaction status
func FilterTransactionBasedOnStatus(transactions []*storage.TransactionsData, loanDetails map[string]*storage.LoanDetail, status string) []*storage.TransactionsData {
	var filteredTransactions []*storage.TransactionsData
	if status != "" {
		for _, transaction := range transactions {
			var details map[string]string
			if transaction.TransactionDetails != nil {
				_ = json.Unmarshal(transaction.TransactionDetails, &details)
			}
			loanTxnID := details["loanTransactionID"]
			if loanTxnID == "" {
				if transaction.BatchDetails != nil {
					_ = json.Unmarshal(transaction.BatchDetails, &details)
				}
			}
			loanTxnID = details["loanTransactionID"]
			loanDetailData := loanDetails[loanTxnID]
			if loanDetailData == nil {
				continue
			}
			trxStatus := getLendingTransactionStatus(loanDetailData)
			if status != utils.TxnStatusMap[trxStatus] {
				continue
			}
			filteredTransactions = append(filteredTransactions, transaction)
		}
		return filteredTransactions
	}
	return filteredTransactions
}

// getTransactionPage selects the transaction of page based on pagesize and cursor  , boundary checks are done to avoid any out of array bound access
func getTransactionPage(filteredTransactions []*storage.TransactionsData, transactionsPerPage int, req *dto.TransactionHistorySearchRequest) []*storage.TransactionsData {
	transactionPage := filteredTransactions
	if len(filteredTransactions) > transactionsPerPage {
		transactionPage = filteredTransactions[:transactionsPerPage]
	}
	// clicked on prev link this page will have recent transactions which we get from back of the array.
	if req.EndingAfter != "" {
		// for going backward towards latest transactions select last n transactions in array
		if len(filteredTransactions) < transactionsPerPage {
			transactionPage = filteredTransactions
		} else {
			transactionPage = filteredTransactions[len(filteredTransactions)-transactionsPerPage:]
		}
	}
	return transactionPage
}

// createFinalDBLendingTransactionList creates final list of transactions to be shown
func createFinalDBLendingTransactionList(clientBatchIDOrder []string,
	clientBatchIDToTransactionsDataMap map[string][]*storage.TransactionsData, filteredRowsCount int64) []*storage.TransactionsData {
	// Select transactions as per required pageSize
	var finalDBResponse []*storage.TransactionsData
	for _, item := range clientBatchIDOrder {
		if utils.MustConvertToInt64(len(finalDBResponse)) < filteredRowsCount {
			txDataList := clientBatchIDToTransactionsDataMap[item]
			if len(txDataList) != 0 {
				finalDBResponse = append(finalDBResponse, txDataList[0])
			}
		}
	}

	sort.Slice(finalDBResponse, func(i, j int) bool {
		return finalDBResponse[i].BatchValueTimestamp.After(finalDBResponse[j].BatchValueTimestamp)
	})

	return finalDBResponse
}

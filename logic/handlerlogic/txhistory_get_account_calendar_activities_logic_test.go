package handlerlogic

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
	statementMock "gitlab.myteksi.net/dbmy/transaction-statements/api/mock"
)

var mockCheckStatementStatusCtx = func(ctx context.Context, enabled bool) context.Context {
	flagRepo := &featureflag.MockRepo{}
	flagRepo.On("IsCheckStatementStatusEnabled").Return(enabled)
	return featureflag.NewContextWithFeatureFlags(ctx, flagRepo)
}

// Test case for GetAccountCalendarActivities
func TestGetAccountCalendarActivities(t *testing.T) {
	ctx := mockCheckStatementStatusCtx(context.Background(), true)
	accountList := []string{"12345", "********"}
	pastMonthsThresholdForCalendarActivity := 12
	t.Run("happy-path", func(t *testing.T) {
		now := time.Date(2022, 11, 1, 0, 0, 0, 0, time.UTC)
		mockDBResponse := resources.AccountCalendarActivityDBSampleResponse()
		expectedResponse := []api.Date{
			{
				Year:   "2022",
				Months: []string{"11", "06", "02", "01"},
			}, {
				Year:   "2021",
				Months: []string{"11"},
			},
		}

		// storage call mock
		mockStorageDAO := &storage.MockIAccountCalendarActivityDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		storage.AccountCalendarActivityD = mockStorageDAO
		response, err := GetAccountCalendarActivities(ctx, accountList, now, pastMonthsThresholdForCalendarActivity)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("no-resource-path", func(t *testing.T) {
		var mockDBResponse []*storage.AccountCalendarActivity

		// storage call mock
		mockStorageDAO := &storage.MockIAccountCalendarActivityDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		storage.AccountCalendarActivityD = mockStorageDAO
		response, err := GetAccountCalendarActivities(ctx, accountList, time.Now(), pastMonthsThresholdForCalendarActivity)
		assert.Nil(t, err)
		assert.Equal(t, []api.Date{}, response)
	})

	t.Run("error-path", func(t *testing.T) {
		var mockDBResponse []*storage.AccountCalendarActivity

		mockError := errors.New("some random error")

		// storage call mock
		mockStorageDAO := &storage.MockIAccountCalendarActivityDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, mockError)
		storage.AccountCalendarActivityD = mockStorageDAO
		response, err := GetAccountCalendarActivities(ctx, accountList, time.Now(), pastMonthsThresholdForCalendarActivity)
		assert.Error(t, err, customErr.DefaultInternalServerError)
		assert.Equal(t, []api.Date(nil), response)
	})
}

func TestMapCalendarActivityToResponse(t *testing.T) {
	pastMonthsThresholdForCalendarActivity := 12
	t.Run("happy path - calendar activity for single account  outside 12 months range ", func(t *testing.T) {
		now := time.Date(2022, 5, 1, 0, 0, 0, 0, time.UTC)
		matchResult := []*storage.AccountCalendarActivity{
			{
				ID:        3,
				AccountID: "12345",
				Year:      2021,
				Months:    "03, 02, 01",
				UpdatedAt: time.Now(),
			},
		}
		expected := []api.Date{}
		actual := mapCalendarActivityToResponse(matchResult, now, pastMonthsThresholdForCalendarActivity)
		assert.Equal(t, expected, actual)
	})
	t.Run("happy path - calendar activity for multiple accounts outside 12 months range", func(t *testing.T) {
		now := time.Date(2022, 5, 1, 0, 0, 0, 0, time.UTC)
		matchResult := []*storage.AccountCalendarActivity{
			{
				ID:        4,
				AccountID: "12345",
				Year:      2021,
				Months:    "03, 02, 01",
				UpdatedAt: time.Now(),
			}, {
				ID:        3,
				AccountID: "********",
				Year:      2021,
				Months:    "04",
				UpdatedAt: time.Now(),
			},
		}
		expected := []api.Date{}
		actual := mapCalendarActivityToResponse(matchResult, now, pastMonthsThresholdForCalendarActivity)
		assert.Equal(t, expected, actual)
	})
}

func TestFilterAccountCalanderActivities(t *testing.T) {
	accountID := "12345"
	config.SetStatementReadyByDays(1)
	ctx := mockCheckStatementStatusCtx(context.Background(), true)
	mockStatement := &statementMock.TransactionStatements{}
	Dependencies.TransactionStatementsClient = mockStatement
	t.Run("happy path - calendar activity  for single account in current month", func(t *testing.T) {
		now := time.Date(2022, 5, 1, 0, 0, 0, 0, time.UTC)
		response := []api.Date{
			{
				Year:   "2022",
				Months: []string{"05"},
			},
		}

		expected := []api.Date{}
		// Register the mock dependencies
		mockStatement.On("GetAccountTransactionsStatementMetadata", mock.Anything, mock.Anything).Return(nil, nil)

		actual := FilterAccountCalanderActivities(ctx, response, now, accountID, false)
		assert.Equal(t, expected, actual)
	})

	t.Run("happy path - calendar activity for single account in multiple months from current year including current month", func(t *testing.T) {
		now := time.Date(2022, 5, 1, 0, 0, 0, 0, time.UTC)
		response := []api.Date{
			{
				Year:   "2022",
				Months: []string{"05", "04", "03", "01"},
			},
		}
		expected := []api.Date{
			{
				Year:   "2022",
				Months: []string{"04", "03", "01"},
			},
		}
		mockStatement.On("GetAccountTransactionsStatementMetadata", mock.Anything, mock.Anything).Return(nil, nil)

		actual := FilterAccountCalanderActivities(ctx, response, now, accountID, false)
		assert.Equal(t, expected, actual)
	})

	t.Run("happy path - calendar activity for single account in current year excluding current month", func(t *testing.T) {
		now := time.Date(2022, 5, 1, 0, 0, 0, 0, time.UTC)
		response := []api.Date{
			{
				Year:   "2022",
				Months: []string{"04", "03", "02"},
			},
		}
		expected := []api.Date{
			{
				Year:   "2022",
				Months: []string{"04", "03", "02"},
			},
		}
		mockStatement.On("GetAccountTransactionsStatementMetadata", mock.Anything, mock.Anything).Return(nil, nil)

		actual := FilterAccountCalanderActivities(ctx, response, now, accountID, false)
		assert.Equal(t, expected, actual)
	})

	t.Run("happy path - calendar activity for single account within 12 months range", func(t *testing.T) {
		now := time.Date(2022, 5, 1, 0, 0, 0, 0, time.UTC)
		response := []api.Date{
			{
				Year:   "2022",
				Months: []string{"02", "01"},
			}, {
				Year:   "2021",
				Months: []string{"11", "10", "05"},
			},
		}
		expected := []api.Date{
			{
				Year:   "2022",
				Months: []string{"02", "01"},
			}, {
				Year:   "2021",
				Months: []string{"11", "10", "05"},
			},
		}
		mockStatement.On("GetAccountTransactionsStatementMetadata", mock.Anything, mock.Anything).Return(nil, nil)

		actual := FilterAccountCalanderActivities(ctx, response, now, accountID, false)
		assert.Equal(t, expected, actual)
	})

	t.Run("happy path - calendar activity for single account", func(t *testing.T) {
		now := time.Date(2022, 5, 1, 0, 0, 0, 0, time.UTC)
		response := []api.Date{
			{
				Year:   "2022",
				Months: []string{"02", "01"},
			}, {
				Year:   "2021",
				Months: []string{"11", "10", "05"},
			},
		}
		expected := []api.Date{
			{
				Year:   "2022",
				Months: []string{"02", "01"},
			}, {
				Year:   "2021",
				Months: []string{"11", "10", "05"},
			},
		}
		mockStatement.On("GetAccountTransactionsStatementMetadata", mock.Anything, mock.Anything).Return(nil, nil)

		actual := FilterAccountCalanderActivities(ctx, response, now, accountID, false)
		assert.Equal(t, expected, actual)
	})

	t.Run("happy path - calendar activity only in last year for single accounts within 12 months range", func(t *testing.T) {
		now := time.Date(2022, 5, 1, 0, 0, 0, 0, time.UTC)
		response := []api.Date{
			{
				Year:   "2021",
				Months: []string{"11", "10", "05"},
			},
		}
		expected := []api.Date{
			{
				Year:   "2021",
				Months: []string{"11", "10", "05"},
			},
		}
		mockStatement.On("GetAccountTransactionsStatementMetadata", mock.Anything, mock.Anything).Return(nil, nil)

		actual := FilterAccountCalanderActivities(ctx, response, now, accountID, false)
		assert.Equal(t, expected, actual)
	})

	t.Run("happy path - calendar activity  for single account in multiple month with statement generated", func(t *testing.T) {
		now := time.Date(2022, 2, 1, 0, 0, 0, 0, time.UTC)
		response := []api.Date{
			{
				Year:   "2022",
				Months: []string{"01"},
			},
			{
				Year:   "2021",
				Months: []string{"12"},
			},
		}
		expected := []api.Date{
			{
				Year:   "2022",
				Months: []string{"01"},
			},
			{
				Year:   "2021",
				Months: []string{"12"},
			},
		}
		mockStatement.On("GetAccountTransactionsStatementMetadata", mock.Anything, mock.Anything).Return(nil, nil)

		actual := FilterAccountCalanderActivities(ctx, response, now, accountID, false)
		assert.Equal(t, expected, actual)
	})
}

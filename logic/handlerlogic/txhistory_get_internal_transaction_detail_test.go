package handlerlogic

import (
	"context"
	"errors"
	"os"
	"testing"

	"gitlab.myteksi.net/dakota/common/tenants"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

func TestGetInternalTransactionDetailsValidator(t *testing.T) {
	t.Run("accountID-parameter-missing", func(t *testing.T) {
		request := &api.GetInternalTransactionDetailRequest{
			TransactionID: "sdgagsdgs",
		}
		expectResponse := servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		}

		errorResponse := GetInternalTransactionDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})

	t.Run("transactionID-parameter-missing", func(t *testing.T) {
		request := &api.GetInternalTransactionDetailRequest{
			AccountID: "12345",
		}
		expectResponse := servus.ErrorDetail{
			Message: "'transactionID' is a mandatory parameter.",
		}
		errorResponse := GetInternalTransactionDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})

	t.Run("valid-request", func(t *testing.T) {
		request := &api.GetInternalTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "14143-SGSG-124",
		}
		errorResponse := GetInternalTransactionDetailRequestValidator(request)
		assert.Equal(t, 0, len(errorResponse))
	})

	t.Run("both-transactionID-and-batchID-parameters-passed-in", func(t *testing.T) {
		request := &api.GetInternalTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "14143-SGSG-124",
			BatchID:       "123456",
		}
		expectResponse := servus.ErrorDetail{
			Message: "Request is invalid. Only 'transactionID' or 'batchID' may be used.",
		}
		errorResponse := GetInternalTransactionDetailRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})
}

func TestGetInternalDetailForNonResidentTransfer(t *testing.T) {
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("Non-resident transfer internal details", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransferNonResidentTransactions()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData
		mockPaymentTxnDetailStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockPaymentTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferNonResidentTrxDetails(), nil)
		storage.PaymentDetailD = mockPaymentTxnDetailStorageDAO
		mockCardTxDao := &storage.MockICardTransactionDetailDAO{}
		mockCardTxDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		storage.CardTransactionDetailD = mockCardTxDao

		response, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.NoError(t, err)
		counterPartyTransactionDetails := response.CounterParty.TransactionDetails
		assert.Equal(t, "2", counterPartyTransactionDetails["residentStatus"])
		assert.Equal(t, "16830", counterPartyTransactionDetails["purposeCode"])
		assert.Equal(t, "MY", counterPartyTransactionDetails["beneficiaryCountry"])
		assert.Equal(t, "NR", counterPartyTransactionDetails["relationshipCode"])
	})
}

func TestGetInternalDetailForSpendCardReversal(t *testing.T) {
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("Type SPEND_CARD_ATM_REVERSAL internal details", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.SpendCardATMReversalTransactions()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData
		mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
		mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardATMReversalTrxDetails(), nil)
		storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, "ATM withdrawal reversal", response.TransactionDescription)
		assert.NoError(t, err)
	})

	t.Run("Type SPEND_CARD_PRESENT_REVERSAL internal details", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.SpendCardPresentReversalTransactions()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData
		mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
		mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardPresentReversalTrxDetails(), nil)
		storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, "Refund from Amazon", response.TransactionDescription)
		assert.NoError(t, err)
	})

	t.Run("Type SPEND_CARD_NOT_PRESENT_REVERSAL internal details", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.SpendCardNotPresentReversalTransactions()[0]}, nil)
		storage.TransactionsDataD = mockTransactionData
		mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
		mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardNotPresentReversalTrxDetails(), nil)
		storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, "Refund from Amazon", response.TransactionDescription)
		assert.NoError(t, err)
	})
}

func TestGetInternalTransactionDetail(t *testing.T) {
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("resource-present-in-DB", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "abc123efg",
		}
		expectedResp := responses.GetInternalTransactionDetailResponse()[0]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[3]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[0]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("no-resource-present-in-DB", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, (*api.GetInternalTransactionDetailResponse)(nil), actualResp)
		assert.Error(t, customErr.BuildErrorResponse(customErr.ResourceNotFound, "No data present in transactionDataDB"), err)
	})
	t.Run("error-on-fetching-from-DB", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "**********",
			TransactionID: "abcd-efgh-ijkl",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		expectedErr := customErr.DefaultInternalServerError
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})

	t.Run("empty-finalDb-response", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID:     "12345",
			TransactionID: "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		}
		expectedResp := &api.GetInternalTransactionDetailResponse{}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.PendingIncomingTransactions(), nil)
		storage.TransactionsDataD = mockTransactionData
		actualResp, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})

	t.Run("resource-present-in-DB with batchID", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID: "12345",
			BatchID:   "abc123efg",
		}
		expectedResp := responses.GetInternalTransactionDetailResponse()[6]

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[3]}, nil)
		storage.TransactionsDataD = mockTransactionData

		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[10]}, nil)
		storage.PaymentDetailD = mockPaymentDao

		response, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("no-resource-present-in-DB with batchID", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID: "12345",
			BatchID:   "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, (*api.GetInternalTransactionDetailResponse)(nil), actualResp)
		assert.Error(t, customErr.BuildErrorResponse(customErr.ResourceNotFound, "No data present in transactionDataDB"), err)
	})
	t.Run("error-on-fetching-from-DB with batchID", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID: "**********",
			BatchID:   "abcd-efgh-ijkl",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, errors.New("random error message"))
		expectedErr := customErr.DefaultInternalServerError
		storage.TransactionsDataD = mockTransactionData

		actualResp, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})

	t.Run("empty-finalDb-response with batchID", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID: "12345",
			BatchID:   "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		}
		expectedResp := &api.GetInternalTransactionDetailResponse{}

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.PendingIncomingTransactions(), nil)
		storage.TransactionsDataD = mockTransactionData
		actualResp, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})

	t.Run("internal txn card detail with batch ID", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID: "12345",
			BatchID:   "abc123efg",
		}
		expectedResp := responses.GetInternalTransactionDetailResponse()[7]

		mockCardTransactionDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardTransactionDetail.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransactionDetail{resources.CardTransactionDetailSample()}, nil)
		storage.CardTransactionDetailD = mockCardTransactionDetail

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[9]}, nil)
		storage.TransactionsDataD = mockTransactionData

		response, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("internal txn card detail without batch ID", func(t *testing.T) {
		req := &api.GetInternalTransactionDetailRequest{
			AccountID: "12345",
			BatchID:   "",
		}
		expectedResp := responses.GetInternalTransactionDetailResponse()[7]

		mockCardTransactionDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardTransactionDetail.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransactionDetail{resources.CardTransactionDetailSample()}, nil)
		storage.CardTransactionDetailD = mockCardTransactionDetail

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{resources.TransactionsMockDBRows()[9]}, nil)
		storage.TransactionsDataD = mockTransactionData

		response, err := g.GetInternalTransactionDetail(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
}

func TestGetInternalTransactionDetailsResponseGenerator(t *testing.T) {
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}

	txnData := resources.TransactionsMockDBRows()

	req := &api.GetInternalTransactionDetailRequest{
		AccountID:     "12345",
		TransactionID: "9007e9c10ea841c593bee2e65df599ed",
	}

	t.Run("payment-transaction", func(t *testing.T) {
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[0]}, nil)
		storage.PaymentDetailD = mockPaymentDao
		response, _ := g.getInternalTransactionDetailsResponseGenerator(context.Background(), txnData[3], req, "abc123efg")
		assert.Equal(t, responses.GetInternalTransactionDetailResponse()[0], response)
	})

	t.Run("payment-transaction-processing", func(t *testing.T) {
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[9]}, nil)
		storage.PaymentDetailD = mockPaymentDao
		response, _ := g.getInternalTransactionDetailsResponseGenerator(context.Background(), txnData[3], req, "abc123efg")
		assert.Equal(t, responses.GetInternalTransactionDetailResponse()[5], response)
	})

	t.Run("non-payment-transaction", func(t *testing.T) {
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentDataMockDBRows()[3]}, nil)
		storage.PaymentDetailD = mockPaymentDao
		response, _ := g.getInternalTransactionDetailsResponseGenerator(context.Background(), txnData[0], req, "abc123efg")
		assert.Equal(t, responses.GetInternalTransactionDetailResponse()[4], response)
	})

	t.Run("failed-get-externalTransactionID", func(t *testing.T) {
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[8]}, nil)
		storage.PaymentDetailD = mockPaymentDao
		response, err := g.getInternalTransactionDetailsResponseGenerator(context.Background(), txnData[3], req, "abc123efg")
		assert.Error(t, customErr.BuildErrorResponse(customErr.ResourceNotFound, "Failed to get externalTransactionID"), err)
		assert.Equal(t, (*api.GetInternalTransactionDetailResponse)(nil), response)
	})

	t.Run("biz-transaction", func(t *testing.T) {
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[0]}, nil)
		storage.PaymentDetailD = mockPaymentDao
		response, _ := g.getInternalTransactionDetailsResponseGenerator(context.Background(), txnData[14], req, "abc123efg")
		assert.Equal(t, responses.GetInternalTransactionDetailResponse()[0], response)
	})
}

func TestGetInternalTransactionDetailsResponseForReceipt(t *testing.T) {
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	g := GetTransactionDetailStruct{AccountServiceClient: mockAccountServiceClient}

	txnData := resources.TransactionsMockDBRows()

	req := &api.GetInternalTransactionDetailRequest{
		AccountID: "54321",
		BatchID:   "9007e9c10ea841c593bee2e65df599ed",
	}

	t.Run("payment-transaction", func(t *testing.T) {
		mockPaymentDao := &storage.MockIPaymentDetailDAO{}
		mockPaymentDao.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{resources.PaymentRppDataMockDBRows()[10]}, nil)
		storage.PaymentDetailD = mockPaymentDao
		response, _ := g.getInternalTransactionDetailsResponseForReceipt(context.Background(), txnData[3], req, "abc123efg")
		assert.Equal(t, responses.GetInternalTransactionDetailResponse()[6], response)
	})
}

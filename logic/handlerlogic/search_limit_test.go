package handlerlogic

import (
	"context"
	"testing"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/featureflag"

	"gitlab.myteksi.net/dakota/transaction-history/storage"

	"github.com/stretchr/testify/assert"
)

var timeToPointer = func(inputTIme time.Time) *time.Time {
	return &inputTIme
}

func TestCreateTransactionLimit(t *testing.T) {
	scenarios := []struct {
		name                string
		endDate             time.Time
		durationInMonths    int
		expectedSearchLimit SearchTransactionLimit
	}{
		{
			name:                "should return EmptySearchTransactionLimitFilter when durationInMonths less or equal than 0",
			endDate:             time.Now(),
			durationInMonths:    0,
			expectedSearchLimit: EmptySearchTransactionLimitFilter,
		},
		{
			name:             "should limit transactions from 1st March when the today is 19th Aug and client can only query the transactions in 6 months",
			endDate:          time.Date(2024, 8, 19, 0, 0, 0, 0, storage.TimeZoneLocation),
			durationInMonths: 6,
			expectedSearchLimit: SearchTransactionLimit{
				MinStartDate: timeToPointer(time.Date(2024, 3, 1, 0, 0, 0, 0, storage.TimeZoneLocation)),
			},
		},
		{
			name:             "should limit transactions from 1st Aug when the today is 19th Aug and client can only query the transactions in 1 month",
			endDate:          time.Date(2024, 8, 19, 0, 0, 0, 0, storage.TimeZoneLocation),
			durationInMonths: 1,
			expectedSearchLimit: SearchTransactionLimit{
				MinStartDate: timeToPointer(time.Date(2024, 8, 1, 0, 0, 0, 0, storage.TimeZoneLocation)),
			},
		},
	}

	for i := range scenarios {
		scenario := scenarios[i]
		t.Run(scenario.name, func(t *testing.T) {
			searchLimit := BuildSearchTransactionLimit(scenario.endDate, scenario.durationInMonths)
			assert.Equal(t, scenario.expectedSearchLimit, searchLimit)
		})
	}
}

func TestOverwriteStartingDate(t *testing.T) {
	scenarios := []struct {
		name              string
		searchLimitFilter SearchTransactionLimit
		ffSetup           func(*featureflag.MockRepo)
		startingDate      string
		shouldOverwrite   bool
	}{
		{
			name:            "should not overwrite startingDate if no featureFlag provided",
			shouldOverwrite: false,
		},
		{
			name: "should not overwrite startingDate if NoSearchTransactionLimit provided",
			ffSetup: func(repo *featureflag.MockRepo) {
				repo.On("IsTransactionsSearchDurationLimitEnabled").Return(true)
			},
			searchLimitFilter: EmptySearchTransactionLimitFilter,
			shouldOverwrite:   false,
		},
		{
			name:              "should not overwrite startingDate if CASASearchLimitEnabled is false",
			searchLimitFilter: EmptySearchTransactionLimitFilter,
			ffSetup: func(repo *featureflag.MockRepo) {
				repo.On("IsTransactionsSearchDurationLimitEnabled").Return(false)
			},
			shouldOverwrite: false,
		},
		{
			name: "should not overwrite startingDate if startingDate >= SearchTransactionLimit.MinStartDate",
			searchLimitFilter: SearchTransactionLimit{
				MinStartDate: timeToPointer(time.Date(2024, 8, 19, 0, 0, 0, 0, storage.TimeZoneLocation)),
			},
			startingDate: "2024-08-19T00:00:00.999",
			ffSetup: func(repo *featureflag.MockRepo) {
				repo.On("IsTransactionsSearchDurationLimitEnabled").Return(true)
			},
			shouldOverwrite: false,
		},
		{
			name: "should overwrite startingDate if startingDate < SearchTransactionLimit.MinStartDate",
			searchLimitFilter: SearchTransactionLimit{
				MinStartDate: timeToPointer(time.Date(2024, 8, 19, 0, 0, 0, 0, storage.TimeZoneLocation)),
			},
			startingDate: "2024-08-18T00:00:00.999",
			ffSetup: func(repo *featureflag.MockRepo) {
				repo.On("IsTransactionsSearchDurationLimitEnabled").Return(true)
			},
			shouldOverwrite: true,
		},
		{
			name: "should overwrite startingDate if startingDate is empty",
			searchLimitFilter: SearchTransactionLimit{
				MinStartDate: timeToPointer(time.Date(2024, 8, 19, 0, 0, 0, 0, storage.TimeZoneLocation)),
			},
			startingDate: "",
			ffSetup: func(repo *featureflag.MockRepo) {
				repo.On("IsTransactionsSearchDurationLimitEnabled").Return(true)
			},
			shouldOverwrite: true,
		},
	}
	for i := range scenarios {
		scenario := scenarios[i]
		t.Run(scenario.name, func(t *testing.T) {
			ctx := context.Background()
			if scenario.ffSetup != nil {
				featureFlag := &featureflag.MockRepo{}
				scenario.ffSetup(featureFlag)
				ctx = featureflag.NewContextWithFeatureFlags(ctx, featureFlag)
			}
			assert.Equal(t, scenario.shouldOverwrite, scenario.searchLimitFilter.ShouldOverrideStartingDate(ctx, scenario.startingDate))
		})
	}
}

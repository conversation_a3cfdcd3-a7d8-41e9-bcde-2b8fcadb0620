package handlerlogic

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/require"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"

	"gitlab.myteksi.net/dakota/common/tenants"

	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"

	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"

	"gitlab.myteksi.net/dakota/transaction-history/constants"

	"github.com/stretchr/testify/mock"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
)

func TestGetCxTransactionListValidator(t *testing.T) {
	t.Run("accountID-parameter-missing", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "`accountID` is a mandatory field",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`accountID` is a mandatory field"}},
		}

		errorResponse := GetCxTransactionListValidator(request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("accountID-parameter-invalid", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "************",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "Invalid `accountID`",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "Invalid `accountID`"}},
		}
		errorResponse := GetCxTransactionListValidator(request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("happy-path", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "*************",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		errorResponse := GetCxTransactionListValidator(request)
		assert.Equal(t, nil, errorResponse)
	})

	t.Run("page-size-negative", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "*************",
			PageSize:  -1,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "`pageSize` less than minPageSize",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`pageSize` less than minPageSize"}},
		}

		errorResponse := GetCxTransactionListValidator(request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("page-size-greater-than-max", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "*************",
			PageSize:  MaxPageSize + 1,
		}

		expectedError := servus.ServiceError{
			Code:     string(customErr.BadRequest),
			Message:  "`pageSize` greater than maxPageSize",
			HTTPCode: customErr.BadRequest.HTTPStatusCode(),
			Errors:   []servus.ErrorDetail{{Message: "`pageSize` greater than maxPageSize"}},
		}

		errorResponse := GetCxTransactionListValidator(request)
		assert.Equal(t, expectedError, errorResponse)
	})

	t.Run("date-time-input-request", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "*************",
			PageSize:  2,
			StartDate: "2022-10-01T00:00:00Z",
			EndDate:   "2022-10-12T15:00:00Z",
		}
		errorResponse := GetCxTransactionListValidator(request)
		assert.Equal(t, nil, errorResponse)
	})
}

func TestGetCxTransactionsResponseGenerator(t *testing.T) {
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailForCxMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO
	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetails", mock.Anything, mock.Anything).Return(nil, nil, nil)
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
	g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("happy-path-next-page-exist", func(t *testing.T) {
		dbRows := resources.CxTransactionsDataMockDBRows()
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		cursorData := dto.PaginationCursor{}
		response, _ := g.getCxTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsForCxFirstPageResponse(true), response)
	})

	t.Run("happy-path-no-next-page", func(t *testing.T) {
		dbRows := resources.CxTransactionsDataMockDBRows()
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response, _ := g.getCxTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsNoNextPageResponse(), response)
	})

	t.Run("happy-path-prev-page-scrolling", func(t *testing.T) {
		dbRows := resources.GetAllTransactionsBackwardScrollingForCxMockDBResponse()
		request := &dto.TransactionHistorySearchRequest{
			AccountID:   "**********",
			StartDate:   "2021-08-01",
			EndDate:     "2021-08-31",
			PageSize:    2,
			EndingAfter: "MjAyMS0wOC0yMCAwNzo0MDowMCArMDUzMCBJU1QsMSwz",
		}
		cursorData := dto.PaginationCursor{
			ID:                 1,
			FirstTransactionID: 3,
			Date:               "2021-08-20 07:40:00 +0530",
		}
		response, _ := g.getCxTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsBackwardScrollingPrevPageForCxResponse(), response)
	})

	t.Run("happy-path-prev-next-page-exits", func(t *testing.T) {
		dbRows := resources.GetAllTransactionsPrevNextExistForCxMockDBResponse()
		request := &dto.TransactionHistorySearchRequest{
			AccountID:      "**********",
			StartDate:      "2021-08-01",
			EndDate:        "2021-08-31",
			PageSize:       1,
			StartingBefore: "MjAyMS0wOC0yMCAwNzo0MTo0MCArMDUzMCBJU1QsMiwy",
		}
		cursorData := dto.PaginationCursor{
			ID:                 1,
			FirstTransactionID: 3,
			Date:               "2021-08-20 07:40:00 +0530",
		}
		response, _ := g.getCxTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsPrevNextBothExistForCxResponse(), response)
	})

	t.Run("happy-path-with-card", func(t *testing.T) {
		mockCardTransactionDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardTransactionDetail.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransactionDetail{resources.CxCardTransactionDetailSample()}, nil)
		storage.CardTransactionDetailD = mockCardTransactionDetail
		dbRows := resources.CxSearchTransactionsCardsDataMockDBRows()
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		cursorData := dto.PaginationCursor{}
		response, _ := g.getCxTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetTransactionsWithCardForCxResponse(), response)
	})

	t.Run("happy-path-with-rewards-txn", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		mockCardTransactionDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardTransactionDetail.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransactionDetail{resources.CxCardTransactionDetailSample()}, nil)
		storage.CardTransactionDetailD = mockCardTransactionDetail
		dbRows := resources.CxRewardsTransactionsMockDBRows()
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response, _ := g.getCxTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetTransactionsWithRewardsForCxResponse(), response)
	})

	t.Run("happy-path-with-earmark-txn", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		mockCardTransactionDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardTransactionDetail.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransactionDetail{resources.CxCardTransactionDetailSample()}, nil)
		storage.CardTransactionDetailD = mockCardTransactionDetail
		dbRows := resources.CxEarmarkTransactionsMockDBRows()
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response, _ := g.getCxTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetTransactionsWithEarmarkForCxResponse(), response)
	})

	t.Run("debit-card-transaction-without-card-transaction-detail", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		mockCardTransactionDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardTransactionDetail.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransactionDetail{}, nil)
		storage.CardTransactionDetailD = mockCardTransactionDetail
		dbRows := resources.CxSearchTransactionsCardsDataMockDBRows()
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		cursorData := dto.PaginationCursor{}
		response, _ := g.getCxTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetDefaultResponseForCardTransactionsWithoutCardDetail(), response)
	})

	t.Run("debit-card-new-card-issuance-fee", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		localise.Init(mockAppConfig)
		mockCardTransactionDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardTransactionDetail.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransactionDetail{}, nil)
		storage.CardTransactionDetailD = mockCardTransactionDetail
		dbRows := lo.Filter(resources.CardMaintenanceFeeTransactionsDBMockRows(), func(row *storage.TransactionsData, _ int) bool {
			return row.TransactionType == constants.NewCardIssuanceFeeTransactionType
		})
		cardTxn := dbRows[0]
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		cursorData := dto.PaginationCursor{}
		var txnDetail dto.TransactionDetail
		err := json.Unmarshal(cardTxn.TransactionDetails, &txnDetail)
		require.NoError(t, err)
		expected := &dto.TransactionsHistorySearchResponse{
			Links: map[string]string{
				"next":         "",
				"nextCursorID": "",
				"prev":         "",
				"prevCursorID": "",
			},
			Data: []dto.TransactionHistorySearchData{{
				TransactionID:     cardTxn.ClientTransactionID,
				BatchID:           cardTxn.ClientBatchID,
				IconURL:           constants.IconURLMap["DefaultTransaction"],
				Amount:            cardTxn.FormattedAmount(context.Background()),
				Status:            "COMPLETED",
				Currency:          "MYR",
				IsQR:              false,
				CreationTimestamp: cardTxn.BatchValueTimestamp,
				TransactionCode: &dto.TransactionCode{
					Domain:  cardTxn.TransactionDomain,
					Type:    cardTxn.TransactionType,
					SubType: cardTxn.TransactionSubtype,
				},
				CardTransactionDetail: &dto.CxCardTransactionDetail{
					CardID:         txnDetail.CardID,
					TailCardNumber: txnDetail.TailCardNumber,
				},
				TransactionDescription: "New card issuance fee",
				CounterParty: &dto.CounterParty{
					DisplayName: "New card issuance fee",
					TransactionDetails: map[string]string{
						"service_type": "",
					},
				},
				CounterParties: []dto.CounterParty{
					{
						DisplayName: "New card issuance fee",
						TransactionDetails: map[string]string{
							"service_type": "",
						},
					},
				},
			}},
		}

		response, _ := g.getCxTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, expected, response)
	})

	t.Run("MooMo-trans", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentMooMooDataMockDBRows(), nil)
		storage.PaymentDetailD = mockStorageDAO
		dbRows := resources.CxMooMooTransactionsDataMockDBRows()
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response, _ := g.getCxTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetMooMooTransactionsResponse(), response)
	})
}

func TestGetAllCxTransactionsFromDB(t *testing.T) {
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetails", mock.Anything, mock.Anything).Return(resources.GetAccountImageDetailsSampleResponse(), nil)

	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{},
		Locale:     config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := GetAccountTransactionsSearchStruct{
		AccountServiceClient: mockAccountServiceClient,
	}

	t.Run("no-matching-resources", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "12345",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		expectedResp := &dto.TransactionsHistorySearchResponse{
			Links: map[string]string{"prev": "", "prevCursorID": "", "next": "", "nextCursorID": ""},
			Data:  []dto.TransactionHistorySearchData{},
		}

		response, err := g.GetAllCxTransactionsFromDB(context.Background(), request)
		assert.Nil(t, err)
		assert.Equal(t, expectedResp, response)
	})

	t.Run("happy-path-firstPage", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsForCxFirstPageMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsForCxFirstPageResponse(true)
		response, err := g.GetAllCxTransactionsFromDB(context.Background(), request)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("pagesize-exceed-prev-response-size", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID:   "**********",
			StartDate:   "2021-08-01",
			EndDate:     "2021-08-31",
			PageSize:    4,
			EndingAfter: "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsForCxFirstPageMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedError := customErr.BuildErrorResponse(customErr.BadRequest, "pageSize exceed response length when fetching previous page")

		_, err := g.GetAllCxTransactionsFromDB(context.Background(), request)
		assert.Equal(t, expectedError, err)
	})
}

func TestCxTransactionListDBFilters(t *testing.T) {
	t.Run("happyPath", func(t *testing.T) {
		request := &dto.TransactionHistorySearchRequest{
			AccountID:          "12345",
			StartDate:          "2021-08-01",
			EndDate:            "2021-08-31",
			PageSize:           2,
			TransactionType:    "REWARD_CASHBACK,INTEREST_PAYOUT",
			TransactionSubtype: "BANK_INITIATED",
		}
		var cursorData dto.PaginationCursor
		filters := cxTransactionListDBFilters(request, cursorData)
		assert.Equal(t, 10, len(filters))
	})
}

func TestFilterCxTransactionsOnAccountPhaseAndType(t *testing.T) {
	t.Run("all-posting-committed-phase", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.AllCommittedPostingForCx()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, resources.AllCommittedPostingForCx(), finalDBResponse)
	})

	t.Run("reversal posting, only show outgoing cancelled and not incoming cancelled", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.AllReleasePostingForCx()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, []*storage.TransactionsData(nil), finalDBResponse)
	})

	t.Run("reversal posting, only show outgoing cancelled", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.FailedFASTPosting()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, []*storage.TransactionsData{resources.FailedFASTPosting()[0]}, finalDBResponse)
	})

	t.Run("committed-and-pending-outgoing-phase", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.CommittedAndPendingOutgoingPosting()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, responses.FilterTransactionsCommittedAndPendingOutgoingPostingResponse(), finalDBResponse)
	})

	t.Run("all-pending-incoming-phase", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.AllPendingIncomingPosting()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, []*storage.TransactionsData(nil), finalDBResponse)
	})

	t.Run("committed-and-pending-incoming-phase", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.CommittedAndPendingIncomingPosting()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, responses.FilterTransactionsCommittedAndPendingIncomingPostingResponse(), finalDBResponse)
	})

	t.Run("all-posting-outgoing-phase", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.AllPendingOutgoingPosting()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, resources.AllPendingOutgoingPosting(), finalDBResponse)
	})

	t.Run("interest_payout_filtering_reverse_order", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.InterestPayoutTransactionReverseOrderInput()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, constants.CREDIT, finalDBResponse[0].DebitOrCredit)
		assert.Equal(t, "DEFAULT", finalDBResponse[0].AccountAddress)
	})

	t.Run("interest_payout_filtering_ordered_input", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.InterestPayoutTransactionOrderedInput()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, constants.CREDIT, finalDBResponse[0].DebitOrCredit)
		assert.Equal(t, "DEFAULT", finalDBResponse[0].AccountAddress)
	})

	t.Run("pocket_transactions", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.PocketTransactionsForCx()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, responses.PocketTransactionsOrder(), finalDBResponse)
	})

	t.Run("incoming-settlement-txns", func(t *testing.T) {
		req := &dto.TransactionHistorySearchRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.IncomingSettlementTxns()
		finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, []*storage.TransactionsData{resources.IncomingSettlementTxns()[0]}, finalDBResponse)
	})
}

func TestFetchCxPaymentDetail(t *testing.T) {
	t.Run("empty-transactions-data", func(t *testing.T) {
		var clientBatchTxIDs []string
		request := &dto.TransactionHistorySearchRequest{AccountID: "**********"}
		response := fetchCxPaymentDetail(context.Background(), clientBatchTxIDs, request)
		assert.Equal(t, map[string]*storage.PaymentDetail{}, response)
	})

	t.Run("no-matching-transaction", func(t *testing.T) {
		// mocking PaymentDetail Call
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockStorageDAO

		clientBatchTxIDs := []string{"abc123efg"}
		request := &dto.TransactionHistorySearchRequest{AccountID: "**********"}
		response := fetchCxPaymentDetail(context.Background(), clientBatchTxIDs, request)
		assert.Equal(t, map[string]*storage.PaymentDetail{}, response)
	})

	t.Run("happy-path", func(t *testing.T) {
		// mocking PaymentDetail Call
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows()[:3], nil)
		storage.PaymentDetailD = mockStorageDAO

		exportedResponse := responses.FetchPaymentDetailHappyPath()
		clientBatchTxIDs := []string{"abc123efg", "efg123abd", "efg1111abd"}
		request := &dto.TransactionHistorySearchRequest{AccountID: "**********"}
		response := fetchCxPaymentDetail(context.Background(), clientBatchTxIDs, request)
		assert.Equal(t, exportedResponse, response)
	})
}

package handlerlogic

import (
	"encoding/base64"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"gitlab.myteksi.net/dakota/transaction-history/utils"

	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// URL templates.
const (
	PreviousPageLink     = "/v1/accounts/%v/transactions?pageSize=%v&endingAfter=%v"
	NextPageLink         = "/v1/accounts/%v/transactions?pageSize=%v&startingBefore=%v"
	CursorDataTimeLayout = "2006-01-02T15:04:05.999999"
)

// parsePaginationCursorData method that parse the cursor if passed as parameter
func parsePaginationCursorData(startingBefore string, endingAfter string) (dto.PaginationCursor, error) {
	var cursorData dto.PaginationCursor
	var err error
	switch {
	case endingAfter != "":
		cursorData, err = decodeCursor(endingAfter)
	case startingBefore != "":
		cursorData, err = decodeCursor(startingBefore)
	}
	if err != nil {
		return cursorData, err
	}

	return cursorData, nil
}

// encodeCursor method generates encrypted cursor from timestamp & ID
func encodeCursor(data dto.PaginationCursor) string {
	return base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s,%d,%d,%s", data.Date, data.ID, data.FirstTransactionID, data.TransactionID)))
}

// decodeCursor method parse timestamp and ID from encrypted cursor
func decodeCursor(cursor string) (dto.PaginationCursor, error) {
	var res dto.PaginationCursor
	var decodedCursorArr []string
	cursorBytes, err := base64.StdEncoding.DecodeString(cursor)
	// Return error if unable to decode
	if err != nil {
		return res, err
	}
	decodedCursorArr = strings.Split(string(cursorBytes), ",")
	// Return error if decoded data is invalid
	if len(decodedCursorArr) != 4 {
		return res, errors.New("cursor is invalid")
	}
	res.Date = decodedCursorArr[0]
	res.TransactionID = decodedCursorArr[3]
	res.ID, err = strconv.ParseInt(decodedCursorArr[1], 10, 64)
	if err != nil {
		return res, err
	}

	firstTransactionID, err := strconv.ParseInt(decodedCursorArr[2], 10, 64)
	// Handling both ParseInt errors together
	if err != nil {
		return res, err
	}
	res.FirstTransactionID = utils.MustConvertToUint64(firstTransactionID)
	return res, nil
}

// paginationLinks create links required for subsequent pagination calls
func paginationLinks(dbResponse []*storage.TransactionsData, paginationParameters dto.PaginationParameters, cursorData dto.PaginationCursor) map[string]string {
	// if first page request, FirstTransactionID is equal to ID of the first fetched transaction
	if paginationParameters.EndingAfter == "" && paginationParameters.StartingBefore == "" {
		cursorData.FirstTransactionID = dbResponse[0].ID
	}
	nextLink, nextCursorID := nextPageLink(dbResponse, paginationParameters, cursorData)
	prevLink, prevCursorID := prevPageLink(dbResponse, paginationParameters, cursorData)
	return map[string]string{"prev": prevLink, "prevCursorID": prevCursorID, "next": nextLink, "nextCursorID": nextCursorID}
}

// nextPageLink method holds logic of next page link
func nextPageLink(dbResponse []*storage.TransactionsData, paginationParameters dto.PaginationParameters, cursorData dto.PaginationCursor) (string, string) {
	dbLen := utils.MustConvertToInt64(len(dbResponse))
	pageSize := paginationParameters.PageSize

	// Check if we should generate a "next" pagination link
	// This indicates that there are more results beyond the current page.
	// Condition 1: dbLen == pageSize+1
	// Condition 2: If EndingAfter is provided, means the next page is requested
	if dbLen == pageSize+1 || paginationParameters.EndingAfter != "" {
		// Ensure lastIndex does not go out of bounds
		lastIndex := utils.MinInt(int(pageSize-1), len(dbResponse)-1)
		nextPageCursorData := dto.PaginationCursor{
			ID:                 utils.MustConvertToInt64(dbResponse[lastIndex].ID),
			TransactionID:      dbResponse[lastIndex].ClientTransactionID,
			Date:               dbResponse[lastIndex].BatchValueTimestamp.Format(CursorDataTimeLayout),
			FirstTransactionID: cursorData.FirstTransactionID,
		}

		// Generate and return the next page link
		return createPaginationLinks(NextPageLink, paginationParameters, nextPageCursorData)
	}

	return "", ""
}

// prevPageLink method holds logic of previous page link
// nolint:dupl
func prevPageLink(dbResponse []*storage.TransactionsData, paginationParameters dto.PaginationParameters, cursorData dto.PaginationCursor) (string, string) {
	var prevLink, prevCursorID string
	var prevPageCursorData dto.PaginationCursor

	if paginationParameters.StartingBefore == "" && paginationParameters.EndingAfter == "" {
		prevLink = ""
		prevCursorID = ""
	} else if dbResponse[0].ID == cursorData.FirstTransactionID {
		// If the first ID of page equal to FirstTransactionID, this implies not previousPage exist
		prevLink = ""
		prevCursorID = ""
	} else {
		prevPageCursorData = dto.PaginationCursor{
			ID:                 utils.MustConvertToInt64(dbResponse[0].ID),
			TransactionID:      dbResponse[0].ClientTransactionID,
			Date:               dbResponse[0].BatchValueTimestamp.Format(CursorDataTimeLayout),
			FirstTransactionID: cursorData.FirstTransactionID,
		}
		prevLink, prevCursorID = createPaginationLinks(PreviousPageLink, paginationParameters, prevPageCursorData)
	}
	return prevLink, prevCursorID
}

// createPaginationLinks will create pagination links based on next page cursor data.
func createPaginationLinks(link string, paginationParameter dto.PaginationParameters, newCursorData dto.PaginationCursor) (string, string) {
	newCursor := encodeCursor(newCursorData)
	formattedLink := fmt.Sprintf(link, paginationParameter.AccountID, paginationParameter.PageSize, newCursor)
	if paginationParameter.StartDate != "" {
		formattedLink += fmt.Sprintf("&startDate=%v", paginationParameter.StartDate)
	}
	if paginationParameter.EndDate != "" {
		formattedLink += fmt.Sprintf("&endDate=%v", paginationParameter.EndDate)
	}
	return formattedLink, newCursor
}

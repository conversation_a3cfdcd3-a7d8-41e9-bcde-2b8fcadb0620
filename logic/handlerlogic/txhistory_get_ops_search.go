package handlerlogic

import (
	"context"
	"database/sql"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"gitlab.myteksi.net/dakota/transaction-history/logic/presenters/loantransactionpresenter"

	"gitlab.myteksi.net/dakota/transaction-history/logic/presenters/cardtransactionpresenter"

	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/logic/presenters/transactionpresenter"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dakota/transaction-history/utils/validations"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

var emptyLinks = map[string]string{"prev": "", "prevCursorID": "", "next": "", "nextCursorID": ""}

// GetOpsSearchStruct ...
type GetOpsSearchStruct struct {
	AccountServiceClient accountService.AccountService
	AppConfig            *config.AppConfig
	Store                storage.DatabaseStore
	accounts             map[string]*accountService.GetAccountResponse
	accountsErr          map[string]error
}

type transactionDetails struct {
	paymentDetails map[storage.PaymentDataKey]*storage.PaymentDetail
	cardDetails    map[storage.CardDataKey]*storage.CardTransactionDetail
	loanDetails    map[storage.LoanDataKey]*storage.LoanDetail
}

// OpsSearchParam ...
type OpsSearchParam struct {
	AccountID          string
	TransactionID      string
	BatchID            string
	TransactionType    string
	TransactionSubtype string
	FromAmount         int64
	ToAmount           int64
	EndingAfter        string
	StartingBefore     string
	StartDate          string
	EndDate            string
	PageSize           int
	Exhaustive         bool
}

func emptyTransactionDetails() transactionDetails {
	return transactionDetails{
		paymentDetails: make(map[storage.PaymentDataKey]*storage.PaymentDetail),
		cardDetails:    make(map[storage.CardDataKey]*storage.CardTransactionDetail),
		loanDetails:    make(map[storage.LoanDataKey]*storage.LoanDetail),
	}
}

// NewGetOpsSearch ...
func NewGetOpsSearch(accountServiceClient accountService.AccountService, appConfig *config.AppConfig, store storage.DatabaseStore) *GetOpsSearchStruct {
	return &GetOpsSearchStruct{
		AccountServiceClient: accountServiceClient,
		AppConfig:            appConfig,
		Store:                store,
		accounts:             make(map[string]*accountService.GetAccountResponse),
		accountsErr:          make(map[string]error),
	}
}

// GetAccountOnce ...
func (g *GetOpsSearchStruct) GetAccountOnce(ctx context.Context, accountID string) (*accountService.GetAccountResponse, error) {
	if acc, ok := g.accounts[accountID]; ok {
		return acc, nil
	}
	if err, ok := g.accountsErr[accountID]; ok {
		return nil, err
	}
	acc, err := g.AccountServiceClient.GetAccountDetailsByAccountID(ctx, &accountService.GetAccountRequest{
		AccountID:    accountID,
		FetchBalance: false,
	})

	if err != nil {
		g.accountsErr[accountID] = err
		return nil, err
	}

	g.accounts[accountID] = acc
	return acc, nil
}

// GetOpsSearchRequestIdentifierValidator validates the identifiers in request parameters
func GetOpsSearchRequestIdentifierValidator(ctx context.Context, req *api.GetOpsSearchRequest) error {
	if err := validations.ValidateIdentifier(ctx, req.AccountID, req.ExternalID, req.TransactionID, req.BatchID); err != nil {
		return err
	}
	if err := validations.ValidateIdentifierCombination(ctx, req.AccountID, req.ExternalID, req.TransactionID, req.BatchID); err != nil {
		return err
	}
	return nil
}

// GetOpsSearchRequestValidator validates the attribute filter in request parameters
func GetOpsSearchRequestValidator(ctx context.Context, req *api.GetOpsSearchRequest) error {
	if req.Status != "" {
		if err := validations.ValidateStatus(ctx, req.Status); err != nil {
			return err
		}
	}
	if req.TransactionType != "" {
		if err := validations.ValidateTxnType(ctx, req.TransactionType); err != nil {
			return err
		}
	}
	if req.TransactionSubtype != "" {
		if err := validations.ValidateTxnSubtype(ctx, req.TransactionSubtype); err != nil {
			return err
		}
	}
	if req.FromAmount != 0 || req.ToAmount != 0 {
		if err := validations.ValidateAmount(ctx, req.FromAmount, req.ToAmount); err != nil {
			return err
		}
	}
	if req.PageSize != 0 {
		if err := validations.ValidatePageSize(ctx, req.PageSize, MinPageSize, MaxPageSize); err != nil {
			return err
		}
	}

	return nil
}

// GetOpsSearchRequestFilterCombinationValidator validates the combinations of identifier and attribute filters in request parameters
func GetOpsSearchRequestFilterCombinationValidator(ctx context.Context, req *api.GetOpsSearchRequest) error {
	if req.ExternalID != "" || req.TransactionID != "" || req.BatchID != "" {
		if err := validations.ValidateEmptyAttributeFilters(ctx, req); err != nil {
			return err
		}
	}
	if req.Status != "" {
		if err := validations.ValidateTimestampAttribute(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// Response in case of TransactionDetails not present
func emptyGetOpsSearchResponse() *api.GetOpsSearchResponse {
	response := &api.GetOpsSearchResponse{}
	response.Links = emptyLinks
	response.Data = []api.OpsSearchResponse{}
	return response
}

// GetOpsSearch fetches the transactions from the DB and returns the formatted GetOpsSearchResponse.
func (g *GetOpsSearchStruct) GetOpsSearch(ctx context.Context, req *api.GetOpsSearchRequest) (*api.GetOpsSearchResponse, error) {
	var (
		dbResponse []*storage.TransactionsData
		err        error
		db         *sql.DB
	)

	if req.PageSize == MinPageSize {
		req.PageSize = DefaultPageSize
	}
	// parsing cursor if present
	cursorData, err := parsePaginationCursorData(req.StartingBefore, req.EndingAfter)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("Error parsing pagination cursor, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetOpsSearchLogTag, fmt.Sprintf("Parsed cursor data: %s", utils.ToJSON(cursorData)))

	// get data from db
	if req.ExternalID != "" {
		db, err = g.Store.GetDatabaseHandle(ctx, g.AppConfig.Data.MySQL.Master)
		if err != nil {
			slog.FromContext(ctx).Error(constants.GetOpsSearchLogTag, fmt.Sprintf("Fail to get database handle, err: %s", err.Error()))
			return nil, customErr.DefaultInternalServerError
		}
		dbResponse, err = g.Store.GetTxnDataByExternalIDFromDB(ctx, req.ExternalID, db)
		if err != nil && err != data.ErrNoData {
			slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("Error getting ops search, err: %s", err.Error()))
			return nil, customErr.DefaultInternalServerError
		}
	} else {
		param := mapGetOpsSearchRequestToOpsSearchParam(req, false)
		filters := buildOpsSearchTxnDataFilters(param, cursorData)
		dbResponse, err = storage.TransactionsDataD.Find(ctx, filters...)
	}

	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("Error getting ops search, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}

	// when no transactions found
	if len(dbResponse) == 0 || err == data.ErrNoData {
		return emptyGetOpsSearchResponse(), nil
	}

	finalTxnDataDB, txnDetailsData := generateFinalDBResponse(ctx, dbResponse, req)
	if len(finalTxnDataDB) == 0 {
		return emptyGetOpsSearchResponse(), nil
	}

	return g.getOpsSearchResponseGenerator(ctx, finalTxnDataDB, txnDetailsData, req, cursorData), nil
}

// generateFinalDBResponse contains logic to further filter db data on go service layer
func generateFinalDBResponse(ctx context.Context, dbResponse []*storage.TransactionsData, req *api.GetOpsSearchRequest) ([]*storage.TransactionsData, transactionDetails) {
	// method to filter out committed and pending.
	txnDataDBResponse, clientBatchTxIDs := filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse, req)
	txnDetailsData := emptyTransactionDetails()

	if len(txnDataDBResponse) == 0 {
		return []*storage.TransactionsData{}, txnDetailsData
	}

	// fetch corresponding payment details
	transactionsPaymentDetail := fetchOpsSearchPaymentDetail(ctx, clientBatchTxIDs.PaymentBatchIDs, req)
	// fetch corresponding card details
	transactionsCardDetail := fetchOpsSearchCardDetail(ctx, clientBatchTxIDs.CardBatchIDs, req)
	// fetch corresponding loan details
	transactionsLoanDetail := fetchOpsSearchLoanDetail(ctx, clientBatchTxIDs.LoanBatchIDs)

	txnDetailsData.paymentDetails = transactionsPaymentDetail
	txnDetailsData.cardDetails = transactionsCardDetail
	txnDetailsData.loanDetails = transactionsLoanDetail
	// get only customer accounts txn data row (without internal account)
	customerTxnData := getCustomerAccountTxnRows(txnDataDBResponse, txnDetailsData)

	var finalTxnDataDB []*storage.TransactionsData
	// construct final txn data based on status filter (if provided)
	if req.Status != "" {
		for _, transaction := range customerTxnData {
			status := presenterhelper.GetPaymentCasaTransactionStatus(transaction, transactionsPaymentDetail[storage.BuildPaymentDataKey(transaction.ClientBatchID, transaction.AccountID)])
			if transaction.IsCardDomain() && !transaction.IsCardOpsTxn() && !transaction.IsRewardsTxn() {
				status = transactionsCardDetail[storage.BuildCardDataKey(transaction.ClientBatchID, transaction.AccountID)].GetFormattedTxnStatus()
			}
			if transaction.IsLendingDomain() {
				status = presenterhelper.GetLoanCasaTransactionStatus(transaction, transactionsLoanDetail[storage.BuildLoanDataKey(transaction.ClientBatchID)])
			}
			if status != utils.TxnStatusMap[req.Status] {
				continue
			}
			finalTxnDataDB = append(finalTxnDataDB, transaction)
		}
		return finalTxnDataDB, txnDetailsData
	}
	return customerTxnData, txnDetailsData
}

// getOpsSearchResponseGenerator contains logic to generate response from DB data after filtering.
func (g *GetOpsSearchStruct) getOpsSearchResponseGenerator(ctx context.Context, finalTxnDataDB []*storage.TransactionsData, txnDetailsData transactionDetails, req *api.GetOpsSearchRequest, cursorData dto.PaginationCursor) *api.GetOpsSearchResponse {
	var transactionsList []api.OpsSearchResponse

	transactionsPerPage := utils.MinInt(int(req.PageSize), len(finalTxnDataDB))

	// invert final DB entries to show them in descending order
	if req.EndingAfter != "" {
		finalTxnDataDB = invertDBResponseForBackwardScrolling(finalTxnDataDB[:transactionsPerPage])
	}
	for _, transaction := range finalTxnDataDB[:transactionsPerPage] {
		var transactionDetail api.OpsSearchResponse
		isChildAccount, _ := g.isChildAccount(ctx, transaction.AccountID)
		if transaction.IsCardDomain() && !transaction.IsCardOpsTxn() && !transaction.IsRewardsTxn() && !transaction.IsCardMaintenanceFeeTxn() {
			transactionDetail = cardtransactionpresenter.GetCardTransactionOpsSearch(ctx, transaction, txnDetailsData.cardDetails)
		} else if transaction.IsLendingDomain() || transaction.IsBizLendingDomain() {
			transactionDetail = loantransactionpresenter.GetLoanTransactionOpsSearch(ctx, transaction, txnDetailsData.paymentDetails, txnDetailsData.loanDetails)
		} else {
			transactionDetail = transactionpresenter.GeneratePaymentCasaTxnResponseForOpsSearch(ctx, transaction, isChildAccount, txnDetailsData.paymentDetails)
		}
		transactionsList = append(transactionsList, transactionDetail)
	}
	if len(transactionsList) == 0 {
		return emptyGetOpsSearchResponse()
	}

	paginationParams := utils.MapPaginationParameters(req.AccountID, req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, req.PageSize)
	links := paginationLinks(finalTxnDataDB, paginationParams, cursorData)
	return &api.GetOpsSearchResponse{Links: links, Data: transactionsList}
}

// nolint
func buildOpsSearchTxnDataFilters(param *OpsSearchParam, cursorData dto.PaginationCursor) []data.Condition {
	// add default filters
	// Set limit based on the exhaustive flag
	var filters []data.Condition
	if param.Exhaustive {
		filters = []data.Condition{
			data.Limit(int(param.PageSize) + 1),
		}
	} else {
		filters = []data.Condition{
			data.Limit(int(4 * param.PageSize)),
		}
	}

	// Exclude specific transaction types
	filters = append(filters,
		data.NotContainedIn("TransactionType", utils.ConvertToInterface(constants.InterestAccrualTransactionTypes)...),
		data.NotContainedIn("TransactionType", utils.ConvertToInterface(constants.TaxAccrualTransactionTypes)...),
	)

	if param.AccountID != "" {
		filters = append(filters,
			data.EqualTo("AccountID", param.AccountID))
	}
	if param.TransactionID != "" {
		filters = append(filters,
			data.EqualTo("ClientTransactionID", param.TransactionID))
	}
	if param.BatchID != "" {
		filters = append(filters,
			data.EqualTo("ClientBatchID", param.BatchID))
	}
	if param.TransactionType != "" {
		filters = append(filters,
			data.EqualTo("TransactionType", param.TransactionType))
	}
	if param.TransactionSubtype != "" {
		filters = append(filters,
			data.EqualTo("TransactionSubtype", param.TransactionSubtype))
	}

	// filter for amount
	switch {
	case param.FromAmount != 0 && param.ToAmount != 0:
		filters = append(filters,
			data.GreaterThanOrEqualTo("TransactionAmount", utils.ConvertAmountInCentsToAmount(param.FromAmount)),
			data.LessThanOrEqualTo("TransactionAmount", utils.ConvertAmountInCentsToAmount(param.ToAmount)))
	case param.FromAmount != 0:
		filters = append(filters,
			data.GreaterThanOrEqualTo("TransactionAmount", utils.ConvertAmountInCentsToAmount(param.FromAmount)))
	case param.ToAmount != 0:
		filters = append(filters,
			data.LessThanOrEqualTo("TransactionAmount", utils.ConvertAmountInCentsToAmount(param.ToAmount)))
	}

	// filter for endingAfter and startingBefore
	switch {
	case param.EndingAfter == "" && param.StartingBefore == "":
		filters = append(filters,
			data.DescendingOrder("BatchValueTimestamp"),
			data.DescendingOrder("ID"))
	case param.EndingAfter != "":
		filters = append(filters,
			data.GreaterThan("ID", cursorData.ID),
			data.AscendingOrder("BatchValueTimestamp"),
			data.AscendingOrder("ID"),
			data.UnEqualTo("ClientTransactionID", cursorData.TransactionID),
		)
	case param.StartingBefore != "":
		filters = append(filters,
			data.LessThan("ID", cursorData.ID),
			data.DescendingOrder("BatchValueTimestamp"),
			data.DescendingOrder("ID"),
			data.UnEqualTo("ClientTransactionID", cursorData.TransactionID),
		)
	}

	// filter for startingDate and endingDate
	startingDate, endingDate := computeDateRange(param.StartingBefore, param.EndingAfter, param.StartDate, param.EndDate, cursorData)
	if startingDate != "" {
		filters = append(filters, data.GreaterThanOrEqualTo("BatchValueTimestamp", startingDate))
	}
	if endingDate != "" {
		filters = append(filters, data.LessThanOrEqualTo("BatchValueTimestamp", endingDate))
	}
	return filters
}

func mapGetOpsSearchRequestToOpsSearchParam(req *api.GetOpsSearchRequest, exhaustive bool) *OpsSearchParam {
	return &OpsSearchParam{
		AccountID:          req.AccountID,
		TransactionID:      req.TransactionID,
		BatchID:            req.BatchID,
		TransactionType:    req.TransactionType,
		TransactionSubtype: req.TransactionSubtype,
		FromAmount:         req.FromAmount,
		ToAmount:           req.ToAmount,
		EndingAfter:        req.EndingAfter,
		StartingBefore:     req.StartingBefore,
		StartDate:          req.StartDate,
		EndDate:            req.EndDate,
		PageSize:           int(req.PageSize),
		Exhaustive:         exhaustive,
	}
}

// filterTransactionsOnAccountPhaseAndType this method will ensure only one entry per batch will be present
// Logic (** ORDER MATTERS **): Based on
// https://wiki.grab.com/pages/viewpage.action?spaceKey=Digibank&title=Product+requirements+for+Transaction+History%2C+Transaction+Detail+and+Statements+for+R2
// nolint: dupl, gocognit, funlen
func filterOpsSearchTransactionsOnAccountPhaseAndType(dbResponse []*storage.TransactionsData, req *api.GetOpsSearchRequest) ([]*storage.TransactionsData, dto.ClientTypesBatchIDs) {
	// Filter out all incoming failed and cancelled transfer, processing will be filtered out by below loop
	filteredDBResponse := filterOutIncomingFailedAndCancelledTransfer(dbResponse, constants.DefaultAccountAddress)
	// Filter out settled or released card txns
	filteredDBResponse = filterOutSettledOrReleasedCardTxn(filteredDBResponse, constants.DefaultAccountAddress)
	// Storing clientBatchID to maintain the order in which fetched from DB
	clientBatchIDOrder, clientBatchIDToTransactionsDataMap := createClientBatchIDToTransactionsDataMap(filteredDBResponse)

	// Filtering bases on AccountPhase
	for clientBatchID, transactions := range clientBatchIDToTransactionsDataMap {
		var committedPostingTx, pendingOutGoingTx, releaseTx []*storage.TransactionsData
		for _, item := range transactions {
			if item.AccountAddress != constants.DefaultAccountAddress {
				continue
			}
			// do not show the SETTLEMENT/RELEASE record for a card txn
			if item.IsCardTxn() {
				if item.TmTransactionType == constants.SettlementTxType || item.TmTransactionID == constants.Release {
					continue
				}
			}
			switch item.AccountPhase {
			case constants.PostingPhaseCommitted:
				committedPostingTx = append(committedPostingTx, item)
			case constants.PostingPhasePendingOutgoing:
				if item.TmTransactionType == constants.OutboundAuthorisation {
					pendingOutGoingTx = append(pendingOutGoingTx, item)
				} else if item.TmTransactionType == constants.Release && item.TransactionType == constants.ReleaseEarmarkTransactionType {
					releaseTx = append(releaseTx, item)
				}
			}
		}
		clientBatchIDToTransactionsDataMap[clientBatchID] = []*storage.TransactionsData{}

		switch {
		case committedPostingTx != nil:
			clientBatchIDToTransactionsDataMap[clientBatchID] = committedPostingTx
		case releaseTx != nil:
			clientBatchIDToTransactionsDataMap[clientBatchID] = releaseTx
		case pendingOutGoingTx != nil:
			clientBatchIDToTransactionsDataMap[clientBatchID] = pendingOutGoingTx
		}

		slog.FromContext(context.Background()).Info(constants.GetAllTransactionsLogicLogTag,
			fmt.Sprintf("database row selected for batchID: %s, transaction to show: %s", clientBatchID, strings.Join(
				lo.Map(clientBatchIDToTransactionsDataMap[clientBatchID], func(item *storage.TransactionsData, _ int) string {
					return strconv.FormatUint(item.ID, 10)
				}), ","),
			))
	}

	// computing number of rows to be added
	filteredRowsCount := computeNumberOfTransactionsInFilteredDBResponse(req.StartingBefore, req.EndingAfter, req.PageSize)

	// Select transactions as per required pageSize
	finalDBResponse, filteredClientBatchIDs := createFinalDBTransactionList(clientBatchIDOrder, clientBatchIDToTransactionsDataMap, filteredRowsCount)

	// db fetch data in ascending order when EndingAfter is passed in
	// this might cause sorting issue as the transactions are sorted in authorization timestamp (if any) instead of settlement/released timestamp
	if req.EndingAfter != "" {
		sort.Sort(storage.SortByBatchValueTS(finalDBResponse))
	}
	slog.FromContext(context.Background()).Debug(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("finalDBResponse after sorting: %s", utils.ToJSON(finalDBResponse)))
	return finalDBResponse, filteredClientBatchIDs
}

// getCustomerAccountTxnRows filter the txnData based on AccountID get from paymentDetails and cardDetails
// txn data for internal account will be filtered out
func getCustomerAccountTxnRows(txnDataDB []*storage.TransactionsData, txnDetailsData transactionDetails) []*storage.TransactionsData {
	// if there is no payment data and card data, meaning it is pocket transaction / interest payout / rewards txn
	if len(txnDetailsData.paymentDetails) == 0 && len(txnDetailsData.cardDetails) == 0 {
		return txnDataDB
	}

	var custAccountTxnData []*storage.TransactionsData

	// Create a map to store the IDs of items in list1
	customerAccountList := make(map[string]struct{})
	for _, dbRow := range txnDetailsData.paymentDetails {
		customerAccountList[dbRow.AccountID] = struct{}{}
	}
	for _, dbRow := range txnDetailsData.cardDetails {
		customerAccountList[dbRow.AccountID] = struct{}{}
	}

	// Check if an item from list2 exists in list1IDs, if yes, it's an intersection
	for _, dbRow := range txnDataDB {
		if _, found := customerAccountList[dbRow.AccountID]; found {
			custAccountTxnData = append(custAccountTxnData, dbRow)
		}
	}
	return custAccountTxnData
}

// fetchOpsSearchPaymentDetail gets payment's metadata related to transaction.
// nolint: dupl
func fetchOpsSearchPaymentDetail(ctx context.Context, clientBatchTxIDs []string, req *api.GetOpsSearchRequest) map[storage.PaymentDataKey]*storage.PaymentDetail {
	response := make(map[storage.PaymentDataKey]*storage.PaymentDetail)
	// return if no client transaction batch id present
	if len(clientBatchTxIDs) == 0 {
		return response
	}

	// create filters and search in payment detail table
	filters := []data.Condition{
		data.ContainedIn("TransactionID", utils.ConvertToInterface(clientBatchTxIDs)...),
	}

	if req.AccountID != "" {
		filters = append(filters, data.EqualTo("AccountID", req.AccountID))
	}

	dbResponse, err := storage.PaymentDetailD.Find(ctx, filters...)
	if len(dbResponse) == 0 {
		return response
	}
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("error while fetching paymentDetail db: %s", err.Error()))
		return response
	}

	// creating map
	for _, paymentDetail := range dbResponse {
		response[storage.BuildPaymentDataKey(paymentDetail.TransactionID, paymentDetail.AccountID)] = paymentDetail
	}
	return response
}

// fetchOpsSearchCardDetail gets cards detail related to transaction.
// nolint: dupl
func fetchOpsSearchCardDetail(ctx context.Context, clientBatchTxIDs []string, req *api.GetOpsSearchRequest) map[storage.CardDataKey]*storage.CardTransactionDetail {
	response := make(map[storage.CardDataKey]*storage.CardTransactionDetail)
	// return if no client transaction batch id present
	if len(clientBatchTxIDs) == 0 {
		return response
	}

	// create filters and search in payment detail table
	filters := []data.Condition{
		data.ContainedIn("CardTransactionID", utils.ConvertToInterface(clientBatchTxIDs)...),
	}

	if req.AccountID != "" {
		filters = append(filters, data.EqualTo("AccountID", req.AccountID))
	}

	dbResponse, err := storage.CardTransactionDetailD.Find(ctx, filters...)
	if len(dbResponse) == 0 {
		return response
	}
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("error while fetching cardTransactionDetail db: %s", err.Error()))
		return response
	}

	// creating map
	for _, cardDetail := range dbResponse {
		response[storage.BuildCardDataKey(cardDetail.CardTransactionID, cardDetail.AccountID)] = cardDetail
	}
	return response
}

// fetchOpsSearchLoanDetail gets loan detail related to transaction.
// nolint: dupl
func fetchOpsSearchLoanDetail(ctx context.Context, clientBatchTxIDs []string) map[storage.LoanDataKey]*storage.LoanDetail {
	response := make(map[storage.LoanDataKey]*storage.LoanDetail, 0)
	// return if no client transaction batch id present
	if len(clientBatchTxIDs) == 0 {
		return response
	}

	// create filters and search in loanDetail table
	filters := []data.Condition{
		data.ContainedIn("PaymentTransactionID", utils.ConvertToInterface(clientBatchTxIDs)...),
	}

	dbResponse, err := storage.LoanDetailD.Find(ctx, filters...)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("error while fetching loanTransactionDetail db: %s", err.Error()))
		return response
	}
	if len(dbResponse) == 0 {
		return response
	}

	// creating map
	for _, loanDetail := range dbResponse {
		response[storage.BuildLoanDataKey(loanDetail.PaymentTransactionID)] = loanDetail
	}
	return response
}

func (g *GetOpsSearchStruct) isChildAccount(ctx context.Context, accountID string) (bool, error) {
	accountDetail, err := g.GetAccountOnce(ctx, accountID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("error while fetching account details: %s", err.Error()))
		return false, err
	}
	return accountDetail.Account.ParentAccountID != "", nil
}

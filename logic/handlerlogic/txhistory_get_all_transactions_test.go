package handlerlogic

import (
	"context"
	"os"
	"testing"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/featureflag"

	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"

	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"

	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestGetTransactionListValidator(t *testing.T) {
	t.Run("accountID-parameter-missing", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		expectedError := servus.ErrorDetail{
			Message: "`accountID` is a mandatory field",
		}

		errorResponse := GetTransactionListValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})

	t.Run("happy-path", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "12345",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		errorResponse := GetTransactionListValidator(request)
		assert.Equal(t, 0, len(errorResponse))
	})

	t.Run("page-size-negative", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "12345",
			PageSize:  -1,
		}

		expectedError := servus.ErrorDetail{
			Message: "`pageSize` less than minPageSize",
		}

		errorResponse := GetTransactionListValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})

	t.Run("page-size-greater-than-max", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "12345",
			PageSize:  MaxPageSize + 1,
		}
		expectedError := servus.ErrorDetail{
			Message: "`pageSize` greater than maxPageSize",
		}

		errorResponse := GetTransactionListValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectedError, errorResponse[0])
	})
}

func TestGetTransactionsResponseGenerator(t *testing.T) {
	locale := utils.GetLocale()
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
	mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.CardTxnDetailMockDBRows(), nil)
	storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

	mockLoanDetailStorageDAO := &storage.MockILoanDetailDAO{}
	mockLoanDetailStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(resources.LoanDetailMockDBRows(), nil)
	storage.LoanDetailD = mockLoanDetailStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(responses.ListCASAAccountsForCustomerDetailResponse(), nil)
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Withdrawal:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferIn:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferOut:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferFee:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TaxOnInterest:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Adjustment:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			LendingRepayment:        "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			LendingDrawdown:         "https://assets.sgbank.dev/dev/txHistory/images/Drawdown.png",
		},
		Locale: config.Locale{Language: "id"},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("happy-path-next-page-exist", func(t *testing.T) {
		dbRows := resources.TransactionsDataMockDBRows()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsFirstPageResponseForListTransaction(), response)
	})

	t.Run("happy-path-no-next-page", func(t *testing.T) {
		dbRows := resources.TransactionsDataMockDBRows()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  5,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsOnlyPageResponse(), response)
	})

	t.Run("happy-path-prev-page-scrolling", func(t *testing.T) {
		dbRows := resources.GetAllTransactionsBackwardScrollingMockDBResponse()
		request := &api.GetTransactionsHistoryRequest{
			AccountID:   "**********",
			StartDate:   "2021-08-01",
			EndDate:     "2021-08-31",
			PageSize:    2,
			EndingAfter: "MjAyMS0wOC0yMCAwNzo0MDowMCArMDUzMCBJU1QsMSwz",
		}
		cursorData := dto.PaginationCursor{
			ID:                 1,
			FirstTransactionID: 3,
			Date:               "2021-08-20 07:40:00 +0530",
		}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		expected := responses.GetAllTransactionsBackwardScrollingPrevPageResponse()
		expected.Links = map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		}
		assert.Equal(t, expected, response)
	})

	t.Run("happy-path-prev-next-page-exits", func(t *testing.T) {
		dbRows := resources.GetAllTransactionsPrevNextExistMockDBResponse()
		request := &api.GetTransactionsHistoryRequest{
			AccountID:      "**********",
			StartDate:      "2021-08-01",
			EndDate:        "2021-08-31",
			PageSize:       1,
			StartingBefore: "MjAyMS0wOC0yMCAwNzo0MTo0MCArMDUzMCBJU1QsMiwy",
		}
		cursorData := dto.PaginationCursor{
			ID:                 1,
			FirstTransactionID: 3,
			Date:               "2021-08-20 07:40:00 +0530",
		}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		expected := responses.GetAllTransactionsPrevNextBothExistResponse()
		expected.Links = map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=1&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "/v1/accounts/**********/transactions?pageSize=1&endingAfter=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
		}
		assert.Equal(t, expected, response)
	})

	t.Run("happy-path-with-all-categories-and-icon-urls", func(t *testing.T) {
		dbRows := resources.TransactionsDataMockDBRowsForAllCategories()
		request := &api.GetTransactionsHistoryRequest{
			AccountID:      "**********",
			StartDate:      "2021-08-01",
			EndDate:        "2021-08-31",
			PageSize:       50,
			StartingBefore: "MjAyMS0wOC0yMCAwNzo0MTo0MCArMDUzMCBJU1QsMiwy",
		}
		cursorData := dto.PaginationCursor{
			ID:                 1,
			FirstTransactionID: 3,
			Date:               "2021-08-20 07:40:00 +0530",
		}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsWithAllCategoriesAndIconsResponse(), response)
	})

	t.Run("happy-path-for-payment-fee-use-case", func(t *testing.T) {
		dbRows := resources.TransactionsDataMockDBRowsForPaymentFeeUseCaseRequest()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "************",
			PageSize:  50,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsForPaymentFeeResponse(), response)
	})

	t.Run("happy-path-for-card-txn-use-case", func(t *testing.T) {
		dbRows := resources.TransactionsDataMockDBRowsForCardTxnUseCaseRequest()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "************",
			PageSize:  50,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsForCardTxnResponse(), response)
	})

	t.Run("happy-path-for-insurance-txn-use-case", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		dbRows := resources.TransactionsDataMockDBRowsForInsuranceTxn()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "************",
			PageSize:  50,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsForInsuranceTxnResponse(), response)
	})

	t.Run("happy-path-for-loan-txn-use-case", func(t *testing.T) {
		dbRows := resources.TransactionsDataMockDBRowsForLoanTxnUseCaseRequest()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "************",
			PageSize:  50,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsForLoanTxnResponse(), response)
	})

	t.Run("happy-path-for-biz-txn-use-case", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		dbRows := resources.TransactionsDataMockDBRowsForBizTxn()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "************",
			PageSize:  50,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsForBizTxnResponse(), response)
	})

	t.Run("happy-path-for-biz-lending-txn-use-case", func(t *testing.T) {
		mockAccountServiceClient = &accountServiceMock.AccountService{}
		mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetBizAccountDetailsByAccountIDResponse(), nil)
		g = GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}
		dbRows := resources.TransactionsDataMockDBRowsForBizLendingTxnUseCaseRequest()
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "************",
			PageSize:  50,
		}
		cursorData := dto.PaginationCursor{}
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, responses.GetAllTransactionsForBizLendingTxnResponse(), response)
	})
}

func TestGetAllCardReversalTransactions(t *testing.T) {
	defer func() { config.SetTenant("") }()
	locale := utils.GetLocale()
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
	storage.PaymentDetailD = mockStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(responses.ListCASAAccountsForCustomerDetailResponse(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Withdrawal:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferIn:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferOut:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferFee:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TaxOnInterest:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Adjustment:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}

	os.Setenv("LOCALISATION_PATH", "../../localise")
	config.SetTenant(tenants.TenantMY)
	constants.InitializeDynamicConstants(mockAppConfig)
	localise.Init(mockAppConfig)
	g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("spend card ATM reversal transaction", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
		}
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
		mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardATMReversalTrxDetails(), nil)
		mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardATMReversalTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

		response, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.NoError(t, err)
		assert.Equal(t, "ATM withdrawal reversal", response.Data[0].DisplayName)
	})

	t.Run("spend card present reversal transaction", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
		}
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
		mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardPresentReversalTrxDetails(), nil)
		mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardPresentReversalTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

		response, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.NoError(t, err)
		assert.Equal(t, "Refund from Amazon", response.Data[0].DisplayName)
	})

	t.Run("spend card not present reversal transaction", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
		}
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
		mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardNotPresentReversalTrxDetails(), nil)
		mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.SpendCardNotPresentReversalTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

		response, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.NoError(t, err)
		assert.Equal(t, "Refund from Amazon", response.Data[0].DisplayName)
	})
}

func TestGetAllTransactionsFromDB(t *testing.T) {
	locale := utils.GetLocale()
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(responses.ListCASAAccountsForCustomerDetailResponse(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Withdrawal:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferIn:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferOut:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferFee:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TaxOnInterest:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Adjustment:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)

	g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("no-matching-resources", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "12345",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, data.ErrNoData)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		expectedResp := &api.GetTransactionsHistoryResponse{
			Links: map[string]string{"prev": "", "prevCursorID": "", "next": "", "nextCursorID": ""},
			Data:  []api.TransactionHistoryResponse{},
		}

		response, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.Nil(t, err)
		assert.Equal(t, expectedResp, response)
	})

	t.Run("happy-path-firstPage", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetAllTransactionsFirstPageMockDBResponse(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsFirstPageResponseForListTransaction()
		response, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-with-pocket-txns", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "",
			EndDate:   "",
			PageSize:  2,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.PocketTransactions(), nil).Once()
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.CounterPartyTransactionEntryForPocketTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		expectedResponse := responses.GetAllTransactionsPocketTxnsResponse()
		response, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("happy-path-with-grab-txns", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "",
			EndDate:   "",
			PageSize:  1,
		}

		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(resources.GetCounterPartyDisplayNameData()[3:], nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows()[3:], nil)
		storage.PaymentDetailD = mockStorageDAO

		expectedResponse := responses.GetGrabTransactionsPocketTxnsResponse()
		response, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.NoError(t, err)
		assert.Equal(t, expectedResponse, response)
	})

	t.Run("error-path-when-transaction-data-db-throws-error", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
			StartDate: "",
			EndDate:   "",
			PageSize:  1,
		}
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find",
			mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything,
		).Return(nil, customErr.DefaultInternalServerError)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows()[3:], nil)
		storage.PaymentDetailD = mockStorageDAO

		_, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.Error(t, customErr.DefaultInternalServerError, err)
	})
}

func TestTransactionListDBFilters(t *testing.T) {
	t.Run("happyPath", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "12345",
			StartDate: "2021-08-01",
			EndDate:   "2021-08-31",
			PageSize:  2,
		}
		var cursorData dto.PaginationCursor
		filters := transactionListDBFilters(context.Background(), request, cursorData, EmptySearchTransactionLimitFilter)
		assert.Equal(t, 8, len(filters))
	})

	t.Run("should filter transactions in 6 months if SearchTransactionLimit is provided", func(t *testing.T) {
		featureFlag := &featureflag.MockRepo{}
		featureFlag.On("IsTransactionsSearchDurationLimitEnabled").Return(true)
		ctx := featureflag.NewContextWithFeatureFlags(context.Background(), featureFlag)
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "12345",
			EndDate:   "2021-08-31",
			PageSize:  1,
		}
		var cursorData dto.PaginationCursor
		minStartDate := time.Date(2024, 8, 19, 0, 0, 0, 0, storage.TimeZoneLocation)
		filters := transactionListDBFilters(ctx, request, cursorData, SearchTransactionLimit{
			MinStartDate: &minStartDate,
		})
		assert.Equal(t, 8, len(filters))
	})
}

func TestFilterTransactionsOnAccountPhaseAndType(t *testing.T) {
	t.Run("all-posting-committed-phase", func(t *testing.T) {
		req := &api.GetTransactionsHistoryRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.AllCommittedPosting()
		finalDBResponse, clientBatchTxIDs := filterTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"efg123abd", "abc123efg", "efg1111abd"}}, clientBatchTxIDs)
		assert.Equal(t, resources.AllCommittedPosting(), finalDBResponse)
	})

	t.Run("reversal posting, only show outgoing cancelled and not incoming cancelled", func(t *testing.T) {
		req := &api.GetTransactionsHistoryRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.AllReleasePosting()
		finalDBResponse, clientBatchTxIDs := filterTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string(nil)}, clientBatchTxIDs)
		assert.Equal(t, []*storage.TransactionsData(nil), finalDBResponse)
	})

	t.Run("reversal posting, only show outgoing cancelled", func(t *testing.T) {
		req := &api.GetTransactionsHistoryRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.FailedFASTPosting()
		finalDBResponse, clientBatchTxIDs := filterTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"abc123efg"}}, clientBatchTxIDs)
		assert.Equal(t, []*storage.TransactionsData{resources.FailedFASTPosting()[0]}, finalDBResponse)
	})

	t.Run("committed-and-pending-outgoing-phase", func(t *testing.T) {
		req := &api.GetTransactionsHistoryRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.CommittedAndPendingOutgoingPosting()
		finalDBResponse, clientBatchTxIDs := filterTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"efg123abd", "efg1111abd"}}, clientBatchTxIDs)
		assert.Equal(t, responses.FilterTransactionsCommittedAndPendingOutgoingPostingResponse(), finalDBResponse)
	})

	t.Run("all-pending-incoming-phase", func(t *testing.T) {
		req := &api.GetTransactionsHistoryRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.AllPendingIncomingPosting()
		finalDBResponse, clientBatchTxIDs := filterTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string(nil)}, clientBatchTxIDs)
		assert.Equal(t, []*storage.TransactionsData(nil), finalDBResponse)
	})

	t.Run("committed-and-pending-incoming-phase", func(t *testing.T) {
		req := &api.GetTransactionsHistoryRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.CommittedAndPendingIncomingPosting()
		finalDBResponse, clientBatchTxIDs := filterTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"efg123abd"}}, clientBatchTxIDs)
		assert.Equal(t, responses.FilterTransactionsCommittedAndPendingIncomingPostingResponse(), finalDBResponse)
	})

	t.Run("all-posting-outgoing-phase", func(t *testing.T) {
		req := &api.GetTransactionsHistoryRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.AllPendingOutgoingPosting()
		finalDBResponse, clientBatchTxIDs := filterTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"efg123abd", "abc123efg", "efg1111abd"}}, clientBatchTxIDs)
		assert.Equal(t, resources.AllPendingOutgoingPosting(), finalDBResponse)
	})

	t.Run("interest_payout_filtering_reverse_order", func(t *testing.T) {
		req := &api.GetTransactionsHistoryRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.InterestPayoutTransactionReverseOrderInput()
		finalDBResponse, clientBatchTxIDs := filterTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"APPLY_ACCRUED_DEPOSIT_INTEREST_8880986487_5_APPLY_ACCRUED_DEPOSIT_INTEREST_1651248060000000000"}}, clientBatchTxIDs)
		assert.Equal(t, constants.CREDIT, finalDBResponse[0].DebitOrCredit)
		assert.Equal(t, "DEFAULT", finalDBResponse[0].AccountAddress)
	})

	t.Run("interest_payout_filtering_ordered_input", func(t *testing.T) {
		req := &api.GetTransactionsHistoryRequest{AccountID: "12345", PageSize: 2}
		dbResponse := resources.InterestPayoutTransactionOrderedInput()
		finalDBResponse, clientBatchTxIDs := filterTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, dto.ClientTypesBatchIDs{PaymentBatchIDs: []string{"APPLY_ACCRUED_DEPOSIT_INTEREST_8880986487_5_APPLY_ACCRUED_DEPOSIT_INTEREST_1651248060000000000"}}, clientBatchTxIDs)
		assert.Equal(t, constants.CREDIT, finalDBResponse[0].DebitOrCredit)
		assert.Equal(t, "DEFAULT", finalDBResponse[0].AccountAddress)
	})

	t.Run("pocket_transactions", func(t *testing.T) {
		req := &api.GetTransactionsHistoryRequest{AccountID: "**********", PageSize: 2}
		dbResponse := resources.PocketTransactions()
		finalDBResponse, _ := filterTransactionsOnAccountPhaseAndType(dbResponse, req)
		assert.Equal(t, constants.DEBIT, finalDBResponse[0].DebitOrCredit)
		assert.Equal(t, "DEFAULT", finalDBResponse[0].AccountAddress)
	})
}

func TestFetchPaymentDetail(t *testing.T) {
	t.Run("empty-transactions-data", func(t *testing.T) {
		var clientBatchTxIDs []string
		request := &api.GetTransactionsHistoryRequest{AccountID: "**********"}
		response := fetchPaymentDetail(context.Background(), clientBatchTxIDs, request)
		assert.Equal(t, map[string]*storage.PaymentDetail{}, response)
	})

	t.Run("no-matching-transaction", func(t *testing.T) {
		// mocking PaymentDetail Call
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		storage.PaymentDetailD = mockStorageDAO

		clientBatchTxIDs := []string{"abc123efg"}
		request := &api.GetTransactionsHistoryRequest{AccountID: "**********"}
		response := fetchPaymentDetail(context.Background(), clientBatchTxIDs, request)
		assert.Equal(t, map[string]*storage.PaymentDetail{}, response)
	})

	t.Run("happy-path", func(t *testing.T) {
		// mocking PaymentDetail Call
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows()[:3], nil)
		storage.PaymentDetailD = mockStorageDAO

		exportedResponse := responses.FetchPaymentDetailHappyPath()
		clientBatchTxIDs := []string{"abc123efg", "efg123abd", "efg1111abd"}
		request := &api.GetTransactionsHistoryRequest{AccountID: "**********"}
		response := fetchPaymentDetail(context.Background(), clientBatchTxIDs, request)
		assert.Equal(t, exportedResponse, response)
	})
}

func TestGetAllRewardCashbackTransactions(t *testing.T) {
	defer func() { config.SetTenant("") }()
	locale := utils.GetLocale()
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
	storage.PaymentDetailD = mockStorageDAO

	mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
	mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
	storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(responses.ListCASAAccountsForCustomerDetailResponse(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Withdrawal:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferIn:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferOut:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferFee:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TaxOnInterest:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Adjustment:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}

	os.Setenv("LOCALISATION_PATH", "../../localise")
	config.SetTenant(tenants.TenantMY)
	constants.InitializeDynamicConstants(mockAppConfig)
	localise.Init(mockAppConfig)
	g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("GX rewards cashback created by Ops", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
		}
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.GXRewardCashBackTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.NoError(t, err)
		assert.Equal(t, "GX reward", response.Data[0].DisplayName)
	})

	t.Run("Rewards cashback from normal campaign", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
		}
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.NormalRewardCashBackTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.NoError(t, err)
		assert.Equal(t, "Add money reward", response.Data[0].DisplayName)
	})
}

func TestGetTransactionsResponseGeneratorWithCaptureAmount(t *testing.T) {
	locale := utils.GetLocale()
	// mocking PaymentDetail Call
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(resources.PaymentDetailMockDBRows(), nil)
	storage.PaymentDetailD = mockStorageDAO

	mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
	storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

	mockLoanDetailStorageDAO := &storage.MockILoanDetailDAO{}
	mockLoanDetailStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(resources.LoanDetailMockDBRows(), nil)
	storage.LoanDetailD = mockLoanDetailStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(responses.ListCASAAccountsForCustomerDetailResponse(), nil)
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		Locale: config.Locale{Language: "id"},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("happy-path-for-card-txn-use-case-capture-amount", func(t *testing.T) {
		dbRows := resources.TransactionsDataMockDBRowsForCardTxnUseCaseRequest()
		mockCard := resources.CardTxnDetailMockDBRows()
		mockCard[0].CaptureAmountTillDate = 500
		mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockCard, nil)
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "************",
			PageSize:  50,
		}
		cursorData := dto.PaginationCursor{}
		resExpected := responses.GetAllTransactionsForCardTxnResponse()
		resExpected.Data[1].Amount = -500
		response := g.getTransactionsResponseGenerator(context.Background(), dbRows, request, cursorData)
		assert.Equal(t, resExpected, response)
	})
}

func TestUnclaimedMoniesTransactions(t *testing.T) {
	defer func() { config.SetTenant("") }()
	locale := utils.GetLocale()
	mockStorageDAO := &storage.MockIPaymentDetailDAO{}
	mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
	storage.PaymentDetailD = mockStorageDAO

	mockCardTxnDetailStorageDAO := &storage.MockICardTransactionDetailDAO{}
	mockCardTxnDetailStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
	storage.CardTransactionDetailD = mockCardTxnDetailStorageDAO

	mockAccountServiceClient := &accountServiceMock.AccountService{}
	mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(responses.GetAccountDetailsByAccountIDResponse(), nil)
	mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(responses.ListCASAAccountsForCustomerDetailResponse(), nil)

	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
		IconConfig: config.IconConfig{
			SavingsPocketTransfer:   "https://assets.sgbank.dev/dev/txHistory/images/FundsIn.png",
			SavingsPocketWithdrawal: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			DefaultTransaction:      "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Withdrawal:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferIn:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferOut:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TransferFee:             "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			InterestPayout:          "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			TaxOnInterest:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Adjustment:              "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
		},
	}

	os.Setenv("LOCALISATION_PATH", "../../localise")
	config.SetTenant(tenants.TenantMY)
	constants.InitializeDynamicConstants(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
	localise.Init(mockAppConfig)
	g := GetAccountTransactionsSearchStruct{AccountServiceClient: mockAccountServiceClient}

	t.Run("GX Unclaimed Monies transfer", func(t *testing.T) {
		request := &api.GetTransactionsHistoryRequest{
			AccountID: "**********",
		}
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.UnclaimedMoniesTransferTransactions(), nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		response, err := g.GetAllTransactionsFromDB(context.Background(), request, EmptySearchTransactionLimitFilter)
		assert.NoError(t, err)
		assert.Equal(t, "GX Unclaimed Balance Account", response.Data[0].DisplayName)
		assert.Equal(t, "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png", response.Data[0].IconURL)
	})
}

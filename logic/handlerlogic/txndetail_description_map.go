package handlerlogic

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
)

// InternalTransactionFuncTemplate ...
type InternalTransactionFuncTemplate func(str ...string) string

// PaymentsTransactionFuncTemplate ...
type PaymentsTransactionFuncTemplate func(tx context.Context, dbData interface{}, counterParty string) (string, error)

// generateFormattedDescription : Generates the Transaction description.
func generateFormattedDescription(ctx context.Context, txnData *storage.TransactionsData, paymentData *storage.PaymentDetail, counterparty string) (string, error) {
	if paymentData == nil {
		description, err := getDepositsTransactionDetail(ctx, txnData, counterparty)
		if err != nil {
			return "", err
		}
		return description, nil
	}
	if utils.SearchStringArray(constants.SendMoneyFeeTransactionTypes, txnData.TransactionType) {
		return localise.Translate(constants.TransactionFee), nil
	}
	description, err := getPaymentsTransactionDetail(ctx, paymentData, counterparty)
	if err != nil {
		return "", err
	}
	return description, nil
}

func getDepositsTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var response string
	var interestOrTaxDisplayName string
	txnData := dbData.(*storage.TransactionsData)
	transactionTypeMap := make(map[string]InternalTransactionFuncTemplate)
	if utils.SearchStringArray(constants.InterestPayoutTransactionTypes, txnData.TransactionType) {
		interestOrTaxDisplayName = localise.Translate(constants.InterestEarned)
	}
	if utils.SearchStringArray(constants.TaxPayoutTransactionTypes, txnData.TransactionType) {
		interestOrTaxDisplayName = localise.Translate(constants.TaxOnInterestDesc)
	}
	transactionTypeMap[constants.InterestPayoutTransactionSubType] = getInterestOrTaxPayoutTransactionDetail
	transactionTypeMap[constants.PocketFundingTransactionSubType] = getPocketFundingTransactionDetail
	transactionTypeMap[constants.PocketWithdrawalTransactionSubType] = getPocketWithdrawalTransactionDetail
	transactionTypeMap[constants.OPSTransactionSubType] = getOPSTransactionDetail

	// TODO: for the manual postings
	if function, ok := transactionTypeMap[txnData.TransactionSubtype]; ok {
		response = function(counterParty, getTransactionDirection(txnData.DebitOrCredit), interestOrTaxDisplayName)
		return response, nil
	}
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailHandlerLogTag, fmt.Sprintf("Invalid Transaction type: %s", txnData.TransactionType))
	return "", fmt.Errorf("Invalid Transaction type: %s", txnData.TransactionType) // nolint:stylecheck
}

func getOPSTransactionDetail(...string) string {
	return localise.Translate(constants.TransactionAdjustment)
}

func getInterestOrTaxPayoutTransactionDetail(str ...string) string {
	return str[2]
}

func getPaymentsTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	paymentData := dbData.(*storage.PaymentDetail)
	paymentTypeMap := make(map[string]PaymentsTransactionFuncTemplate)
	paymentTypeMap[constants.IntraBank] = getIntraBankTransactionDetail
	paymentTypeMap[constants.RtolAlto] = getRtolTransactionDetail
	paymentTypeMap[constants.RtolAJ] = getRtolTransactionDetail
	paymentTypeMap[constants.RPP] = getRPPTransactionDetail
	if function, ok := paymentTypeMap[paymentData.TransactionSubType]; ok {
		response, err := function(ctx, paymentData, counterParty)
		if err != nil {
			return "", err
		}
		return response, nil
	}
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailHandlerLogTag, fmt.Sprintf("Invalid paymentTransaction subType: %s", paymentData.TransactionSubType))
	return "", fmt.Errorf("Invalid paymentTransaction subType: %s", paymentData.TransactionSubType) // nolint:stylecheck
}

func getIntraBankTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var desc string
	paymentData := dbData.(*storage.PaymentDetail)
	desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.BankTransfer), getPaymentDirection(paymentData.Amount), counterParty)
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Intrabank Transaction description is completed")
	return desc, nil
}

func getRtolTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var desc string
	paymentData := dbData.(*storage.PaymentDetail)
	desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.OnlineTransfer), getPaymentDirection(paymentData.Amount), counterParty)
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "Rtol Transaction description is completed")
	return desc, nil
}

func getRPPTransactionDetail(ctx context.Context, dbData interface{}, counterParty string) (string, error) {
	var desc string
	paymentData := dbData.(*storage.PaymentDetail)
	desc = fmt.Sprintf("%s %s %s", localise.Translate(constants.RPPNormalTransfer), getPaymentDirection(paymentData.Amount), counterParty)
	slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "RPP Transaction description is completed")
	return desc, nil
}

func getTransactionDirection(debitOrCredit string) string {
	if debitOrCredit == constants.CREDIT {
		return localise.Translate(constants.IN)
	}
	return localise.Translate(constants.OUT)
}

func getPaymentDirection(amount int64) string {
	var txnDirection string
	if amount < 0 {
		txnDirection = localise.Translate(constants.OUT)
	} else if amount > 0 {
		txnDirection = localise.Translate(constants.IN)
	}
	return txnDirection
}

func getPocketFundingTransactionDetail(str ...string) string {
	response := fmt.Sprintf("%s %s %s", localise.Translate(constants.FundMoved), str[1], str[0])
	return response
}

func getPocketWithdrawalTransactionDetail(str ...string) string {
	response := fmt.Sprintf("%s %s %s", localise.Translate(constants.WithdrawalDesc), str[1], str[0])
	return response
}

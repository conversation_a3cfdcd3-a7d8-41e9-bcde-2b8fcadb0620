package handlerlogic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	"gitlab.myteksi.net/dakota/transaction-history/logic/helper"

	"gitlab.myteksi.net/dakota/transaction-history/logic"

	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetCxTransactionListValidator validates the request parameters
func GetCxTransactionListValidator(req *dto.TransactionHistorySearchRequest) error {
	var errMsgs []string
	var errors []servus.ErrorDetail

	accountID := req.AccountID

	if accountID == "" {
		errMessage := "`accountID` is a mandatory field"
		errors = append(errors, servus.ErrorDetail{
			Message: errMessage,
		})
		errMsgs = append(errMsgs, errMessage)
	}

	// Check if the `accountID` does not start with the required prefix "8888" or "8188". (main deposit ID or main biz deposit acc ID)
	if accountID != "" && !helper.IsDepositsAccountID(accountID) && !helper.IsBizDepositsAccountID(accountID) {
		errMessage := "Invalid `accountID`"
		errors = append(errors, servus.ErrorDetail{
			Message: errMessage,
		})
		errMsgs = append(errMsgs, errMessage)
	}

	if req.PageSize > MaxPageSize {
		errMessage := "`pageSize` greater than maxPageSize"
		errors = append(errors, servus.ErrorDetail{
			Message: errMessage,
		})
		errMsgs = append(errMsgs, errMessage)
	}

	if req.PageSize < MinPageSize {
		errMessage := "`pageSize` less than minPageSize"
		errors = append(errors, servus.ErrorDetail{
			Message: errMessage,
		})
		errMsgs = append(errMsgs, errMessage)
	}

	if len(errMsgs) > 0 {
		errorCode := customErr.BadRequest
		return servus.ServiceError{
			HTTPCode: errorCode.HTTPStatusCode(),
			Code:     string(errorCode),
			Message:  strings.Join(errMsgs, ", "),
			Errors:   errors,
		}
	}

	return nil
}

// GetAllCxTransactionsFromDB prepare filters, fetch transactions and format response
// nolint:dupl
func (g *GetAccountTransactionsSearchStruct) GetAllCxTransactionsFromDB(ctx context.Context, req *dto.TransactionHistorySearchRequest) (*dto.TransactionsHistorySearchResponse, error) {
	if req.PageSize == MinPageSize {
		req.PageSize = DefaultPageSize
	}
	// parse the cursors if present
	cursorData, err := parsePaginationCursorData(req.StartingBefore, req.EndingAfter)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("Error parsing pagination cursor, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("Parsed cursor data: %s", utils.ToJSON(cursorData)))

	// creating DB filters
	filters := cxTransactionListDBFilters(req, cursorData)
	startTimeForFiltering := time.Now()
	dbResponse, err := storage.TransactionsDataD.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("Error getting data from transactionsData DB, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("time taken to fetch data from DB :%s", time.Since(startTimeForFiltering)))
	if len(dbResponse) == 0 || err == data.ErrNoData {
		slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("No data present in the database for the account: %s", req.AccountID))
		return emptyGetCxTransactionsHistoryResponse(), nil
	}
	response, err := g.getCxTransactionsResponseGenerator(ctx, dbResponse, req, cursorData)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("Error generate cx transaction response, err: %s", err.Error()))
		return nil, customErr.BuildErrorResponse(customErr.BadRequest, err.Error())
	}
	slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("time taken filter the DB response  :%s", time.Since(startTimeForFiltering)))
	return response, nil
}

func getTransactionTypesToExclude() []string {
	transactionTypesToExclude := append(constants.InterestAccrualTransactionTypes, constants.InterestPayoutTransactionType)
	return transactionTypesToExclude
}

// emptyGetTransactionsHistoryResponse will generate empty response structure in case no matching txns is found
func emptyGetCxTransactionsHistoryResponse() *dto.TransactionsHistorySearchResponse {
	response := &dto.TransactionsHistorySearchResponse{}
	response.Links = map[string]string{"prev": "", "prevCursorID": "", "next": "", "nextCursorID": ""}
	response.Data = []dto.TransactionHistorySearchData{}
	return response
}

// cxTransactionListDBFilters creates filter condition
// nolint: dupl, funlen
func cxTransactionListDBFilters(req *dto.TransactionHistorySearchRequest, cursorData dto.PaginationCursor) []data.Condition {
	// add default filters. Here InterestPayoutTransactionType is excluded from the perspective of pockets
	var filters []data.Condition

	filters = append(filters, data.EqualTo("AccountID", req.AccountID))

	filters = append(filters,
		data.Limit(int(6*req.PageSize)), // TODO : Just fetch req.PageSize no. of txns, since the below filters will return only valid txns from DB
		data.Or(
			data.And(data.EqualTo("TransactionType", constants.InterestPayoutTransactionType), data.EqualTo("AccountAddress", constants.DefaultAccountAddress)),
			data.NotContainedIn("TransactionType", utils.ConvertToInterface(getTransactionTypesToExclude())...),
		),
		data.Or(
			data.EqualTo("DebitOrCredit", constants.DEBIT),
			data.And(data.EqualTo("DebitOrCredit", constants.CREDIT),
				data.Or(
					data.EqualTo("BatchStatus", constants.BatchStatusAccepted),
					data.And(data.EqualTo("BatchStatus", constants.BatchStatusRejected), data.UnEqualTo("AccountAddress", constants.DefaultAccountAddress)),
				)),
		))

	if req.TransactionDomain != "" {
		filters = append(filters, data.EqualTo("TransactionDomain", req.TransactionDomain))
	}

	if req.TransactionType != "" {
		// Replace all spaces with an empty string
		trimmedTransactionType := strings.Replace(req.TransactionType, " ", "", -1)
		transactionTypes := strings.Split(trimmedTransactionType, ",")
		filters = append(filters, data.ContainedIn("TransactionType", utils.ConvertToInterface(transactionTypes)...))
	}

	if req.TransactionSubtype != "" {
		filters = append(filters, data.EqualTo("TransactionSubtype", req.TransactionSubtype))
	}

	switch {
	case req.EndingAfter == "" && req.StartingBefore == "":
		filters = append(filters,
			data.DescendingOrder("BatchValueTimestamp"),
			data.DescendingOrder("ID"))
	case req.EndingAfter != "":
		filters = append(filters,
			data.GreaterThan("ID", cursorData.ID),
			data.AscendingOrder("BatchValueTimestamp"),
			data.AscendingOrder("ID"),
			data.UnEqualTo("ClientBatchID", cursorData.TransactionID),
		)
	case req.StartingBefore != "":
		filters = append(filters,
			data.LessThan("ID", cursorData.ID),
			data.DescendingOrder("BatchValueTimestamp"),
			data.DescendingOrder("ID"),
			data.UnEqualTo("ClientBatchID", cursorData.TransactionID),
		)
	}

	startingDate, endingDate := computeDateRange(req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, cursorData)
	if startingDate != "" {
		filters = append(filters, data.GreaterThanOrEqualTo("BatchValueTimestamp", startingDate))
	}
	if endingDate != "" {
		filters = append(filters, data.LessThanOrEqualTo("BatchValueTimestamp", endingDate))
	}
	return filters
}

// getCxTransactionsResponseGenerator contains logic to generate response from DB data after filtering.
func (g *GetAccountTransactionsSearchStruct) getCxTransactionsResponseGenerator(ctx context.Context, dbResponse []*storage.TransactionsData, req *dto.TransactionHistorySearchRequest, cursorData dto.PaginationCursor) (*dto.TransactionsHistorySearchResponse, error) {
	// method to filter out committed and pending.
	finalDBResponse := filterCxTransactionsOnAccountPhaseAndType(dbResponse, req)

	if len(finalDBResponse) == 0 {
		return emptyGetCxTransactionsHistoryResponse(), nil
	}
	clientBatchIDs := getPaymentAndCardClientBatchIDs(finalDBResponse)
	// fetch corresponding payment details
	transactionsPaymentDetail := fetchCxPaymentDetail(ctx, clientBatchIDs.PaymentBatchIDs, req)

	// fetch corresponding card payment details
	transactionsCardDetail := fetchCardTransactionDetail(ctx, clientBatchIDs.CardBatchIDs)

	// fetch corresponding loan details
	transactionsLoanDetail := fetchCxLoanDetail(ctx, clientBatchIDs.LoanBatchIDs)

	var transactionsList []dto.TransactionHistorySearchData

	pageSize := int(req.PageSize)
	finalDBResponseLen := len(finalDBResponse)

	if req.EndingAfter != "" && pageSize > finalDBResponseLen {
		slog.FromContext(ctx).Debug(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("request pageSize %d exceed finalDBResponseLen: %d", pageSize, finalDBResponseLen))
		return nil, errors.New("pageSize exceed response length when fetching previous page")
	}

	transactionsPerPage := utils.MinInt(pageSize, finalDBResponseLen)

	// invert final DB entries to show them in descending order
	if req.EndingAfter != "" {
		finalDBResponse = invertDBResponseForBackwardScrolling(finalDBResponse[:transactionsPerPage])
	}

	// iterating over all transactions
	for _, transaction := range finalDBResponse[:transactionsPerPage] {
		isChildAccount, _ := g.isChildAccount(ctx, transaction.AccountID)
		if transaction.IsCardDomain() && !transaction.IsCardOpsTxn() && !transaction.IsRewardsTxn() && !transaction.IsCardMaintenanceFeeTxn() {
			transactionsList = append(transactionsList, cardTransactionResponse(ctx, transaction, transactionsCardDetail))
		} else if transaction.IsLendingDomain() {
			transactionsList = append(transactionsList, loanTransactionResponse(ctx, transaction, transactionsLoanDetail, transactionsPaymentDetail))
		} else {
			transactionsList = append(transactionsList, paymentTransactionResponse(ctx, transaction, transactionsPaymentDetail, isChildAccount))
		}
	}
	paginationParams := utils.MapPaginationParameters(req.AccountID, req.StartingBefore, req.EndingAfter, req.StartDate, req.EndDate, req.PageSize)
	links := paginationLinks(finalDBResponse, paginationParams, cursorData)
	return &dto.TransactionsHistorySearchResponse{Links: links, Data: transactionsList}, nil
}

// filterTransactionsOnAccountPhaseAndType this method will ensure only one entry per batch will be present
// Logic (** ORDER MATTERS **): Based on
// https://wiki.grab.com/pages/viewpage.action?spaceKey=Digibank&title=Product+requirements+for+Transaction+History%2C+Transaction+Detail+and+Statements+for+R2
// nolint: gocognit
func filterCxTransactionsOnAccountPhaseAndType(dbResponse []*storage.TransactionsData, req *dto.TransactionHistorySearchRequest) []*storage.TransactionsData {
	filteredDBResponse := filterOutApplyEarmarkFailedTransfer(dbResponse, constants.DefaultAccountAddress)
	// Filter out settled or released card txns
	filteredDBResponse = filterOutSettledOrReleasedCardTxn(filteredDBResponse, constants.DefaultAccountAddress)
	// Storing clientBatchID to maintain the order in which fetched from DB
	// clientBatchIDOrder, clientBatchIDToTransactionsDataMap := createClientBatchIDToTransactionsDataMap(dbResponse)
	clientBatchIDOrder, clientBatchIDToTransactionsDataMap := createClientBatchIDToTransactionsDataMap(filteredDBResponse)

	// Filtering bases on AccountPhase
	for clientBatchID, transactions := range clientBatchIDToTransactionsDataMap {
		var committedPostingTx, pendingOutGoingTx, releaseTx []*storage.TransactionsData
		for _, item := range transactions {
			if item.AccountAddress != constants.DefaultAccountAddress {
				continue
			}
			if item.AccountID != req.AccountID && len(transactions) > 1 {
				continue
			}
			if item.AccountPhase == constants.PostingPhaseCommitted {
				committedPostingTx = append(committedPostingTx, item)
			} else if item.AccountPhase == constants.PostingPhasePendingOutgoing {
				if item.TmTransactionType == constants.OutboundAuthorisation {
					pendingOutGoingTx = append(pendingOutGoingTx, item)
				} else if item.TmTransactionType == constants.Release && item.TransactionType == constants.ReleaseEarmarkTransactionType {
					releaseTx = append(releaseTx, item)
				}
			}
		}
		clientBatchIDToTransactionsDataMap[clientBatchID] = []*storage.TransactionsData{}
		// this is a folding ops, we select one posting instructions that would conclude the batch status and ignore the rest
		if committedPostingTx != nil {
			clientBatchIDToTransactionsDataMap[clientBatchID] = committedPostingTx
		} else if releaseTx != nil {
			clientBatchIDToTransactionsDataMap[clientBatchID] = releaseTx
		} else if pendingOutGoingTx != nil {
			clientBatchIDToTransactionsDataMap[clientBatchID] = pendingOutGoingTx
		}
		slog.FromContext(context.Background()).Info(constants.GetAccountTransactionsSearchForCXLogTag,
			fmt.Sprintf("database row selected for batchID: %s, transaction to show: %s", clientBatchID,
				strings.Join(
					lo.Map(clientBatchIDToTransactionsDataMap[clientBatchID], func(item *storage.TransactionsData, _ int) string {
						return strconv.FormatUint(item.ID, 10)
					}), ","),
			))
	}

	// computing number of rows to be added
	filteredRowsCount := computeNumberOfTransactionsInFilteredDBResponse(req.StartingBefore, req.EndingAfter, req.PageSize)

	// Select transactions as per required pageSize
	finalDBResponse := createCxFinalDBTransactionList(clientBatchIDOrder, clientBatchIDToTransactionsDataMap, filteredRowsCount)

	// db fetch data in ascending order when EndingAfter is passed in
	// this might cause sorting issue as the transactions are sorted in authorization timestamp (if any) instead of settlement/released timestamp
	if req.EndingAfter != "" {
		sort.Sort(storage.SortByBatchValueTS(finalDBResponse))
	}
	return finalDBResponse
}

// // createClientBatchIDToTransactionsDataMapAndTxnOrder method to group txnData by clientBatchID,
// // so that one entry per clientBatchID can be return in response.
// // clientBatchIDOrder determines the order in which transaction should appear in the final list.
// func createClientBatchIDToTransactionsDataMapAndTxnOrder(dbResponse []*storage.TransactionsData) ([]string, map[string][]*storage.TransactionsData) {
//	var clientBatchIDOrder []string
//	clientBatchIDToTransactionsDataMap := make(map[string][]*storage.TransactionsData)
//	idPositionMap := make(map[string]int)
//	for _, transactionData := range dbResponse {
//		if _, ok := clientBatchIDToTransactionsDataMap[transactionData.ClientBatchID]; !ok {
//			clientBatchIDOrder = append(clientBatchIDOrder, transactionData.ClientBatchID)
//			idPositionMap[transactionData.ClientBatchID] = len(clientBatchIDOrder) - 1
//			clientBatchIDToTransactionsDataMap[transactionData.ClientBatchID] = []*storage.TransactionsData{transactionData}
//		} else {
//			if (transactionData.AccountPhase == constants.PostingPhasePendingOutgoing && transactionData.TmTransactionType == constants.OutboundAuthorisation) || (transactionData.AccountPhase == constants.PostingPhaseCommitted && transactionData.DebitOrCredit == constants.CREDIT) {
//				clientBatchIDOrder = append(clientBatchIDOrder[:idPositionMap[transactionData.ClientBatchID]], clientBatchIDOrder[idPositionMap[transactionData.ClientBatchID]+1:]...)
//				clientBatchIDOrder = append(clientBatchIDOrder, transactionData.ClientBatchID)
//				idPositionMap = fetchReorderedClientBatchIDPosition(idPositionMap, clientBatchIDOrder)
//			}
//			clientBatchIDToTransactionsDataMap[transactionData.ClientBatchID] = append(clientBatchIDToTransactionsDataMap[transactionData.ClientBatchID], transactionData)
//		}
//	}
//	return clientBatchIDOrder, clientBatchIDToTransactionsDataMap
// }

func getPaymentAndCardClientBatchIDs(transactions []*storage.TransactionsData) (clientBatchIDs dto.ClientTypesBatchIDs) {
	var cardBatchIDs, paymentBatchIDs, loanBatchIDs []string
	for _, transaction := range transactions {
		if transaction.IsCardDomain() && !transaction.IsCardOpsTxn() && !transaction.IsRewardsTxn() {
			cardBatchIDs = append(cardBatchIDs, transaction.ClientBatchID)
		} else if transaction.IsLendingDomain() {
			loanBatchIDs = append(loanBatchIDs, transaction.ClientBatchID)
			paymentBatchIDs = append(paymentBatchIDs, transaction.ClientBatchID) // we will need the payment detail for LENDING trx
		} else {
			paymentBatchIDs = append(paymentBatchIDs, transaction.ClientBatchID)
		}
	}

	return dto.ClientTypesBatchIDs{
		PaymentBatchIDs: paymentBatchIDs,
		CardBatchIDs:    cardBatchIDs,
		LoanBatchIDs:    loanBatchIDs,
	}
}

// nolint:dupl
// fetchCardTransactionDetail gets card related metadata related to transaction.
func fetchCardTransactionDetail(ctx context.Context, clientBatchTxIDs []string) map[string]*storage.CardTransactionDetail {
	response := make(map[string]*storage.CardTransactionDetail)
	// return if no client transaction batch id present
	if len(clientBatchTxIDs) == 0 {
		slog.FromContext(ctx).Info(constants.GetAccountTransactionsSearchForCXLogTag, "No card transaction IDs provided")
		return response
	}

	// create filters and search in CardTransactionID detail table
	filters := []data.Condition{
		data.ContainedIn("CardTransactionID", utils.ConvertToInterface(clientBatchTxIDs)...),
	}
	dbResponse, err := storage.CardTransactionDetailD.Find(ctx, filters...)

	if err != nil || len(dbResponse) == 0 {
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetAccountTransactionsSearchForCXLogTag,
				fmt.Sprintf("Failed to fetch card transaction details, err:  %s", err.Error()))
		} else {
			slog.FromContext(ctx).Warn(constants.GetAccountTransactionsSearchForCXLogTag, "No details found for the provided card transactions")
		}
		return response
	}

	// creating map
	for _, cardTransactionDetail := range dbResponse {
		response[cardTransactionDetail.CardTransactionID] = cardTransactionDetail
	}
	return response
}

// nolint: funlen
func cardTransactionResponse(ctx context.Context, transaction *storage.TransactionsData,
	transactionsCardDetail map[string]*storage.CardTransactionDetail) dto.TransactionHistorySearchData {
	var (
		displayName                   string
		cardDetail                    *storage.CardTransactionDetail
		isPartialSettlement           = false
		captureAmountTillDate         int64
		captureOriginalAmountTillDate int64
	)

	status := getCardTransactionStatus(transactionsCardDetail[transaction.ClientBatchID])
	amountInCents := logic.GetTransactionAmount(ctx, transaction, transactionsCardDetail[transaction.ClientBatchID])
	iconURL := getIconURLForCardsTransaction(transaction.TransactionType, transaction.TransactionSubtype)
	// accountIconDetail := getAccountIconDetail(iconURL, counterPartyAccountID, clientBatchIDToPocketIDMap[transaction.ClientBatchID])
	cardTransactionDetail := buildCardTransactionDetailResponse(ctx, transactionsCardDetail[transaction.ClientBatchID], transaction)

	if transaction.ClientBatchID != "" {
		detail, ok := transactionsCardDetail[transaction.ClientBatchID]
		if ok {
			cardDetail = detail
		}
	}

	if cardDetail == nil {
		return defaultCxSearchResponse(ctx, transaction)
	}
	captureAmountTillDate = logic.FormattedAmountInCents(transaction, cardDetail.CaptureAmountTillDate)
	captureOriginalAmountTillDate = logic.FormattedAmountInCents(transaction, cardDetail.CaptureOriginalAmountTillDate)
	if cardDetail.CaptureAmountTillDate > 0 {
		isPartialSettlement = true
	}

	key := transaction.GetTxnScenarioKey()
	helperFuncs := presenterhelper.TransactionResponseHelperFuncs[key]
	displayName = presenterhelper.GetCardTransactionDisplayNameForListing(ctx, transaction, cardDetail)
	transactionDescription, _ := helperFuncs.DescriptionFunc(ctx, cardDetail, displayName)
	if _, ok := constants.ATMFeeTransactionTypes[transaction.TransactionType]; ok {
		transactionDescription = presenterhelper.GetDBMYCardAtmFeeTransactionDecription(transaction, displayName)
	}

	counterParty := dto.CounterParty{
		DisplayName: displayName,
	}
	var tags []api.TransactionTags
	// format to response structure
	return dto.TransactionHistorySearchData{
		TransactionID: transaction.ClientTransactionID,
		BatchID:       transaction.ClientBatchID,
		IconURL:       iconURL,
		//	AccountIconDetail:     &accountIconDetail,
		Amount:            amountInCents,
		CounterParty:      &counterParty,
		CounterParties:    []dto.CounterParty{counterParty},
		Currency:          transaction.TransactionCurrency,
		Status:            status,
		CreationTimestamp: transaction.BatchValueTimestamp,
		TransactionCode: &dto.TransactionCode{
			Domain:  transaction.TransactionDomain,
			Type:    transaction.TransactionType,
			SubType: transaction.TransactionSubtype,
		},
		CardTransactionDetail:          cardTransactionDetail,
		Tags:                           tags,
		TransactionDescription:         transactionDescription,
		IsPartialSettlement:            isPartialSettlement,
		CapturedAmountTillDate:         captureAmountTillDate,
		CapturedOriginalAmountTillDate: captureOriginalAmountTillDate,
	}
}

// nolint: funlen
func paymentTransactionResponse(ctx context.Context, transaction *storage.TransactionsData, transactionsPaymentDetail map[string]*storage.PaymentDetail, isChildAccount bool) dto.TransactionHistorySearchData {
	var (
		txnPaymentDetail               *storage.PaymentDetail
		transactionDescription         string
		displayName                    string
		swiftCode                      string
		counterPartyAccountID          string
		counterPartyTransactionDetails map[string]string
	)

	if transaction.ClientBatchID != "" {
		detail, ok := transactionsPaymentDetail[transaction.ClientBatchID]
		if ok {
			txnPaymentDetail = detail
		}
	}
	status := presenterhelper.GetPaymentCasaTransactionStatus(transaction, txnPaymentDetail)
	amountInCents := transaction.FormattedAmount(ctx)
	iconURL := getDefaultIconURL(transaction)
	// accountIconDetail := getAccountIconDetail(iconURL, counterPartyAccountID, clientBatchIDToPocketIDMap[transaction.ClientBatchID])
	displayName = presenterhelper.GetCounterPartyDisplayNamePocketView(ctx, transaction, txnPaymentDetail, isChildAccount)

	var key = transaction.GetTxnScenarioKey()

	if txnPaymentDetail != nil {
		transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(ctx, txnPaymentDetail, displayName)
		swiftCode = txnPaymentDetail.GetCounterPartyAccount(ctx).SwiftCode
		counterPartyAccountID = txnPaymentDetail.CounterPartyAccountID
	} else {
		transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(ctx, transaction, displayName)
		if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
			counterPartyAccountID = transaction.GetPocketTxnCounterPartyAccountID(ctx)
		}
	}
	counterPartyTransactionDetails = presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(ctx, transaction, txnPaymentDetail)
	counterParty := dto.CounterParty{
		DisplayName:   displayName,
		SwiftCode:     swiftCode,
		AccountNumber: counterPartyAccountID,
		TransactionDetails: map[string]string{
			"service_type": counterPartyTransactionDetails["service_type"],
		},
	}
	// format to response structure
	res := dto.TransactionHistorySearchData{
		TransactionID: transaction.ClientTransactionID,
		BatchID:       transaction.ClientBatchID,
		IconURL:       iconURL,
		// AccountIconDetail: &accountIconDetail,
		Amount:            amountInCents,
		CounterParty:      &counterParty,
		CounterParties:    []dto.CounterParty{counterParty},
		Currency:          transaction.TransactionCurrency,
		Status:            status,
		CreationTimestamp: transaction.BatchValueTimestamp,
		TransactionCode: &dto.TransactionCode{
			Domain:  transaction.TransactionDomain,
			Type:    transaction.TransactionType,
			SubType: transaction.TransactionSubtype,
		},
		TransactionDescription: transactionDescription,
		TransactionRemarks:     getTransactionRemarks(ctx, transaction, txnPaymentDetail),
	}
	if transaction.IsCardMaintenanceFeeTxn() {
		var txnDetail dto.TransactionDetail
		err := json.Unmarshal(transaction.TransactionDetails, &txnDetail)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetAccountTransactionsSearchForCXLogTag, fmt.Sprintf("Error parsing transaction details, err: %s", err.Error()))
		} else {
			res.CardTransactionDetail = &dto.CxCardTransactionDetail{
				CardID:         txnDetail.CardID,
				TailCardNumber: txnDetail.TailCardNumber,
			}
		}
	}
	return res
}

// nolint: funlen
func loanTransactionResponse(ctx context.Context, transaction *storage.TransactionsData, transactionsLoanDetail map[string]*storage.LoanDetail, transactionsPaymentDetail map[string]*storage.PaymentDetail) dto.TransactionHistorySearchData {
	var (
		txnPaymentDetail       *storage.PaymentDetail
		txnLoanDetail          *storage.LoanDetail
		transactionDescription string
		displayName            string
		swiftCode              string
		counterPartyAccountID  string
		serviceType            string
	)

	if transaction.ClientBatchID != "" {
		detail, ok := transactionsPaymentDetail[transaction.ClientBatchID]
		if ok {
			txnPaymentDetail = detail
		}
		detail2, ok := transactionsLoanDetail[transaction.ClientBatchID]
		if ok {
			txnLoanDetail = detail2
		}
	}

	amountInCents := transaction.FormattedAmount(ctx)
	status := presenterhelper.GetLoanCasaTransactionStatus(transaction, txnLoanDetail)

	var key = transaction.GetTxnScenarioKey()
	transactionDescription, _ = presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(ctx, transaction, displayName)

	if txnLoanDetail != nil {
		displayName = presenterhelper.GetLoanTransactionDisplayNameForOpsSearch(ctx, transaction, txnLoanDetail)
		counterPartyAccountID = txnLoanDetail.AccountID
	}

	if txnPaymentDetail != nil {
		swiftCode = txnPaymentDetail.GetCounterPartyAccount(ctx).SwiftCode
		serviceType = txnPaymentDetail.GetPaymentMetadata(ctx).ServiceType
	}

	// format to response structure
	return dto.TransactionHistorySearchData{
		TransactionID: transaction.ClientTransactionID,
		BatchID:       transaction.ClientBatchID,
		// IconURL:       iconURL,
		// AccountIconDetail: &accountIconDetail,
		Amount: amountInCents,
		CounterParty: &dto.CounterParty{
			DisplayName:   displayName,
			SwiftCode:     swiftCode,
			AccountNumber: counterPartyAccountID,
			TransactionDetails: map[string]string{
				"service_type": serviceType,
			},
		},
		Currency:          transaction.TransactionCurrency,
		Status:            status,
		CreationTimestamp: transaction.BatchValueTimestamp,
		TransactionCode: &dto.TransactionCode{
			Domain:  transaction.TransactionDomain,
			Type:    transaction.TransactionType,
			SubType: transaction.TransactionSubtype,
		},
		TransactionDescription: transactionDescription,
		TransactionRemarks:     getTransactionRemarks(ctx, transaction, txnPaymentDetail),
		CounterParties:         presenterhelper.GetLoanCounterParties(ctx, transaction, txnPaymentDetail, txnLoanDetail),
	}
}

// nolint:dupl
// fetchCxPaymentDetail gets payment's metadata related to transaction.
func fetchCxPaymentDetail(ctx context.Context, clientBatchTxIDs []string, req *dto.TransactionHistorySearchRequest) map[string]*storage.PaymentDetail {
	response := make(map[string]*storage.PaymentDetail)
	// return if no client transaction batch id present
	if len(clientBatchTxIDs) == 0 {
		return response
	}

	// create filters and search in payment detail table
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.ContainedIn("TransactionID", utils.ConvertToInterface(clientBatchTxIDs)...),
	}
	dbResponse, err := storage.PaymentDetailD.Find(ctx, filters...)
	if err != nil || len(dbResponse) == 0 {
		return response
	}

	// creating map
	for _, paymentDetail := range dbResponse {
		response[paymentDetail.TransactionID] = paymentDetail
	}
	return response
}

// nolint:dupl
// fetchCxLoanDetail gets metadata related to loan transaction.
func fetchCxLoanDetail(ctx context.Context, clientBatchTxIDs []string) map[string]*storage.LoanDetail {
	response := make(map[string]*storage.LoanDetail)
	// return if no client transaction batch id present
	if len(clientBatchTxIDs) == 0 {
		return response
	}

	// create filters and search in payment detail table
	filters := []data.Condition{
		data.ContainedIn("PaymentTransactionID", utils.ConvertToInterface(clientBatchTxIDs)...),
	}
	dbResponse, err := storage.LoanDetailD.Find(ctx, filters...)
	if err != nil || len(dbResponse) == 0 {
		return response
	}

	// creating map
	for _, loanDetail := range dbResponse {
		response[loanDetail.PaymentTransactionID] = loanDetail
	}
	return response
}

// createCxFinalDBTransactionList creates final list of transactions to be shown
func createCxFinalDBTransactionList(clientBatchIDOrder []string,
	clientBatchIDToTransactionsDataMap map[string][]*storage.TransactionsData, filteredRowsCount int64) []*storage.TransactionsData {
	// Select transactions as per required pageSize
	var finalDBResponse []*storage.TransactionsData
	for _, item := range clientBatchIDOrder {
		if int64(len(finalDBResponse)) < filteredRowsCount {
			txDataList := clientBatchIDToTransactionsDataMap[item]
			if len(txDataList) != 0 {
				finalDBResponse = append(finalDBResponse, txDataList...)
			}
		}
	}
	return finalDBResponse
}

func defaultCxSearchResponse(ctx context.Context, transaction *storage.TransactionsData) dto.TransactionHistorySearchData {
	var tags []api.TransactionTags
	amountInCents := transaction.FormattedAmount(ctx)

	return dto.TransactionHistorySearchData{
		TransactionID: transaction.ClientTransactionID,
		BatchID:       transaction.ClientBatchID,
		IconURL:       "",
		Amount:        amountInCents,
		CounterParty: &dto.CounterParty{
			DisplayName: "",
		},
		Currency:          transaction.TransactionCurrency,
		Status:            transaction.BatchStatus,
		CreationTimestamp: transaction.BatchValueTimestamp,
		TransactionCode: &dto.TransactionCode{
			Domain:  transaction.TransactionDomain,
			Type:    transaction.TransactionType,
			SubType: transaction.TransactionSubtype,
		},
		CardTransactionDetail:  &dto.CxCardTransactionDetail{},
		Tags:                   tags,
		TransactionDescription: "",
	}
}

package handlerlogic

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	"gitlab.myteksi.net/dakota/common/tenants"
	customerMasterApi "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

func TestGetCASATransactionsSummaryRequestValidator(t *testing.T) {
	t.Run("accountID-parameter-missing", func(t *testing.T) {
		request := &api.GetCASATransactionsSummaryRequest{
			AccountID: "",
		}
		expectResponse := servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		}

		errorResponse := GetCASATransactionsSummaryRequestValidator(request)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})
	t.Run("valid-request", func(t *testing.T) {
		request := &api.GetCASATransactionsSummaryRequest{
			AccountID: "12345",
		}
		errorResponse := GetCASATransactionsSummaryRequestValidator(request)
		assert.Equal(t, 0, len(errorResponse))
	})
}

func TestGetTransactionsSummary(t *testing.T) {
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	t.Run("happy-path", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.GetCASATransactionsSummaryValidResponse()
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferTransactionsDBMockRows(), nil)
		storage.TransactionsDataD = mockTransactionData
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate
		response, err := GetTransactionsSummary(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("happy-path-momo-cash-out", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.GetCASATransactionsSummaryValidResponse()
		mockTransDatas := resources.TransferTransactionsDBMockRows()
		mockTransDatas[0].TransactionType = "CASH_OUT"
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(mockTransDatas, nil)
		storage.TransactionsDataD = mockTransactionData
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate
		response, err := GetTransactionsSummary(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("happy-path-with-different-transactions", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.GetCASATransactionsSummaryValidResponse2()
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PendingTransactionsDBMockRows(), nil)
		storage.TransactionsDataD = mockTransactionData
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate
		response, err := GetTransactionsSummary(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("dbmy-happy-path", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.DBMYGetCASATransactionsSummaryValidResponse()
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferTransactionsDBMockRows(), nil)
		storage.TransactionsDataD = mockTransactionData
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYInterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate
		response, err := GetTransactionsSummary(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("no interest transaction data", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.GetCASATransactionsSummaryNoInterestResponse()
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.TransferTransactionsDBMockRows(), nil).Once()
		storage.TransactionsDataD = mockTransactionData
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.InterestAggregateD = mockInterestAggregate
		response, err := GetTransactionsSummary(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("no transfer transaction data", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.GetCASATransactionsSummaryNoTransferResponse()
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.TransactionsDataD = mockTransactionData
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate
		response, err := GetTransactionsSummary(context.Background(), req)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("error in fetching transfer transaction data", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error in fetching data from db"))
		storage.TransactionsDataD = mockTransactionData
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate
		response, err := GetTransactionsSummary(context.Background(), req)
		expectedErr := customErr.DefaultInternalServerError
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, response)
	})
	t.Run("error in fetching interest transaction data", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.TransactionsMockDBRows(), nil)
		storage.TransactionsDataD = mockTransactionData
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error in fetching interest_aggregate from db"))
		storage.InterestAggregateD = mockInterestAggregate
		response, err := GetTransactionsSummary(context.Background(), req)
		expectedErr := customErr.DefaultInternalServerError
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, response)
	})
}

func TestGetTransactionsSummaryDBMY(t *testing.T) {
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	t.Run("dbmy-happy-path", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.DBMYGetCASATransactionsSummaryValidResponse2()

		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}
		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(
			responses.DBMYListCASAAccountsForCustomerDetailResponse(), nil)

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYTransactionsDataDBRow(), nil)
		storage.TransactionsDataD = mockTransactionData

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYInterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate

		mockPaymentDetail := &storage.MockIPaymentDetailDAO{}
		mockPaymentDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYPaymentDetailDBRow(), nil).Once()
		storage.PaymentDetailD = mockPaymentDetail

		mockCardDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		storage.CardTransactionDetailD = mockCardDetail

		accountResponse := &accountService.GetAccountResponse{
			Account: &accountService.Account{
				CifNumber: "test-cif",
			},
		}
		mockAccountServiceClient.On("GetAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()

		logicImpl := CasaTransactionsSummaryImpl{AccountService: mockAccountServiceClient}
		response, err := logicImpl.GetTransactionsSummary(context.Background(), &CasaTransactionsSummaryRequest{
			GetCASATransactionsSummaryRequest: req,
			CifNumber:                         "test-cif",
		})
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("dbmy-happy-path: with rewards txn", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.DBMYGetCASATransactionsSummaryValidWithRewardsResponse()

		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}

		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(
			responses.DBMYListCASAAccountsForCustomerDetailResponse(), nil)

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYTransactionsDataWithRewardsDBRow(), nil)
		storage.TransactionsDataD = mockTransactionData

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYInterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate

		mockPaymentDetail := &storage.MockIPaymentDetailDAO{}
		mockPaymentDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYPaymentDetailWithRewardsDBRow(), nil)
		storage.PaymentDetailD = mockPaymentDetail

		mockCardDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		storage.CardTransactionDetailD = mockCardDetail

		accountResponse := &accountService.GetAccountResponse{
			Account: &accountService.Account{
				CifNumber: "test-cif",
			},
		}
		mockAccountServiceClient.On("GetAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()

		logicImpl := CasaTransactionsSummaryImpl{AccountService: mockAccountServiceClient}
		response, err := logicImpl.GetTransactionsSummary(context.Background(), &CasaTransactionsSummaryRequest{
			GetCASATransactionsSummaryRequest: req,
			CifNumber:                         "test-cif",
		})
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("dbmy-happy-path: card txn with missing original txn id", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.DBMYGetCASATransactionsSummaryValidWithCardRefundTxnResponse()[0]

		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}

		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(
			responses.DBMYListCASAAccountsForCustomerDetailResponse(), nil)

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYTransactionsDataWithCardTxnDBRow(), nil)
		storage.TransactionsDataD = mockTransactionData

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYInterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate

		mockPaymentDetail := &storage.MockIPaymentDetailDAO{}
		mockPaymentDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYPaymentDetailWithCardTxnDBRow(), nil)
		storage.PaymentDetailD = mockPaymentDetail

		mockCardDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYCardTxnDetailDBRow(), nil)
		storage.CardTransactionDetailD = mockCardDetail

		accountResponse := &accountService.GetAccountResponse{
			Account: &accountService.Account{
				CifNumber: "test-cif",
			},
		}
		mockAccountServiceClient.On("GetAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()

		logicImpl := CasaTransactionsSummaryImpl{AccountService: mockAccountServiceClient}
		response, err := logicImpl.GetTransactionsSummary(context.Background(), &CasaTransactionsSummaryRequest{
			GetCASATransactionsSummaryRequest: req,
			CifNumber:                         "test-cif",
		})
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("dbmy-happy-path: card txn with same batch id but different txn id", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.DBMYGetCASATransactionsSummaryValidWithCardRefundTxnResponse()[1]

		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}

		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(
			responses.DBMYListCASAAccountsForCustomerDetailResponse(), nil)

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYTransactionsDataWithCardReversalTxnDBRow(), nil)
		storage.TransactionsDataD = mockTransactionData

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYInterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate

		mockPaymentDetail := &storage.MockIPaymentDetailDAO{}
		mockPaymentDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYPaymentDetailWithCardTxnDBRow(), nil)
		storage.PaymentDetailD = mockPaymentDetail

		mockCardDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYCardTxnDetailWithReversalTxnDBRow(), nil)
		storage.CardTransactionDetailD = mockCardDetail

		accountResponse := &accountService.GetAccountResponse{
			Account: &accountService.Account{
				CifNumber: "test-cif",
			},
		}
		mockAccountServiceClient.On("GetAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()

		logicImpl := CasaTransactionsSummaryImpl{AccountService: mockAccountServiceClient}
		response, err := logicImpl.GetTransactionsSummary(context.Background(), &CasaTransactionsSummaryRequest{
			GetCASATransactionsSummaryRequest: req,
			CifNumber:                         "test-cif",
		})
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("dbmy-happy-path: with provisional credit txn", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.DBMYGetCASATransactionsSummaryValidWithCardOpsResponse()[0]

		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}

		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(
			responses.DBMYListCASAAccountsForCustomerDetailResponse(), nil)

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYTransactionsDataWithCardOpsDBRow(), nil)
		storage.TransactionsDataD = mockTransactionData

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYInterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate

		mockPaymentDetail := &storage.MockIPaymentDetailDAO{}
		mockPaymentDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		storage.PaymentDetailD = mockPaymentDetail

		mockCardDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		storage.CardTransactionDetailD = mockCardDetail

		accountResponse := &accountService.GetAccountResponse{
			Account: &accountService.Account{
				CifNumber: "test-cif",
			},
		}
		mockAccountServiceClient.On("GetAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()

		logicImpl := CasaTransactionsSummaryImpl{AccountService: mockAccountServiceClient}
		response, err := logicImpl.GetTransactionsSummary(context.Background(), &CasaTransactionsSummaryRequest{
			GetCASATransactionsSummaryRequest: req,
			CifNumber:                         "test-cif",
		})
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("dbmy-happy-path: with lending txn", func(t *testing.T) {
		req := &api.GetCASATransactionsSummaryRequest{
			AccountID: "**********",
		}
		expectedResp := responses.DBMYGetCASATransactionsSummaryValidWithLoanResponse()

		mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
		mockAccountServiceClient := &accountServiceMock.AccountService{}

		mockCustomerMasterClient.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(
			&customerMasterApi.LookupCIFNumberResponse{CifNumber: "test-cif"}, nil)
		mockAccountServiceClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(
			&accountService.CheckPermissionsForAccountResponse{Status: accountService.AccountPermission_ALLOWED}, nil)
		mockAccountServiceClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(
			responses.DBMYListCASAAccountsForCustomerDetailResponse(), nil)

		mockTransactionData := &storage.MockITransactionsDataDAO{}
		mockTransactionData.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYTransactionsDataWithLoanDBRow(), nil)
		storage.TransactionsDataD = mockTransactionData

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYInterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate

		mockPaymentDetail := &storage.MockIPaymentDetailDAO{}
		mockPaymentDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.DBMYPaymentDetailWithRewardsDBRow(), nil)
		storage.PaymentDetailD = mockPaymentDetail

		mockCardDetail := &storage.MockICardTransactionDetailDAO{}
		mockCardDetail.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		storage.CardTransactionDetailD = mockCardDetail

		accountResponse := &accountService.GetAccountResponse{
			Account: &accountService.Account{
				CifNumber: "test-cif",
			},
		}
		mockAccountServiceClient.On("GetAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()

		logicImpl := CasaTransactionsSummaryImpl{AccountService: mockAccountServiceClient}
		response, err := logicImpl.GetTransactionsSummary(context.Background(), &CasaTransactionsSummaryRequest{
			GetCASATransactionsSummaryRequest: req,
			CifNumber:                         "test-cif",
		})
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
}

func Test_getTxnBatchIDs(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		finalDBResponse := []*storage.TransactionsData{
			{
				ID:                  1,
				ClientTransactionID: "txn1",
				ClientBatchID:       "batchID1",
			},
			{
				ID:                  2,
				ClientTransactionID: "txn2",
				ClientBatchID:       "batchID2",
			},
			{
				ID:                  3,
				ClientTransactionID: "txn3",
				ClientBatchID:       "batchID3",
			},
		}
		expectedResp := []string{"batchID1", "batchID2", "batchID3"}
		response := getTxnBatchIDs(finalDBResponse)
		assert.Equal(t, expectedResp, response)
	})
}

func Test_buildOriginalTransactionIDMapper(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		paymentDetail := []*storage.PaymentDetail{
			{
				ID:              1,
				TransactionID:   "batchID1",
				TransactionType: constants.TransferMoneyRevTxType,
				Metadata:        json.RawMessage(`{"original_transaction_id": "testBatch1"}`),
			},
			{
				ID:              2,
				TransactionID:   "batchID2",
				TransactionType: constants.SendMoneyRevTxType,
				Metadata:        json.RawMessage(`{"original_transaction_id": "testBatch2"}`),
			},
			{
				ID:              3,
				TransactionID:   "batchID3",
				TransactionType: constants.ReceiveMoneyTxType,
				Metadata:        json.RawMessage(`{}`),
			},
		}
		cardDetail := []*storage.CardTransactionDetail{}
		expectedResp := map[string]string{
			"batchID1": "testBatch1",
			"batchID2": "testBatch2",
		}
		txnDetailDB := transactionDetailsDB{
			paymentDetails: paymentDetail,
			cardDetails:    cardDetail,
		}
		response := buildOriginalTransactionIDMapper(context.Background(), txnDetailDB)
		assert.Equal(t, expectedResp, response)
	})
}

func Test_getOriginalTxnID(t *testing.T) {
	t.Run("happy-path: get from mapper", func(t *testing.T) {
		originalTxnIDMapper := map[string]string{
			"batchID1": "testBatch1",
			"batchID2": "testBatch2",
		}
		finalDBResponse := &storage.TransactionsData{
			ID:                  1,
			ClientTransactionID: "txn1",
			ClientBatchID:       "batchID1",
		}
		expectedResp := "testBatch1"
		response, err := getOriginalTxnID(context.Background(), originalTxnIDMapper, finalDBResponse)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})

	t.Run("happy-path: get from transactionsData", func(t *testing.T) {
		originalTxnIDMapper := map[string]string{
			"batchID1": "testBatch1",
			"batchID2": "testBatch2",
		}
		finalDBResponse := &storage.TransactionsData{
			ID:                  1,
			ClientTransactionID: "txn1",
			ClientBatchID:       "batchID1",
			TransactionType:     constants.SpendMoneyRevTxType,
			TransactionDetails:  json.RawMessage(`{"original_transaction_id": "testBatch4"}`),
		}
		expectedResp := "testBatch4"
		response, err := getOriginalTxnID(context.Background(), originalTxnIDMapper, finalDBResponse)
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
}

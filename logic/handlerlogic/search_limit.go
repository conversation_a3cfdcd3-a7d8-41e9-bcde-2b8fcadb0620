package handlerlogic

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"

	"gitlab.myteksi.net/dakota/transaction-history/featureflag"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
)

// EmptySearchTransactionLimitFilter ...
var EmptySearchTransactionLimitFilter = SearchTransactionLimit{}

// SearchTransactionLimit ...
type SearchTransactionLimit struct {
	MinStartDate *time.Time
}

// BuildSearchTransactionLimit builds the SearchTransactionLimit
// endDate timezone expects UTC+8 which was already defined in storage.TimeZoneLocation
// transactionsSearchFilterDurationInMonths is how many months customer allowed to view their transactions
func BuildSearchTransactionLimit(endDate time.Time, transactionsSearchFilterDurationInMonths int) SearchTransactionLimit {
	if transactionsSearchFilterDurationInMonths > 0 {
		lastMonth := endDate.AddDate(0, -(transactionsSearchFilterDurationInMonths - 1), 0)
		minStartDate := time.Date(lastMonth.Year(), lastMonth.Month(), 1, 0, 0, 0, 0, lastMonth.Location())
		return SearchTransactionLimit{MinStartDate: &minStartDate}
	}
	return EmptySearchTransactionLimitFilter
}

// ShouldOverrideStartingDate ...
func (transactionLimit SearchTransactionLimit) ShouldOverrideStartingDate(ctx context.Context, startingDate string) bool {
	featureFlags := featureflag.FeatureFlagsFromContext(ctx)
	if featureFlags == nil || !featureFlags.IsTransactionsSearchDurationLimitEnabled() {
		return false
	}
	if transactionLimit.MinStartDate == nil {
		return false
	}
	if lo.IsEmpty(startingDate) {
		return true
	}
	parsedTime, err := time.Parse(CursorDataTimeLayout, startingDate)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing startingDate: %s. Will use the MinStartDate", err.Error()))
		return true
	}
	return parsedTime.Before(*transactionLimit.MinStartDate)
}

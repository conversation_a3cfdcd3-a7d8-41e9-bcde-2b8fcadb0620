package handlerlogic

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// SnowflakeStruct ...
var SnowflakeStruct storage.SnowflakeQueryImpl = &storage.SnowflakeQuery{}

// GetSTM453TransactionDetail fetches the transactions detail from the DB and snowflake and return the formatted GetSTM453TransactionInfoResponse.
func GetSTM453TransactionDetail(ctx context.Context, req *api.GetSTM453TransactionInfoRequest) (*api.GetSTM453TransactionInfoResponse, error) {
	paymentDetail := fetchPaymentDataForSTM453Transaction(ctx, req)
	if paymentDetail == nil {
		return &api.GetSTM453TransactionInfoResponse{}, nil
	}
	fastTransactionID, err := getFastTransactionIDFromMetadata(ctx, paymentDetail.Metadata)
	if err != nil || fastTransactionID == "" {
		return &api.GetSTM453TransactionInfoResponse{}, nil
	}
	rows, err := SnowflakeStruct.GetSTM453TransactionDetail(fastTransactionID)
	if err != nil {
		return &api.GetSTM453TransactionInfoResponse{}, nil
	}
	txDetail, err := getSTM453TransactionRowFromQuery(ctx, rows)
	if err != nil {
		return &api.GetSTM453TransactionInfoResponse{}, nil
	}
	return getSTM453ResponseGenerator(txDetail, fastTransactionID, paymentDetail), nil
}

// GetSTM453TransactionDetailRequestValidator validates the request parameters
func GetSTM453TransactionDetailRequestValidator(req *api.GetSTM453TransactionInfoRequest) *servus.ErrorDetail {
	if req.TransactionID == "" {
		return &servus.ErrorDetail{
			Message: "'transactionID' is a mandatory parameter.",
		}
	}
	return nil
}

// Unmarshall metadata and fetch the FastTransactionID
func getFastTransactionIDFromMetadata(ctx context.Context, metadataJSON json.RawMessage) (string, error) {
	var metadata map[string]string
	if err := json.Unmarshal(metadataJSON, &metadata); err != nil {
		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing Metadata, err: %s", err.Error()))
		return "", err
	}
	if val, ok := metadata["externalID"]; ok {
		return val, nil
	}
	return "", nil
}

// Generate GetSTM453TransactionInfoResponse from STM453TransactionInfo
func getSTM453ResponseGenerator(txData *dto.STM453TransactionInfo, fastTransactionID string, paymentDetail *storage.PaymentDetail) *api.GetSTM453TransactionInfoResponse {
	return &api.GetSTM453TransactionInfoResponse{
		TransactionType:      txData.TransactionType,
		Amount:               &api.Money{Val: int64(txData.TransactionAmount * 100), CurrencyCode: paymentDetail.Currency},
		TransactionSubtype:   txData.TransactionSubtype,
		TransactionTimestamp: txData.TransactionTimestamp,
		FastTransactionID:    fastTransactionID,
		SenderAccountID:      txData.SenderAccountNumber,
		ReceiverAccountID:    txData.RecipientAccountNumber,
		RecipientBank:        txData.RecipientBank,
	}
}

// getSTM453TransactionRowFromQuery fetch the rows from snowflake
func getSTM453TransactionRowFromQuery(ctx context.Context, rows *sql.Rows) (*dto.STM453TransactionInfo, error) {
	defer closeRows(ctx, rows)
	txData := dto.STM453TransactionInfo{}
	for rows.Next() {
		if err := rows.Scan(&txData.TransactionType, &txData.TransactionAmount, &txData.TransactionSubtype, &txData.TransactionTimestamp, &txData.FastTransactionID, &txData.SenderAccountNumber, &txData.RecipientAccountNumber, &txData.RecipientBank); err != nil {
			slog.FromContext(ctx).Warn(constants.SnowflakeTransactionHistoryLogTag, fmt.Sprintf("Unable to scan values from Snowflake rows : %s", err.Error()))
			return nil, err
		}
	}
	return &txData, nil
}

// closeRows ...
func closeRows(ctx context.Context, rows *sql.Rows) {
	err := rows.Close()
	if err != nil {
		slog.FromContext(ctx).Warn(constants.SnowflakeTransactionHistoryLogTag, fmt.Sprintf("Unable to close rows: %s", err.Error()))
	}
}

// fetchPaymentDataForSTM453Transaction fetches the payment details for a given transactionID
func fetchPaymentDataForSTM453Transaction(ctx context.Context, req *api.GetSTM453TransactionInfoRequest) *storage.PaymentDetail {
	filters := []data.Condition{
		data.EqualTo("TransactionID", req.TransactionID),
	}
	response, err := storage.PaymentDetailD.Find(ctx, filters...)
	if err != nil || len(response) == 0 {
		slog.FromContext(ctx).Warn(constants.GetSTM453TransactionInfoLogTag, "No data present in the paymentDetailDB")
		return nil
	}
	return response[0]
}

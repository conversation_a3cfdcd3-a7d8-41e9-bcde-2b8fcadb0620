package handlerlogic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
)

type transactionDetailsDB struct {
	paymentDetails []*storage.PaymentDetail
	cardDetails    []*storage.CardTransactionDetail
	// TODO: there is no reversal transaction type in LENDING domain, should revisit this in future when there is any
	// loanDetails []*storage.LoanDetail
}

// CasaTransactionsSummaryRequest ...
type CasaTransactionsSummaryRequest struct {
	GetCASATransactionsSummaryRequest *api.GetCASATransactionsSummaryRequest
	CifNumber                         string
}

// CasaTransactionsSummaryImpl ...
type CasaTransactionsSummaryImpl struct {
	AccountService accountService.AccountService
}

func emptyTransactionDetailsDB() transactionDetailsDB {
	return transactionDetailsDB{
		paymentDetails: nil,
		cardDetails:    nil,
	}
}

// GetCASATransactionsSummaryRequestValidator validates the request parameters
func GetCASATransactionsSummaryRequestValidator(req *api.GetCASATransactionsSummaryRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.AccountID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		})
	}
	return errors
}

// GetTransactionsSummary returns the summary of the money spent and received in the main account.
func GetTransactionsSummary(ctx context.Context, req *api.GetCASATransactionsSummaryRequest) (*api.GetCASATransactionsSummaryResponse, error) {
	totalInterestEarned, err := getTotalInterestEarned(ctx, req.AccountID, "")
	if err != nil {
		return nil, err
	}
	currMonthMoneySpent, currMonthMoneyReceived, err := getCurrMonthMoneySpentAndReceived(ctx, req)
	if err != nil {
		return nil, err
	}

	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned:       totalInterestEarned,
		CurrentMonthMoneySpent:    currMonthMoneySpent,
		CurrentMonthMoneyReceived: currMonthMoneyReceived,
	}, nil
}

// GetTransactionsSummary ...
func (c *CasaTransactionsSummaryImpl) GetTransactionsSummary(ctx context.Context, req *CasaTransactionsSummaryRequest) (*api.GetCASATransactionsSummaryResponse, error) {
	// Get account list by cif number
	accountList, err := utils.GetAccountList(ctx, c.AccountService, req.CifNumber)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetPocketInterestEarnedHandlerLogTag, fmt.Sprintf("Error getting account list, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	totalInterestEarned, err := getTotalInterestEarnedDBMY(ctx, utils.GetAccountIdsFromList(accountList), "DEFAULT")
	if err != nil {
		return nil, err
	}
	currMonthMoneySpent, currMonthMoneyReceived, err := getCurrMonthMoneySpentAndReceived(ctx, req.GetCASATransactionsSummaryRequest)
	if err != nil {
		return nil, err
	}

	return &api.GetCASATransactionsSummaryResponse{
		TotalInterestEarned:       totalInterestEarned,
		CurrentMonthMoneySpent:    currMonthMoneySpent,
		CurrentMonthMoneyReceived: currMonthMoneyReceived,
	}, nil
}

// getCurrMonthMoneySpentAndReceived filter from the DB, the transactions of the current month and returns the aggregated money spent and received data.
func getCurrMonthMoneySpentAndReceived(ctx context.Context, req *api.GetCASATransactionsSummaryRequest) (*api.CurrentMonthMoneySpent, *api.CurrentMonthMoneyReceived, error) {
	currMonthSpentReceivedFilter := prepareCurrMonthSpentRecFilters(req)
	currMon := time.Now().Format(utils.TimestampMonthFormat)
	dbResponse, err := storage.TransactionsDataD.Find(ctx, currMonthSpentReceivedFilter...)
	if err != nil && !errors.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(constants.GetCASATransactionsSummaryHandlerLogTag, fmt.Sprintf("Error getting transaction history data db, err: %s", err.Error()))
		return nil, nil, customErr.DefaultInternalServerError
	}
	if len(dbResponse) == 0 || errors.Is(err, data.ErrNoData) {
		return emptyCurrentMonthMoneySpentResponse(currMon), emptyCurrentMonthMoneyReceivedResponse(currMon), nil
	}
	var moneySpent, moneyReceived *api.Money
	if len(dbResponse) > 0 {
		switch config.GetTenant() {
		case tenants.TenantMY:
			moneySpent, moneyReceived = getCalculatedExpenseSummaryDBMY(ctx, dbResponse)
		default:
			moneySpent, moneyReceived = getCalculatedExpenseSummary(ctx, dbResponse)
		}
	}

	currMonthMoneySpent := &api.CurrentMonthMoneySpent{
		Month:      currMon,
		MoneySpent: moneySpent,
	}
	currMonthMoneyRec := &api.CurrentMonthMoneyReceived{
		Month:         currMon,
		MoneyReceived: moneyReceived,
	}
	return currMonthMoneySpent, currMonthMoneyRec, nil
}

// getFinalDBResponse filters out only COMPLETED transactions excluding the pocket transfers. i.e when the AccountPhase == POSTING_PHASE_COMMITTED or TmTransactionType == RELEASE
func getFinalDBResponse(dbResponse []*storage.TransactionsData) []*storage.TransactionsData {
	_, clientBatchIDToTransactionsDataMap := createClientBatchIDToTransactionsDataMap(dbResponse)
	var finalDBResponse []*storage.TransactionsData
	for _, transactions := range clientBatchIDToTransactionsDataMap {
		var committedPostingTx *storage.TransactionsData
		for _, item := range transactions {
			// To handle pocket transfers
			if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, item.TransactionSubtype) {
				continue
			}
			if item.AccountPhase == constants.PostingPhaseCommitted {
				committedPostingTx = item
			}
		}
		if committedPostingTx != nil {
			finalDBResponse = append(finalDBResponse, committedPostingTx)
		}
	}
	return finalDBResponse
}

// getCalculatedExpenseSummary aggregates all the money spent and received from and to the main account.
// Total money sent current month: (If reversal in the same month, then deduct from sent; else it will be included in received in next month)
// Total money received current month: (If reversal in the same month, then deduct from received; else it will be included in sent in next month)
func getCalculatedExpenseSummary(ctx context.Context,
	dbResponse []*storage.TransactionsData) (*api.Money, *api.Money) {
	var amountSpent, amountReceived int64
	currencyCode := constants.Currency
	finalDBResponse := getFinalDBResponse(dbResponse)
	transferMap := make(map[string]bool)
	for _, transfer := range finalDBResponse {
		transferMap[transfer.ClientTransactionID] = true
	}
	for _, transaction := range finalDBResponse {
		amount := utils.GetAmountInCents(ctx, transaction.TransactionAmount)
		if utils.SearchStringArray(constants.ReverseTransactionType, transaction.TransactionType) {
			txnDetails := make(map[string]string)
			err := json.Unmarshal(transaction.TransactionDetails, &txnDetails)
			if err != nil {
				slog.FromContext(ctx).Warn(constants.GetCASATransactionsSummaryHandlerLogTag, fmt.Sprintf("Error parsing TransactionDetails, err: %s", err.Error()))
				continue
			}
			if transaction.DebitOrCredit == constants.DEBIT && transferMap[txnDetails["original_transaction_id"]] {
				amountReceived -= amount
				continue
			}
			if transaction.DebitOrCredit == constants.CREDIT && transferMap[txnDetails["original_transaction_id"]] {
				amountSpent -= amount
				continue
			}
		}
		if transaction.DebitOrCredit == constants.DEBIT {
			amountSpent += amount
		} else {
			amountReceived += amount
		}
		currencyCode = transaction.TransactionCurrency
	}
	return &api.Money{
			CurrencyCode: currencyCode,
			Val:          amountSpent,
		}, &api.Money{
			CurrencyCode: currencyCode,
			Val:          amountReceived,
		}
}

// getCalculatedExpenseSummaryDBMY aggregates all the money spent and received from and to the main account for DBMY
// Total money sent current month: (If reversal in the same month, then deduct from sent; else it will be included in received in next month)
// Total money received current month: (If reversal in the same month, then deduct from received; else it will be included in sent in next month)
func getCalculatedExpenseSummaryDBMY(ctx context.Context,
	dbResponse []*storage.TransactionsData) (*api.Money, *api.Money) {
	var (
		amountSpent, amountReceived int64
		currencyCode                string
	)

	// DBMY not using refund mechanism for reversal txn, except for SPEND_MONEY_REVERSAL
	// hence will need to query to the transaction detail table (e.g. PaymentDetail, CardTxnDetail etc) to get the originalTxnID
	txnBatchIDs := getTxnBatchIDs(dbResponse)
	txnDetailsDB, err := getTransactionDetailFromDB(ctx, txnBatchIDs)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetCASATransactionsSummaryHandlerLogTag, fmt.Sprintf("Error getting txn details from db, err: %s", err.Error()))
	}
	originalTxnIDMapper := buildOriginalTransactionIDMapper(ctx, txnDetailsDB)

	transferMap := make(map[string]bool)
	for _, transaction := range dbResponse {
		transferMap[transaction.ClientBatchID] = true
	}
	amountSpent, amountReceived, currencyCode = calculateExpenseSummaryDBMY(ctx, dbResponse, originalTxnIDMapper, transferMap)

	return &api.Money{
			CurrencyCode: currencyCode,
			Val:          amountSpent,
		}, &api.Money{
			CurrencyCode: currencyCode,
			Val:          amountReceived,
		}
}

func getTxnBatchIDs(finalDBResponse []*storage.TransactionsData) []string {
	batchIDList := make([]string, 0, len(finalDBResponse))
	for _, txn := range finalDBResponse {
		batchID := txn.ClientBatchID
		batchIDList = append(batchIDList, batchID)
	}
	return batchIDList
}

func getTransactionDetailFromDB(ctx context.Context, finalBatchIDList []string) (transactionDetailsDB, error) {
	// query payment detail table
	filter := []data.Condition{
		data.ContainedIn("TransactionID", utils.ConvertToInterface(finalBatchIDList)...),
	}
	dbResponse, err := storage.PaymentDetailD.Find(ctx, filter...)
	if err != nil && !errors.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(constants.GetCASATransactionsSummaryHandlerLogTag, fmt.Sprintf("Error getting payment-detail db, err: %s", err.Error()))
		return emptyTransactionDetailsDB(), customErr.DefaultInternalServerError
	}
	cardFilter := []data.Condition{
		data.ContainedIn("CardTransactionID", utils.ConvertToInterface(finalBatchIDList)...),
	}
	cardDetailResponse, err := storage.CardTransactionDetailD.Find(ctx, cardFilter...)
	if err != nil && !errors.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(constants.GetCASATransactionsSummaryHandlerLogTag, fmt.Sprintf("Error getting card transaction db, err: %s", err.Error()))
		return emptyTransactionDetailsDB(), customErr.DefaultInternalServerError
	}
	return transactionDetailsDB{
		paymentDetails: dbResponse,
		cardDetails:    cardDetailResponse,
	}, nil
}

func buildOriginalTransactionIDMapper(ctx context.Context, txnDetailsDB transactionDetailsDB) map[string]string {
	originalTxnIDMapper := make(map[string]string)
	// if paymentDetail and cardDetail is nil, return empty mapper
	if txnDetailsDB.paymentDetails == nil && txnDetailsDB.cardDetails == nil {
		return originalTxnIDMapper
	}
	for _, pd := range txnDetailsDB.paymentDetails {
		if utils.SearchStringArray(constants.ReverseTransactionType, pd.TransactionType) {
			metadata := pd.GetPaymentMetadata(ctx)
			originalTxnIDMapper[pd.TransactionID] = metadata.OriginalTransactionID
		}
	}
	for _, cd := range txnDetailsDB.cardDetails {
		if utils.SearchStringArray(constants.ReverseTransactionType, cd.TransactionType) {
			metadata := cd.GetMetadata(ctx)
			originalTxnIDMapper[cd.CardTransactionID] = metadata.OriginalChargeID
		}
	}
	return originalTxnIDMapper
}

func getOriginalTxnID(ctx context.Context, originalTxnIDMapper map[string]string, transaction *storage.TransactionsData) (string, error) {
	if transaction.TransactionType == constants.SpendMoneyRevTxType {
		txnDetails := make(map[string]string)
		err := json.Unmarshal(transaction.TransactionDetails, &txnDetails)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetCASATransactionsSummaryHandlerLogTag, fmt.Sprintf("Error parsing TransactionDetails, err: %s", err.Error()))
			return "", err
		}
		return txnDetails["original_transaction_id"], nil
	}
	return originalTxnIDMapper[transaction.ClientBatchID], nil
}

func calculateExpenseSummaryDBMY(ctx context.Context, finalDBResponse []*storage.TransactionsData, originalTxnIDMapper map[string]string, transferMap map[string]bool) (int64, int64, string) {
	var (
		amountSpent, amountReceived int64
		currencyCode                string
	)

	for _, transaction := range finalDBResponse {
		// To handle pocket transfers
		if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
			continue
		}
		amount := utils.GetAmountInCents(ctx, transaction.TransactionAmount)

		if utils.SearchStringArray(constants.ReverseTransactionType, transaction.TransactionType) {
			originalTxnID, _ := getOriginalTxnID(ctx, originalTxnIDMapper, transaction)
			isCurrMonthReversal := transferMap[originalTxnID]
			// if it is current month reversal, we will offset the amount; else will treat it as normal debit/credit txn
			if isCurrMonthReversal {
				amountReceived, amountSpent = handleReversalTxnAmount(transaction, amountReceived, amountSpent, amount)
				continue
			}
		}
		if transaction.DebitOrCredit == constants.DEBIT {
			amountSpent += amount
		} else {
			amountReceived += amount
		}
		currencyCode = transaction.TransactionCurrency
	}
	return amountSpent, amountReceived, currencyCode
}

func handleReversalTxnAmount(transaction *storage.TransactionsData, amountReceived int64, amountSpent int64, amount int64) (int64, int64) {
	if transaction.DebitOrCredit == constants.DEBIT {
		amountReceived -= amount
	}
	if transaction.DebitOrCredit == constants.CREDIT {
		amountSpent -= amount
	}
	return amountReceived, amountSpent
}

func prepareCurrMonthSpentRecFilters(req *api.GetCASATransactionsSummaryRequest) []data.Condition {
	firstDay := utils.GetLocalTimeFirstDayInUTC(time.Now(), constants.OffSetInMinutesForMYTimezone)
	filter := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.GreaterThanOrEqualTo("BatchValueTimestamp", firstDay),
		data.EqualTo("AccountAddress", constants.DefaultAccountAddress),
		data.EqualTo("BatchStatus", constants.BatchStatusAccepted),
		data.EqualTo("AccountPhase", constants.PostingPhaseCommitted),
		data.ContainedIn("TransactionType", utils.ConvertToInterface(constants.SentReceiveTransactionTypes)...),
	}
	return filter
}

// emptyMoneyResponse will generate empty response structure in case no matching txns is found
func emptyMoneyResponse() *api.Money {
	currencyCode := constants.Currency
	response := &api.Money{CurrencyCode: currencyCode, Val: 0}
	return response
}

// emptyMoneyResponse will generate empty response structure in case no matching txns is found
func emptyCurrentMonthMoneySpentResponse(month string) *api.CurrentMonthMoneySpent {
	response := &api.CurrentMonthMoneySpent{
		Month:      month,
		MoneySpent: emptyMoneyResponse(),
	}
	return response
}

// emptyMoneyResponse will generate empty response structure in case no matching txns is found
func emptyCurrentMonthMoneyReceivedResponse(month string) *api.CurrentMonthMoneyReceived {
	response := &api.CurrentMonthMoneyReceived{
		Month:         month,
		MoneyReceived: emptyMoneyResponse(),
	}
	return response
}

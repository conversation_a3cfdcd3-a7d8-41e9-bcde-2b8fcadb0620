package handlerlogic

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/utils"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	"gitlab.myteksi.net/dakota/flow"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

const (
	defaultMaxCursorLimit             = 10
	defaultExecutionSleepDurationInMs = 500
)

// FetchTxnWithExhaustiveCursorPaginationPipe ...
type FetchTxnWithExhaustiveCursorPaginationPipe struct {
	req                  *api.GetOpsSearchRequest
	resp                 *api.GetOpsSearchResponse
	responseSerializer   func(ctx context.Context, txnData []*storage.TransactionsData, txnDetails transactionDetails, req *api.GetOpsSearchRequest, cursor dto.PaginationCursor) *api.GetOpsSearchResponse
	AccountServiceClient accountService.AccountService
	AppConfig            *config.AppConfig
	Store                storage.DatabaseStore
	accounts             map[string]*accountService.GetAccountResponse
	accountsErr          map[string]error
	dbResponse           []*storage.TransactionsData
	db                   *sql.DB
	// finalTxnDataDB collects all transaction data fetched from the database during pagination,
	// ensuring it is available for further processing and used to generate the final response.
	finalTxnDataDB []*storage.TransactionsData
	txnDetailsData transactionDetails
	cursorData     dto.PaginationCursor
	// defaultStartingBefore defines the starting point for pagination, using the startingBefore value from the request.
	defaultStartingBefore      string
	maxCursorLimit             int
	executionSleepDurationInMs int
}

// FetchTxnWithExhaustiveCursorPagination fetches transactions from the DB, filter and returns a formatted GetOpsSearchResponse.
func (g *GetOpsSearchStruct) FetchTxnWithExhaustiveCursorPagination(ctx context.Context, req *api.GetOpsSearchRequest) (*api.GetOpsSearchResponse, error) {
	p := g.newFetchTxnWithExhaustiveCursorPaginationPipe(req)

	steps := flow.Seq(
		p.validateGetOpsSearchRequest(),
		p.fetchData(),
		p.prepareResponse(),
	)

	err := flow.ExecSeq(ctx, steps)
	if err != nil {
		return nil, err
	}
	return p.resp, nil
}

func (g *GetOpsSearchStruct) newFetchTxnWithExhaustiveCursorPaginationPipe(req *api.GetOpsSearchRequest) *FetchTxnWithExhaustiveCursorPaginationPipe {
	txnDetailsData := transactionDetails{
		paymentDetails: make(map[storage.PaymentDataKey]*storage.PaymentDetail),
		cardDetails:    make(map[storage.CardDataKey]*storage.CardTransactionDetail),
		loanDetails:    make(map[storage.LoanDataKey]*storage.LoanDetail),
	}

	pipe := &FetchTxnWithExhaustiveCursorPaginationPipe{
		req:                   req,
		responseSerializer:    g.getOpsSearchResponseGenerator, // Pass the function directly
		AccountServiceClient:  g.AccountServiceClient,
		AppConfig:             g.AppConfig,
		Store:                 g.Store,
		accounts:              g.accounts,
		accountsErr:           g.accountsErr,
		txnDetailsData:        txnDetailsData,
		defaultStartingBefore: req.StartingBefore,
		finalTxnDataDB:        make([]*storage.TransactionsData, 0, req.PageSize+1), // Pre-allocate with PageSize + 1
	}

	// Use the configured value or fallback to the default.
	maxCursorLimit := config.GetMaxCursorLimit()
	if maxCursorLimit != 0 {
		pipe.maxCursorLimit = maxCursorLimit
	} else {
		pipe.maxCursorLimit = defaultMaxCursorLimit
	}

	executionSleepDurationInMs := config.GetExecutionSleepDurationInMs()
	if executionSleepDurationInMs != 0 {
		pipe.executionSleepDurationInMs = executionSleepDurationInMs
	} else {
		pipe.executionSleepDurationInMs = defaultExecutionSleepDurationInMs
	}

	return pipe
}

// validateGetOpsSearchRequest checks if the request's identifiers, attributes, and filters are valid.
func (p *FetchTxnWithExhaustiveCursorPaginationPipe) validateGetOpsSearchRequest() flow.StepFn {
	return func(ctx context.Context) error {
		if err := GetOpsSearchRequestIdentifierValidator(ctx, p.req); err != nil {
			return err
		}
		if err := GetOpsSearchRequestValidator(ctx, p.req); err != nil {
			return err
		}
		return GetOpsSearchRequestFilterCombinationValidator(ctx, p.req)
	}
}

// fetchData retrieves transaction data from the DB with or without exhaustive pagination.
func (p *FetchTxnWithExhaustiveCursorPaginationPipe) fetchData() flow.StepFn {
	return func(ctx context.Context) error {
		var err error
		if p.req.PageSize == MinPageSize {
			p.req.PageSize = DefaultPageSize
		}
		p.cursorData, err = parsePaginationCursorData(p.req.StartingBefore, p.req.EndingAfter)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("Error parsing pagination cursor, err: %s", err.Error()))
			return customErr.DefaultInternalServerError
		}

		if p.req.ExternalID != "" {
			err = p.fetchByExternalID(ctx)
		} else {
			err = p.fetchWithExhaustivePagination(ctx)
		}

		return err
	}
}

// prepareResponse formats the final response after data is retrieved and processed.
// func (p *FetchTxnWithExhaustiveCursorPaginationPipe) prepareResponse() flow.StepFn {
//	return func(ctx context.Context) error {
//		p.resp = p.getOpsSearchStruct.getOpsSearchResponseGenerator(ctx, p.finalTxnDataDB, p.txnDetailsData, p.req, p.cursorData)
//		return nil
//	}
// }

// prepareResponse formats the final response after data is retrieved and processed.
func (p *FetchTxnWithExhaustiveCursorPaginationPipe) prepareResponse() flow.StepFn {
	return func(ctx context.Context) error {
		if len(p.finalTxnDataDB) == 0 {
			p.resp = emptyGetOpsSearchResponse()
			return nil
		}
		p.resp = p.responseSerializer(ctx, p.finalTxnDataDB, p.txnDetailsData, p.req, p.cursorData)
		return nil
	}
}

// fetchByExternalID retrieves transaction data by ExternalID if provided.
func (p *FetchTxnWithExhaustiveCursorPaginationPipe) fetchByExternalID(ctx context.Context) error {
	var err error
	p.db, err = p.Store.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetOpsSearchLogTag, fmt.Sprintf("Fail to get database handle, err: %s", err.Error()))
		return customErr.DefaultInternalServerError
	}

	p.dbResponse, err = p.Store.GetTxnDataByExternalIDFromDB(ctx, p.req.ExternalID, p.db)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("Error getting ops search, err: %s", err.Error()))
		return customErr.DefaultInternalServerError
	}
	if len(p.dbResponse) == 0 || errors.Is(err, data.ErrNoData) {
		// Data is empty; the response preparation will handle this case.
		return nil
	}

	p.finalTxnDataDB, p.txnDetailsData = generateFinalDBResponse(ctx, p.dbResponse, p.req)
	return nil
}

// fetchWithExhaustivePagination fetches data with cursor pagination logic.
// This function iteratively retrieves paginated data until either:
// - The data retrieved exceeds the requested PageSize, or
// - The maximum cursor pagination limit is reached.
// nolint
func (p *FetchTxnWithExhaustiveCursorPaginationPipe) fetchWithExhaustivePagination(ctx context.Context) error {
	pagesFetched := 0
	// Local variable to manage the cursor for starting the next page fetch.
	// Initialized to the defaultStartingBefore value for the request.
	startingBeforeCursor := p.defaultStartingBefore
	// Local copy of the cursor data to allow modifications during the pagination process.
	cursorData := p.cursorData
	for {
		param := mapGetOpsSearchRequestToOpsSearchParam(p.req, true)
		filters := buildOpsSearchTxnDataFilters(param, cursorData)
		// Fetch paginated data from the database using the current filters.
		err := p.paginatedFetch(ctx, filters)
		if err != nil {
			return err
		}

		slog.FromContext(ctx).Info(constants.GetOpsSearchLogTag, fmt.Sprintf("Size of p.finalTxnDataDB: %d", len(p.finalTxnDataDB)))

		// If the accumulated data exceeds the requested PageSize.
		if len(p.finalTxnDataDB) > int(p.req.PageSize) {
			// Truncate the data to the requested PageSize + 1 for indicating there is more data.
			p.finalTxnDataDB = p.finalTxnDataDB[:p.req.PageSize+1]
			// Assign startingBeforeCursor back to p.defaultStartingBefore to use it when preparing the response.
			startingBeforeCursor = p.defaultStartingBefore
			slog.FromContext(ctx).Debug(constants.GetOpsSearchLogTag, fmt.Sprintf("Condition met: finalTxnDataDB length (%d) exceeds PageSize (%d)", len(p.finalTxnDataDB), int(p.req.PageSize)))
			break
		}

		// If it is last page or the pages fetched more than cursor limit
		maxCursorLimit := p.maxCursorLimit
		if len(p.dbResponse) <= int(p.req.PageSize) || pagesFetched >= maxCursorLimit {
			// Assign startingBeforeCursor back to p.defaultStartingBefore to use it when preparing the response.
			startingBeforeCursor = p.defaultStartingBefore
			slog.FromContext(ctx).Debug(constants.GetOpsSearchLogTag, fmt.Sprintf("Condition met: dbResponse length (%d) <= PageSize (%d) or pagesFetched (%d) >= MaxCursorLimit (%d)", len(p.dbResponse), int(p.req.PageSize), pagesFetched, maxCursorLimit))
			break
		}

		// Update the cursorData with details of the last transaction in the current response.
		lastTxn := p.dbResponse[len(p.dbResponse)-1]
		cursorData = dto.PaginationCursor{
			ID:                 int64(lastTxn.ID),
			TransactionID:      lastTxn.ClientTransactionID,
			Date:               lastTxn.BatchValueTimestamp.Format(CursorDataTimeLayout),
			FirstTransactionID: cursorData.FirstTransactionID,
		}

		// Prepare pagination parameters for the next iteration using the updated cursorData.
		paginationParams := utils.MapPaginationParameters(p.req.AccountID, startingBeforeCursor, p.req.EndingAfter, p.req.StartDate, p.req.EndDate, p.req.PageSize)
		// Determine the next cursor position based on the current response and pagination parameters.
		_, nextCursorID := nextPageLink(p.dbResponse, paginationParams, cursorData)

		// Update the local startingBeforeCursor for the next iteration.
		startingBeforeCursor = nextCursorID
		pagesFetched++
		slog.FromContext(ctx).Info(constants.GetOpsSearchLogTag, fmt.Sprintf("Current pagesFetched: %d", pagesFetched))

		// Short pause between pagination requests to prevent tight looping
		// Pause for the specified duration
		time.Sleep(time.Duration(p.executionSleepDurationInMs) * time.Millisecond)

		// Finally update the request's StartingBefore field once at the end to reflect the final cursor position after pagination.
		// This ensures subsequent operations or response preparation can use the correct state.
		p.cursorData = cursorData
		p.req.StartingBefore = startingBeforeCursor
	}

	p.req.StartingBefore = startingBeforeCursor
	return nil
}

// paginatedFetch executes a paginated fetch, appending fetched data to finalTxnDataDB.
func (p *FetchTxnWithExhaustiveCursorPaginationPipe) paginatedFetch(ctx context.Context, filters []data.Condition) error {
	dbResponse, err := storage.TransactionsDataD.Find(ctx, filters...)
	if err != nil && !errors.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(constants.GetOpsSearchLogTag, fmt.Sprintf("Error getting ops search, err: %s", err.Error()))
		return customErr.DefaultInternalServerError
	}

	p.dbResponse = dbResponse
	currentBatchTxnData, currentTxnDetails := generateFinalDBResponse(ctx, dbResponse, p.req)
	p.finalTxnDataDB = append(p.finalTxnDataDB, currentBatchTxnData...)

	for key, detail := range currentTxnDetails.paymentDetails {
		p.txnDetailsData.paymentDetails[key] = detail
	}

	for key, detail := range currentTxnDetails.cardDetails {
		p.txnDetailsData.cardDetails[key] = detail
	}

	if currentTxnDetails.loanDetails != nil {
		for key, detail := range currentTxnDetails.loanDetails {
			p.txnDetailsData.loanDetails[key] = detail
		}
	}

	return nil
}

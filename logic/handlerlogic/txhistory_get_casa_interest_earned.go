package handlerlogic

import (
	"context"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetCASAInterestEarnedRequestValidator validates the request parameters
func GetCASAInterestEarnedRequestValidator(req *api.GetCASAInterestEarnedRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.AccountID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		})
	}
	return errors
}

// GetCASAInterestEarned gets the total interest earned for the casa account
func GetCASAInterestEarned(ctx context.Context, req *api.GetCASAInterestEarnedRequest) (*api.GetCASAInterestEarnedResponse, error) {
	totalInterestEarned, err := getTotalInterestEarned(ctx, req.AccountID, constants.DefaultAccountAddress)
	if err != nil {
		return nil, err
	}
	return &api.GetCASAInterestEarnedResponse{
		TotalInterestEarned: totalInterestEarned,
	}, nil
}

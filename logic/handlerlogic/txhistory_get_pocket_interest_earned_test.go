package handlerlogic

import (
	"context"
	"errors"
	"testing"

	"gitlab.myteksi.net/dakota/transaction-history/test/utils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/responses"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

func TestGetPocketInterestEarnedRequestValidator(t *testing.T) {
	t.Run("valid-request", func(t *testing.T) {
		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "SAVINGS",
			PocketID:   "*************",
		}
		errorResponse := GetPocketInterestEarnedRequestValidator(req)
		assert.Equal(t, 0, len(errorResponse))
	})
	t.Run("missing-parameter-pocketID", func(t *testing.T) {
		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "SAVINGS",
			PocketID:   "",
		}
		expectResponse := servus.ErrorDetail{
			Message: "'pocketID' is a mandatory parameter.",
		}

		errorResponse := GetPocketInterestEarnedRequestValidator(req)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})
	t.Run("missing-parameter-pocketType", func(t *testing.T) {
		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "",
			PocketID:   "*************",
		}
		expectResponse := servus.ErrorDetail{
			Message: "'pocketType' is a mandatory parameter.",
		}

		errorResponse := GetPocketInterestEarnedRequestValidator(req)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})
	t.Run("invalid-parameter-pocketType", func(t *testing.T) {
		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "DEPOSITS",
			PocketID:   "*************",
		}
		expectResponse := servus.ErrorDetail{
			Message: "'pocketType' is invalid.",
		}

		errorResponse := GetPocketInterestEarnedRequestValidator(req)
		assert.Equal(t, 1, len(errorResponse))
		assert.Equal(t, expectResponse, errorResponse[0])
	})
}

func TestGetPocketValidator(t *testing.T) {
	t.Run("parent account is empty", func(t *testing.T) {
		expectResponse := customErr.BuildErrorResponse(customErr.BadRequest, "The pocket in the request is invalid")
		errorResponse := GetPocketValidator("")
		assert.Equal(t, expectResponse, errorResponse)
	})
	t.Run("parent account is not empty", func(t *testing.T) {
		errorResponse := GetPocketValidator("**********")
		assert.Nil(t, errorResponse)
	})
}

func TestGetPocketInterestEarned(t *testing.T) {
	locale := utils.GetLocale()
	mockAppConfig := &config.AppConfig{
		TransactionHistoryServiceConfig: config.TransactionHistoryServiceConfig{
			DefaultCurrency: locale.Currency,
		},
	}
	constants.InitializeDynamicConstants(mockAppConfig)
	t.Run("happy path", func(t *testing.T) {
		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "SAVINGS",
			PocketID:   "*************",
		}
		expectedResp := responses.GetPocketInterestEarnedValidResponse()

		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.PocketInterestTransactionsDBMockRows(), nil)
		storage.InterestAggregateD = mockInterestAggregate

		response, err := GetPocketInterestEarned(context.Background(), req, "**********")
		assert.Equal(t, expectedResp, response)
		assert.NoError(t, err)
	})
	t.Run("empty-response-request", func(t *testing.T) {
		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "SAVINGS",
			PocketID:   "*************",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		storage.InterestAggregateD = mockInterestAggregate
		expectedResp := responses.GetPocketInterestEarnedEmptyResponse()

		actualResp, err := GetPocketInterestEarned(context.Background(), req, "**********")
		assert.Equal(t, expectedResp, actualResp)
		assert.Nil(t, err)
	})
	t.Run("error-response-request", func(t *testing.T) {
		req := &api.GetPocketInterestEarnedRequest{
			PocketType: "SAVINGS",
			PocketID:   "*************",
		}
		mockInterestAggregate := &storage.MockIInterestAggregateDAO{}
		mockInterestAggregate.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregate{}, errors.New("random error message"))
		storage.InterestAggregateD = mockInterestAggregate
		expectedErr := customErr.DefaultInternalServerError

		actualResp, err := GetPocketInterestEarned(context.Background(), req, "**********")
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, actualResp)
	})
}

package handlerlogic

import (
	"context"
	"fmt"
	"os"
	"testing"

	"gitlab.myteksi.net/dakota/common/tenants"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
)

var interestEarned = "Interest Earned"

func TestGetDescription(t *testing.T) {
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	t.Run("Interest payout test", func(t *testing.T) {
		txnData := resources.InterestPayoutTransactionsMockDBRows()[1]
		expectedDescription := interestEarned
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "Interest Earned")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Tax payout test", func(t *testing.T) {
		txnData := resources.TaxPayoutTransactionsMockDBRows()[1]
		expectedDescription := "Tax on Interest"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "Interest Earned")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Interest payout reversal test", func(t *testing.T) {
		txnData := resources.InterestPayoutReversalTransactionsMockDBRows()[0]
		expectedDescription := interestEarned
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "Interest Earned")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("IntraBank Transfer test", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedDescription := "Bank Transfer to UserNo2"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("IntraBank Transfer for amount greater than 0", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.Amount = 20
		expectedDescription := "Bank Transfer from UserNo2"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Online Transfer test RtolAJ", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionSubType = constants.RtolAJ
		expectedDescription := "Online Transfer to UserNo2"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Online Transfer test RtolAlto", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionSubType = constants.RtolAlto
		expectedDescription := "Online Transfer to UserNo2"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Ops transaction test ", func(t *testing.T) {
		txnData := resources.OPSTransactionsMockDBRows()[0]
		expectedDescription := "Transaction Adjustment"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Pocket Funding test", func(t *testing.T) {
		txnData := resources.PocketTransactions()[1]
		expectedDescription := "Fund Moved to Paris"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "Paris")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Pocket withdrawal test", func(t *testing.T) {
		txnData := resources.PocketTransactions()[3]
		expectedDescription := "Withdrawal from Paris"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "Paris")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Transaction fee test RtolAJ", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		txnData.TransactionSubtype = constants.RtolAJ
		txnData.TransactionType = constants.SendMoneyFeeTransactionType
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedDescription := "Transaction Fee"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Transaction fee test RtolAlto", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		txnData.TransactionSubtype = constants.RtolAlto
		txnData.TransactionType = constants.SendMoneyFeeTransactionType
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedDescription := "Transaction Fee"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
}

func TestDBMYGetDescription(t *testing.T) {
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)
	t.Run("Apply earmark test", func(t *testing.T) {
		txnData := resources.EarmarkTransactionsMockDBRows()[1]
		expectedDescription := "On hold amount"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Release earmark test", func(t *testing.T) {
		txnData := resources.EarmarkTransactionsMockDBRows()[0]
		expectedDescription := "On hold amount"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Interest payout test", func(t *testing.T) {
		txnData := resources.InterestPayoutTransactionsMockDBRows()[1]
		expectedDescription := "Interest earned 🎉"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "Interest Earned")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("IntraBank Transfer test", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedDescription := "Transfer to UserNo2"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("IntraBank Transfer for amount greater than 0", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.Amount = 20
		expectedDescription := "Transfer from UserNo2"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("IntraBank Transfer reversal test", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[1]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionType = constants.TransferMoneyRevTxType
		expectedDescription := "Transfer reversal to GXBank"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), paymentData, "GXBank")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("IntraBank Transfer reversal for amount greater than 0", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		txnData.TransactionType = constants.PocketTransactionTypes[1]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionType = constants.TransferMoneyRevTxType
		paymentData.Amount = 20
		expectedDescription := "Transfer reversal from GXBank"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), paymentData, "GXBank")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Duitnow Transfer test - online banking", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[3]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionType = constants.FundInTxType
		paymentData.TransactionSubType = constants.RPP
		expectedDescription := "Transfer to UserNo2"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Duitnow Transfer Reversal test - online banking", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		txnData.TransactionType = constants.FundInRevTxType
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionType = constants.FundInRevTxType
		paymentData.TransactionSubType = constants.RPP
		expectedDescription := "Reversal to UserNo2"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Duitnow Transfer test - proxy", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[5]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionType = constants.SendMoneyTxType
		paymentData.TransactionSubType = constants.RPP
		expectedDescription := "Transfer to UserNo2"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Duitnow Transfer Reversal test - proxy", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		txnData.TransactionType = constants.SendMoneyRevTxType
		txnData.DebitOrCredit = constants.CREDIT
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionType = constants.SendMoneyRevTxType
		paymentData.TransactionSubType = constants.RPP
		expectedDescription := "Reversal to UserNo2"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Duitnow Transfer Reversal test - proxy for amount greater than 0", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		txnData.TransactionType = constants.ReceiveMoneyRevTxType
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionType = constants.ReceiveMoneyRevTxType
		paymentData.TransactionSubType = constants.RPP
		paymentData.Amount = 20
		expectedDescription := "Reversal from UserNo2"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Ops transaction test - debit", func(t *testing.T) {
		txnData := resources.OPSTransactionsMockDBRows()[0]
		expectedDescription := "Bank adjustments applied"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "Bank adjustments")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Ops transaction test - credit", func(t *testing.T) {
		txnData := resources.OPSTransactionsMockDBRows()[1]
		expectedDescription := "Bank adjustments applied"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "Bank adjustments")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Pocket Funding test-FROM_MAIN_TO_POCKET-credit", func(t *testing.T) {
		txnData := resources.PocketTransactions()[0]
		expectedDescription := "Money added from Main Account"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "Main Account")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Pocket Funding test-FROM_MAIN_TO_POCKET-debit", func(t *testing.T) {
		txnData := resources.PocketTransactions()[1]
		expectedDescription := "Transfer to Paris Pocket"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "Paris")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Pocket withdrawal test-FROM_POCKET_TO_MAIN-debit", func(t *testing.T) {
		txnData := resources.PocketTransactions()[2]
		expectedDescription := "Money withdrawn to Main Account"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "Main Account")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Pocket withdrawal test-FROM_POCKET_TO_MAIN-credit", func(t *testing.T) {
		txnData := resources.PocketTransactions()[3]
		expectedDescription := "Withdrawal from Paris Pocket"
		var key = getTxnScenarioKey(txnData)
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "Paris")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Rewards transaction", func(t *testing.T) {
		txnData := resources.RewardsTransactionsMockDBRows()[0]
		expectedDescription := "Grab Unlimited Reward"
		var key = txnData.GetTxnScenarioKey()
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "Grab Unlimited Reward")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Card rewards transaction", func(t *testing.T) {
		txnData := resources.RewardsTransactionsMockDBRows()[1]
		expectedDescription := "Cashback Earned"
		var key = txnData.GetTxnScenarioKey()
		actualDescription, err := presenterhelper.TransactionResponseHelperFuncs[key].DescriptionFunc(context.Background(), txnData, "Cashback Earned")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
}

func TestGetDescriptionFailedTxns(t *testing.T) {
	t.Run("contract_violation_transaction", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.FailedStatusPaymentTransactionsDBRows()[0]
		expectedDescription := "Bank Transfer to UserNo2"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
}

func TestGetDescriptionErrorPath(t *testing.T) {
	t.Run("Invalid Internal transaction type", func(t *testing.T) {
		txnData := resources.ReleaseTransactions()[0]
		expectedDescription := ""
		expectedErr := fmt.Errorf("Invalid Transaction type: %s", txnData.TransactionType)
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "DummyUser")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Equal(t, expectedErr, err)
	})
}

func TestGetDescriptionInBahasa(t *testing.T) {
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "id"},
	}
	os.Setenv("LOCALISATION_PATH", "../../localise")
	localise.Init(mockAppConfig)
	t.Run("Interest payout test", func(t *testing.T) {
		txnData := resources.InterestPayoutTransactionsMockDBRows()[1]
		expectedDescription := "Bunga Didapat"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "Interest Earned")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Tax payout test", func(t *testing.T) {
		txnData := resources.TaxPayoutTransactionsMockDBRows()[1]
		expectedDescription := "Pajak atas Bunga"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "Interest Earned")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Interest payout reversal test", func(t *testing.T) {
		txnData := resources.InterestPayoutReversalTransactionsMockDBRows()[0]
		expectedDescription := "Bunga Didapat"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "Interest Earned")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("IntraBank Transfer test", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedDescription := "Transfer Bank ke UserNo2"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("IntraBank Transfer amount greater than 0 test", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.Amount = 20
		expectedDescription := "Transfer Bank dari UserNo2"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Online Transfer test RtolAJ", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionSubType = constants.RtolAJ
		expectedDescription := "Transfer Online ke UserNo2"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Online Transfer test RtolAlto", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.TransactionSubType = constants.RtolAlto
		expectedDescription := "Transfer Online ke UserNo2"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Ops transaction test ", func(t *testing.T) {
		txnData := resources.OPSTransactionsMockDBRows()[0]
		expectedDescription := "Penyesuaian Transaksi"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Pocket Funding test", func(t *testing.T) {
		txnData := resources.PocketTransactions()[1]
		expectedDescription := "Pemindahan Dana ke Paris"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "Paris")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Pocket withdrawal test", func(t *testing.T) {
		txnData := resources.PocketTransactions()[3]
		expectedDescription := "Penarikan dari Paris"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, nil, "Paris")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("IntraBank Transfer failed test", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		paymentData := resources.PaymentDataMockDBRows()[0]
		paymentData.Status = constants.FailedStatus
		expectedDescription := "Transfer Bank ke UserNo2"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Transaction fee test RtolAJ", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		txnData.TransactionSubtype = constants.RtolAJ
		txnData.TransactionType = constants.SendMoneyFeeTransactionType
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedDescription := "Biaya Transaksi"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
	t.Run("Transaction fee test RtolAlto", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[0]
		txnData.TransactionSubtype = constants.RtolAlto
		txnData.TransactionType = constants.SendMoneyFeeTransactionType
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedDescription := "Biaya Transaksi"
		actualDescription, err := generateFormattedDescription(context.Background(), txnData, paymentData, "UserNo2")
		assert.Equal(t, expectedDescription, actualDescription)
		assert.Nil(t, err)
	})
}

func TestGetCounterPartyTransactionDetails(t *testing.T) {
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	t.Run("Fund In", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[3]
		paymentData := resources.PaymentRppDataMockDBRows()[0]
		expectedTxnDetails := map[string]string{
			"txn_scenario": "FUND_IN",
			"bank_name":    "Affin Bank Berhad",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("Receive Money", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[4]
		paymentData := resources.PaymentRppDataMockDBRows()[1]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "RECEIVE_MONEY",
			"recipient_reference": "Dinner",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("Send Money-RPP-Account number", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[5]
		paymentData := resources.PaymentRppDataMockDBRows()[2]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "SEND_MONEY",
			"recipient_reference": "Dinner",
			"payment_description": "extra details",
			"type":                "Account number",
			"service_type":        "",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("Send Money-RPP-Mobile number", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[5]
		paymentData := resources.PaymentRppDataMockDBRows()[3]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "SEND_MONEY",
			"recipient_reference": "Dinner",
			"payment_description": "extra details",
			"type":                "Mobile number",
			"service_type":        "",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("Send Money-RPP-MyKad", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[5]
		paymentData := resources.PaymentRppDataMockDBRows()[4]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "SEND_MONEY",
			"recipient_reference": "Dinner",
			"payment_description": "extra details",
			"type":                "MyKad",
			"service_type":        "",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("Send Money-RPP-Army Police ID", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[5]
		paymentData := resources.PaymentRppDataMockDBRows()[5]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "SEND_MONEY",
			"recipient_reference": "Dinner",
			"payment_description": "extra details",
			"type":                "Army/Police ID",
			"service_type":        "",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("Send Money-RPP-Passport", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[5]
		paymentData := resources.PaymentRppDataMockDBRows()[6]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "SEND_MONEY",
			"recipient_reference": "Dinner",
			"payment_description": "extra details",
			"type":                "Passport",
			"service_type":        "",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("Send Money-RPP-Business Registration", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[5]
		paymentData := resources.PaymentRppDataMockDBRows()[7]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "SEND_MONEY",
			"recipient_reference": "Dinner",
			"payment_description": "extra details",
			"type":                "Business Registration",
			"service_type":        "",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("Transfer Money-Intrabank-Receiver", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[6]
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "TRANSFER_MONEY",
			"recipient_reference": "Thanks for the dinner",
			"service_type":        "",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("Transfer Money-Intrabank-Sender", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[7]
		paymentData := resources.PaymentDataMockDBRows()[0]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "TRANSFER_MONEY",
			"recipient_reference": "Thanks for the dinner",
			"service_type":        "",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("Adjustment-Bank Initiated", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[8]
		paymentData := resources.PaymentRppDataMockDBRows()[0]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "BANK_INITIATED",
			"recipient_reference": "TEST REMARKS",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("InterestPayout-Savings", func(t *testing.T) {
		txnData := resources.InterestPayoutTransactionsMockDBRows()[1]
		paymentData := resources.PaymentRppDataMockDBRows()[0]
		expectedTxnDetails := map[string]string{
			"txn_scenario": "INTEREST_PAYOUT",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("TransferMoney-FromMainToPocket", func(t *testing.T) {
		txnData := resources.PocketTransactions()[1]
		paymentData := resources.PaymentRppDataMockDBRows()[0]
		expectedTxnDetails := map[string]string{
			"txn_scenario": "FROM_MAIN_TO_POCKET",
			"service_type": "",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("TransferMoney-FromPocketToMain", func(t *testing.T) {
		txnData := resources.PocketTransactions()[3]
		paymentData := resources.PaymentRppDataMockDBRows()[0]
		expectedTxnDetails := map[string]string{
			"txn_scenario": "FROM_POCKET_TO_MAIN",
			"service_type": "",
		}
		var key = getTxnScenarioKey(txnData)
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
	t.Run("DisputeBankRefund-Bank Initiated", func(t *testing.T) {
		txnData := resources.TransactionsMockDBRows()[13]
		paymentData := resources.PaymentRppDataMockDBRows()[0]
		expectedTxnDetails := map[string]string{
			"txn_scenario":        "BANK_INITIATED",
			"recipient_reference": "TEST REMARKS",
		}
		var key = txnData.GetTxnScenarioKey()
		actualTxnDetails := presenterhelper.TransactionResponseHelperFuncs[key].DetailsFunc(context.Background(), txnData, paymentData)
		assert.Equal(t, expectedTxnDetails, actualTxnDetails)
	})
}

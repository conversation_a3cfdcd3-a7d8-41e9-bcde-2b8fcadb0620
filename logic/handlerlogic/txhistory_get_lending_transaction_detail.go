package handlerlogic

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/server/config"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	pairingService "gitlab.myteksi.net/dakota/payment/pairing-service/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/logic/helper"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetLendingTransactionDetailStruct ...
type GetLendingTransactionDetailStruct struct {
	AccountServiceClient            accountService.AccountService
	PairingServiceClient            pairingService.PairingService
	TransactionHistoryServiceConfig config.TransactionHistoryServiceConfig
	FeatureFlags                    config.FeatureFlags
}

// GetLendingTransactionDetailRequestValidator validates the request parameters
func GetLendingTransactionDetailRequestValidator(req *api.GetLendingTransactionDetailRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.AccountID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		})
	}
	if req.TransactionID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'transactionID' is a mandatory parameter.",
		})
	}
	if req.ProductVariantCode != "" {
		allowedProductVariants := []string{constants.FlexiLOCProductVariantCode, constants.FlexiBizLOCProductVariantCode}
		err := validateProductVariant(req.ProductVariantCode, allowedProductVariants...)
		if err != nil {
			errors = append(errors, *err)
		}
	}
	return errors
}

// GetLendingTransactionDetail fetches the transactions from the DB and returns the formatted GetTransactionDetailResponse.
// nolint: dupl
func (g *GetLendingTransactionDetailStruct) GetLendingTransactionDetail(ctx context.Context, req *api.GetLendingTransactionDetailRequest) (*api.GetLendingTransactionDetailResponse, error) {
	var loanDetailData *storage.LoanDetail
	startTimeForFiltering := time.Now()
	transactions, err := helper.FetchTransaction(ctx, req, g.TransactionHistoryServiceConfig, g.FeatureFlags)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Error getting normal transaction in detail, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Successfully fetched transactions data: %s", utils.ToJSON(transactions)))
	if len(transactions) == 0 || err == data.ErrNoData {
		var recoveryTransaction *storage.TransactionsData
		recoveryTransaction, loanDetailData, err = fetchRecoveryTransaction(ctx, req)
		if err != nil && err != data.ErrNoData {
			slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Error getting recovery transactions in detail, err: %s", err.Error()))
			return nil, customErr.DefaultInternalServerError
		}
		if recoveryTransaction != nil {
			transactions = append(transactions, recoveryTransaction)
		}
	}
	if len(transactions) == 0 {
		slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("No transaction found for the account: %s, with transaction id: %s", req.AccountID, req.TransactionID))
		return nil, customErr.BuildErrorResponse(customErr.ResourceNotFound, "No transaction data found")
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Fetched data from DB for tje account :%s, with transaction id: %s", req.AccountID, req.TransactionID))
	finalTransactionData := helper.FilterInvalidTransactions(transactions)
	if finalTransactionData == nil {
		slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("No transaction found for account: %s, with transaction id: %s", req.AccountID, req.TransactionID))
		return nil, customErr.BuildErrorResponse(customErr.ResourceNotFound, "No transaction data found")
	}
	slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Succesfully fetched data from DB : time :%s", time.Since(startTimeForFiltering)))
	response, err := g.getLendingTransactionDetailResponse(ctx, finalTransactionData, req, loanDetailData)
	slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("time taken filter the DB response  :%s", time.Since(startTimeForFiltering)))
	if err != nil {
		return nil, err
	}
	return response, nil
}

func fetchRecoveryTransaction(ctx context.Context, req *api.GetLendingTransactionDetailRequest) (*storage.TransactionsData, *storage.LoanDetail, error) {
	loanDetails, err := helper.FetchLoanDetailForTransactionID(ctx, req)
	if err != nil {
		return nil, nil, err
	}
	var recoveryTransaction *storage.TransactionsData
	var loanDetailData *storage.LoanDetail
	if len(loanDetails) != 0 {
		loanDetailData = loanDetails[0]
		_, repaymentDetails, err := getDrawdownAndRepaymentDetailsDTO(loanDetails[0])
		if err != nil {
			return nil, nil, err
		}
		recoveryTransaction, err = helper.CreateTransactionFromLoanDetailForWriteOff(loanDetails[0], req.AccountID, repaymentDetails)
		if err != nil {
			return nil, nil, err
		}
	}
	return recoveryTransaction, loanDetailData, nil
}

// Gather the response elements and return the final response
// nolint: dupl
func (g *GetLendingTransactionDetailStruct) getLendingTransactionDetailResponse(ctx context.Context, txnData *storage.TransactionsData, req *api.GetLendingTransactionDetailRequest, loanDetailData *storage.LoanDetail) (*api.GetLendingTransactionDetailResponse, error) {
	loanTxnID, err := helper.GetLoanTxnIDFromTransaction(txnData)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Error while extracting loanTransactionID from transaction, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	if loanDetailData == nil {
		loanDetail, loanErr := helper.FetchLoanDetailForLoanTransactionID(ctx, req, loanTxnID)
		if loanErr != nil {
			return nil, loanErr
		}
		loanDetailData = loanDetail
	}
	paymentTransactionID := helper.GetPaymentTransactionID(txnData, loanDetailData)
	paymentData, err := helper.FetchPaymentDetailForTransaction(ctx, req, paymentTransactionID)
	if err != nil && err != data.ErrNoData {
		return nil, customErr.DefaultInternalServerError
	}
	// If payment data is empty, setting paymentTransactionID to empty as we don't want to show wrong clientBatchID
	if paymentData == nil {
		paymentTransactionID = ""
	}

	drawDownDetails, repaymentDetails, err := getDrawdownAndRepaymentDetails(loanDetailData, paymentTransactionID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Error parsing loan detail for drawdown and repayment, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Error parsing loan detail status, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}

	counterPartyDetails := g.getCounterPartyDetails(ctx, paymentData, txnData, loanDetailData)

	transactionDescription := generateDescription(drawDownDetails, repaymentDetails, loanDetailData.TransactionType)
	status := getLendingTransactionStatus(loanDetailData)
	amount := getAmountWithSign(txnData, loanDetailData)
	transactionDetail := &api.GetLendingTransactionDetailResponse{
		Amount:                 amount,
		TransactionDescription: transactionDescription,
		Status:                 status,
		TransactionTimestamp:   txnData.BatchValueTimestamp,
		CounterParty:           counterPartyDetails,
		DrawdownDetails:        drawDownDetails,
		RepaymentDetails:       repaymentDetails,
	}
	return transactionDetail, nil
}

func getDrawdownAndRepaymentDetails(loanDetailData *storage.LoanDetail, paymentTransactionID string) (*api.DrawDownDetails, *api.RepaymentDetails, error) {
	drawDownDetailsDTO, repaymentDetailsDTO, err := getDrawdownAndRepaymentDetailsDTO(loanDetailData)
	if err != nil {
		return nil, nil, err
	}
	var drawDownDetails *api.DrawDownDetails
	if loanDetailData.TransactionType == constants.DrawdownTransactionType {
		drawDownDetails = fetchDrawDownDetails(&drawDownDetailsDTO, loanDetailData.LoanTransactionID, paymentTransactionID)
	}
	var repaymentDetails *api.RepaymentDetails
	if loanDetailData.TransactionType == constants.RepaymentTransactionType {
		repaymentDetails = fetchRepaymentDetails(&repaymentDetailsDTO, loanDetailData.LoanTransactionID, loanDetailData.PaymentTransactionID)
	}
	return drawDownDetails, repaymentDetails, nil
}

func (g *GetLendingTransactionDetailStruct) getCounterPartyDetails(ctx context.Context, paymentData *storage.PaymentDetail, txnData *storage.TransactionsData, loanDetail *storage.LoanDetail) *api.LendingCounterParty {
	iconURL := getIconURLByTransactionType(txnData)
	if paymentData == nil {
		return &api.LendingCounterParty{
			IconURL: iconURL,
		}
	}
	// fetch account details to get the pairing ID and fetch the proxy details from pairing service
	accountDetails := getAccountDetails(ctx, paymentData)
	var displayName, counterPartyAccountID string
	if loanDetail.TransactionSubType == constants.IntraBank {
		displayName = constants.CASAMainAccount
		counterPartyAccountID = getCounterPartyAccountIDForLending(ctx, txnData, paymentData)
	} else {
		displayName = accountDetails.DisplayName
	}
	counterPartyDetails := &api.LendingCounterParty{
		DisplayName: displayName,
		IconURL:     iconURL,
		AccountID:   counterPartyAccountID,
	}
	isPaymentTransfer := checkIfRppAccount(&accountDetails)
	if isPaymentTransfer {
		proxyDetails := g.getProxyDetails(ctx, accountDetails)
		counterPartyDetails.ProxyDetail = &api.ProxyDetail{
			Channel: proxyDetails.Channel,
			Type:    proxyDetails.Type,
			Value:   proxyDetails.Value,
		}
	}

	return counterPartyDetails
}

func getAccountDetails(ctx context.Context, paymentData *storage.PaymentDetail) dto.AccountDetail {
	var accountDetails dto.AccountDetail
	accountInfo := paymentData.CounterPartyAccount
	err := json.Unmarshal(accountInfo, &accountDetails)
	if err != nil {
		slog.FromContext(ctx).Info(constants.GetLendingTransactionDetailLogTag, fmt.Sprintf("Error parsing CounterParty Account, err: %s", err.Error()))
	}
	return accountDetails
}

func generateDescription(drawDownDetails *api.DrawDownDetails, repaymentDetails *api.RepaymentDetails, transactionType string) string {
	if transactionType == constants.DrawdownTransactionType {
		return fmt.Sprintf("Loan for %s", drawDownDetails.LoanName)
	}
	var c int
	isOverdue := repaymentDetails.PaymentForOverdue != nil
	isInstalment := repaymentDetails.PaymentForInstalments != nil
	if isInstalment {
		c += len(repaymentDetails.PaymentForInstalments.RepaymentSummary)
	}
	if isOverdue {
		c += len(repaymentDetails.PaymentForOverdue.RepaymentSummary)
	}

	var desc string
	if c == 0 || c > 1 {
		desc = "your loans"
	}
	if c == 1 && isOverdue {
		desc = repaymentDetails.PaymentForOverdue.RepaymentSummary[0].LoanName
	}
	if c == 1 && isInstalment {
		desc = repaymentDetails.PaymentForInstalments.RepaymentSummary[0].LoanName
	}
	return fmt.Sprintf("Payment for %s", desc)
}

func fetchRepaymentDetails(repaymentDetails *dto.RepaymentDetailsDTO, loanTxnID string, transferID string) *api.RepaymentDetails {
	totalInterestSaved := helper.ConvertToMoneyObj(repaymentDetails.TotalInterestSaved)
	totalOverdueInterestPaid := helper.ConvertToMoneyObj(repaymentDetails.PenalInterestCharged)

	// calculate payment for instalments if there is total interest saved
	installmentRepayment := getRepaymentSummaryForIndividualLoans(repaymentDetails.LoanRepaymentDetail, false)
	var paymentForInstalments *api.PaymentForInstalments = nil
	if len(installmentRepayment) > 0 {
		paymentForInstalments = &api.PaymentForInstalments{
			TotalInterestSaved: totalInterestSaved,
			RepaymentSummary:   installmentRepayment,
		}
	}

	// calculate payment overdue if there is penal interest charged
	overdueSummary := getRepaymentSummaryForIndividualLoans(repaymentDetails.LoanRepaymentDetail, true)
	var paymentForOverdue *api.PaymentForOverdue = nil
	if repaymentDetails.IsTotalPenalInterestCharged {
		paymentForOverdue = &api.PaymentForOverdue{
			TotalOverdueInterestPaid: totalOverdueInterestPaid,
			RepaymentSummary:         overdueSummary,
		}
	}

	// computing sum of loan amount to check if it is overpaid or not
	sumOfLoanAmount := computeSumOfLoanAmount(repaymentDetails.LoanRepaymentDetail)
	overPaidAmount := sumOfLoanAmount - helper.ConvertAmountToCents(repaymentDetails.TotalRepaymentAmount)
	var lastRepaymentOverpaid *api.LastRepaymentOverpaid
	if overPaidAmount > 0 {
		lastRepaymentOverpaid = generateFormattedOverpaidValues(overPaidAmount)
	} else {
		lastRepaymentOverpaid = nil
	}

	return &api.RepaymentDetails{
		RepaymentID:           loanTxnID,
		TransferID:            transferID,
		PaymentForInstalments: paymentForInstalments,
		PaymentForOverdue:     paymentForOverdue,
		LastRepaymentOverpaid: lastRepaymentOverpaid,
	}
}

// getRepaymentSummaryForIndividualLoans returns the loan details for each individual loans
func getRepaymentSummaryForIndividualLoans(individualLoanDetail []dto.LoanRepaymentDetailDTO, isOverdue bool) []api.RepaymentSummary {
	var repaymentSummary []api.RepaymentSummary
	for _, repayment := range individualLoanDetail {
		var savedAmount, overdueAmount *api.Money
		if repayment.IsPenalInterestCharged {
			overdueAmount = helper.ConvertToMoneyObj(repayment.PenalInterestCharged)
		} else {
			savedAmount = helper.ConvertToMoneyObj(repayment.NormalInterestSave)
		}
		individualLoanRepaymentDetail := api.RepaymentSummary{
			LoanName:                      repayment.LoanName,
			PaidAmount:                    helper.ConvertToMoneyObj(repayment.RepaymentAmount),
			RemainingPayableBeforePayment: helper.ConvertToMoneyObj(repayment.TotalDueBeforeRepayment),
			RemainingPayable:              helper.ConvertToMoneyObj(repayment.TotalDueAfterRepayment),
			InterestSavedAmount:           savedAmount,
			OverdueInterestPaidAmount:     overdueAmount,
		}
		if isOverdue && repayment.IsPenalInterestCharged {
			repaymentSummary = append(repaymentSummary, individualLoanRepaymentDetail)
		}

		if !isOverdue && !repayment.IsPenalInterestCharged {
			repaymentSummary = append(repaymentSummary, individualLoanRepaymentDetail)
		}
	}
	return repaymentSummary
}

func generateFormattedOverpaidValues(amount int64) *api.LastRepaymentOverpaid {
	return &api.LastRepaymentOverpaid{
		Title:       constants.OverPaidTitle,
		Description: fmt.Sprintf(constants.OverPaidDescription, amount),
	}
}

func computeSumOfLoanAmount(detail []dto.LoanRepaymentDetailDTO) int64 {
	var totalAmount int64
	for _, loanDetail := range detail {
		amountPaid := helper.ConvertAmountToCents(loanDetail.RepaymentAmount)
		totalAmount += amountPaid
	}
	return totalAmount
}

func fetchDrawDownDetails(drawDownDetails *dto.DrawdownDetailsDTO, loanTxnID string, transferID string) *api.DrawDownDetails {
	timePeriodInMonths := drawDownDetails.LoanTenorInMonths
	return &api.DrawDownDetails{
		WithdrawalID: loanTxnID,
		TransferID:   transferID,
		LoanName:     drawDownDetails.LoanName,
		RepaymentPeriod: &api.RepaymentPeriod{
			TimePeriodInMonths: utils.MustConvertToInt64(timePeriodInMonths),
			StartDate:          drawDownDetails.FirstInstallmentDueDate,
			EndDate:            drawDownDetails.LastInstallmentDueDate,
			InstallmentCount:   generateRepaymentInstallmentCount(drawDownDetails.RepaymentInstallment, utils.MustConvertToInt64(timePeriodInMonths)),
		},
		MonthlyRepayment: generateMonthlyRepayment(drawDownDetails.RepaymentInstallment),
		TotalPayment: &api.TotalPayment{
			TotalAmount:           helper.ConvertToMoneyObj(drawDownDetails.TotalAmountDue),
			InterestAmount:        helper.ConvertToMoneyObj(drawDownDetails.TotalNormalInterestDue),
			InterestRate:          helper.ConvertValueToFloat(drawDownDetails.AnnualPercentageRate),
			EffectiveInterestRate: helper.ConvertValueToFloat(drawDownDetails.EffectiveInterestRate),
			DisbursedAmount:       helper.ConvertToMoneyObj(drawDownDetails.DisbursementAmount),
			ProcessingFeeAmount:   helper.ConvertToMoneyObj(drawDownDetails.ProcessingFeeAmount),
			ProcessingFeeRate:     helper.ConvertValueToFloat(drawDownDetails.ProcessingFeeRate),
		},
		ProductVariantCode: drawDownDetails.ProductVariantCode,
	}
}

func (g *GetLendingTransactionDetailStruct) getProxyDetails(ctx context.Context, accountDetails dto.AccountDetail) *pairingService.Proxy {
	pairingID := accountDetails.PairingID
	var proxyDetails = &pairingService.Proxy{}
	// look up proxy details from the pairing service
	slog.FromContext(ctx).Info(constants.GetProxyDetailLogTag, fmt.Sprintf("Calling pairing service for proxy details, pairingID: %s", pairingID))
	lookUpInfoReq := pairingService.LookUpInfoRequest{
		PairingID: pairingID,
	}
	proxyLookUpInfo, err := g.PairingServiceClient.LookUpInfo(ctx, &lookUpInfoReq)
	if err != nil {
		slog.FromContext(ctx).Info(constants.GetProxyDetailLogTag, fmt.Sprintf("Error in retrieving proxy details from pairing service, err: %s", err.Error()))
		return proxyDetails
	}
	slog.FromContext(ctx).Info(constants.GetProxyDetailLogTag, fmt.Sprintf("Data retrieved from pairing service, proxy details: %s", utils.ToJSON(proxyLookUpInfo)))
	if proxyLookUpInfo.Data.Proxy != nil {
		return proxyLookUpInfo.Data.Proxy
	}
	return proxyDetails
}

func generateMonthlyRepaymentDescriptions(installment map[int64]*api.Money) []string {
	var descriptions []string
	for key, singleInstallment := range installment {
		value := float64(singleInstallment.Val) / float64(100)
		var periodDescription string
		if key == 1 {
			periodDescription = "final payment"
			description := fmt.Sprintf("%v %s of RM%v", key, periodDescription, value)
			descriptions = append(descriptions, description)
		} else {
			periodDescription = "monthly payments"
			description := fmt.Sprintf("%v %s of RM%v", key, periodDescription, value)
			descriptions = append([]string{description}, descriptions...)
		}
	}
	return descriptions
}

func generateMonthlyRepayment(installment map[int64]*api.Money) *api.MonthlyRepayment {
	var odi, equatedEMI, lastEMI *api.Money
	for key, singleInstallment := range installment {
		if key == 1 {
			lastEMI = &api.Money{
				CurrencyCode: singleInstallment.CurrencyCode,
				Val:          singleInstallment.Val,
			}
		} else if key == 0 {
			odi = &api.Money{
				CurrencyCode: singleInstallment.CurrencyCode,
				Val:          singleInstallment.Val,
			}
		} else {
			equatedEMI = &api.Money{
				CurrencyCode: singleInstallment.CurrencyCode,
				Val:          singleInstallment.Val,
			}
		}
	}

	// this case to handle of tenure only have 1 tenor month
	if equatedEMI == nil {
		equatedEMI = lastEMI
		lastEMI = nil
	}

	return &api.MonthlyRepayment{
		Emi:     equatedEMI,
		LastEmi: lastEMI,
		Odi:     odi,
	}
}

func generateRepaymentInstallmentCount(installment map[int64]*api.Money, timePeriodInMonths int64) int64 {
	for key := range installment {
		if key == 0 {
			return timePeriodInMonths + 1
		}
	}
	return timePeriodInMonths
}

package handlerlogic

import (
	"context"
	"encoding/json"
	"fmt"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/logic/presenters/cardtransactionpresenter"
	"gitlab.myteksi.net/dakota/transaction-history/logic/presenters/loantransactionpresenter"
	"gitlab.myteksi.net/dakota/transaction-history/logic/presenters/transactionpresenter"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetTransactionDetailStruct ...
type GetTransactionDetailStruct struct {
	AccountServiceClient accountService.AccountService
	account              *accountService.GetAccountResponse
	accountErr           error
}

// GetAccountOnce ...
func (g *GetTransactionDetailStruct) GetAccountOnce(ctx context.Context, accountID string) (*accountService.GetAccountResponse, error) {
	if g.account != nil || g.accountErr != nil {
		return g.account, g.accountErr
	}
	acc, err := g.AccountServiceClient.GetAccountDetailsByAccountID(ctx, &accountService.GetAccountRequest{
		AccountID:    accountID,
		FetchBalance: false,
	})
	g.account = acc
	g.accountErr = err
	return acc, err
}

// GetTransactionDetailRequestValidator validates the request parameters
func GetTransactionDetailRequestValidator(req *api.GetTransactionDetailRequest) []servus.ErrorDetail {
	var errors []servus.ErrorDetail
	if req.AccountID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'accountID' is a mandatory parameter.",
		})
	}
	if req.TransactionID == "" {
		errors = append(errors, servus.ErrorDetail{
			Message: "'transactionID' is a mandatory parameter.",
		})
	}
	return errors
}

// GetTransactionDetail fetches the transactions from the DB and returns the formatted GetTransactionDetailResponse.
func (g *GetTransactionDetailStruct) GetTransactionDetail(ctx context.Context, req *api.GetTransactionDetailRequest) (*api.GetTransactionDetailResponse, error) {
	filters := prepareTransactionDataFilters(req)
	dbResponse, err := storage.TransactionsDataD.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, fmt.Sprintf("Error getting transaction history data, err: %s", err.Error()))
		return nil, customErr.DefaultInternalServerError
	}
	if len(dbResponse) == 0 || err == data.ErrNoData {
		return emptyGetTransactionDetailsResponse(), nil
	}

	finalTransactionData := filterTxnOnAccountPhaseAndType(dbResponse)
	if finalTransactionData == nil {
		return emptyGetTransactionDetailsResponse(), nil
	}
	batchID := dbResponse[0].ClientBatchID
	if finalTransactionData.TransactionDomain == constants.DebitCardDomain && !finalTransactionData.IsCardOpsTxn() && !finalTransactionData.IsRewardsTxn() &&
		!finalTransactionData.IsCardMaintenanceFeeTxn() {
		return g.getCardTransactionDetailsResponseGenerator(ctx, finalTransactionData, req, batchID), nil
	} else if finalTransactionData.TransactionDomain == constants.LendingDomain || finalTransactionData.TransactionDomain == constants.BizLendingDomain {
		return g.getLoanTransactionDetailsResponseGenerator(ctx, finalTransactionData, req, batchID), nil
	}
	return g.getTransactionDetailsResponseGenerator(ctx, finalTransactionData, req, batchID), nil
}

// filterTxnOnAccountPhaseAndType Filter out the transactions based on the account phase and type
func filterTxnOnAccountPhaseAndType(dbResponse []*storage.TransactionsData) *storage.TransactionsData {
	var finalData *storage.TransactionsData
	// Filter out all incoming failed and cancelled transfer, processing will be filtered out by below loop
	filteredDBResponse := filterOutIncomingFailedAndCancelledTransfer(dbResponse, constants.DefaultAccountAddress)
	// Filter out apply earmark failed transfer
	filteredDBResponse = filterOutApplyEarmarkFailedTransfer(filteredDBResponse, constants.DefaultAccountAddress)
	// Filter out settled or released card txns
	filteredDBResponse = filterOutSettledOrReleasedCardTxn(filteredDBResponse, constants.DefaultAccountAddress)

	var committedPostingTx, pendingOutGoingTx, releaseTx *storage.TransactionsData
	for _, transaction := range filteredDBResponse {
		if transaction.AccountAddress != constants.DefaultAccountAddress {
			continue
		}
		if transaction.AccountPhase == constants.PostingPhaseCommitted {
			committedPostingTx = transaction
		} else if transaction.AccountPhase == constants.PostingPhasePendingOutgoing && transaction.TmTransactionType == constants.OutboundAuthorisation {
			pendingOutGoingTx = transaction
		} else if transaction.AccountPhase == constants.PostingPhasePendingOutgoing && transaction.TmTransactionType == constants.Release &&
			(transaction.TransactionType == constants.ReleaseEarmarkTransactionType || transaction.TransactionType == constants.NewCardIssuanceFeeTransactionType) {
			releaseTx = transaction
		}
	}
	if committedPostingTx != nil {
		finalData = committedPostingTx
	} else if releaseTx != nil {
		finalData = releaseTx
	} else if pendingOutGoingTx != nil {
		finalData = pendingOutGoingTx
	}
	return finalData
}

// Prepares filter for fetching data from the TransactionData
func prepareTransactionDataFilters(req *api.GetTransactionDetailRequest) []data.Condition {
	filters := []data.Condition{
		data.EqualTo("AccountID", req.AccountID),
		data.EqualTo("ClientTransactionID", req.TransactionID),
	}
	return filters
}

func fetchPaymentData(ctx context.Context, accountID string, batchID string) *storage.PaymentDetail {
	filters := []data.Condition{
		data.EqualTo("AccountID", accountID),
		data.EqualTo("TransactionID", batchID),
	}
	response, err := storage.PaymentDetailD.Find(ctx, filters...)
	if err != nil || len(response) == 0 {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "No data present in the paymentDetailDB")
		return nil
	}
	return response[0]
}

func fetchCardData(ctx context.Context, accountID string, batchID string) *storage.CardTransactionDetail {
	filters := []data.Condition{
		data.EqualTo("AccountID", accountID),
		data.EqualTo("CardTransactionID", batchID),
	}
	response, err := storage.CardTransactionDetailD.Find(ctx, filters...)
	if err != nil || len(response) == 0 {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "No data present in the cardDetailDB")
		return nil
	}
	return response[0]
}

func fetchLoanData(ctx context.Context, batchID string) *storage.LoanDetail {
	filters := []data.Condition{
		data.EqualTo("PaymentTransactionID", batchID),
	}
	response, err := storage.LoanDetailD.Find(ctx, filters...)
	if err != nil || len(response) == 0 {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, "No data present in the loanDetailDB")
		return nil
	}
	return response[0]
}

// Get the transaction status description
func getStatusDescription(ctx context.Context, transaction *storage.TransactionsData, transactionsPaymentDetail map[string]*storage.PaymentDetail) string {
	var statusDesc string
	var statusDetails dto.StatusDetails

	// For other Transactions(Currently Payments)
	if transaction.ClientBatchID != "" {
		paymentDetail, ok := transactionsPaymentDetail[transaction.ClientBatchID]
		if ok {
			err := json.Unmarshal(paymentDetail.StatusDetails, &statusDetails)
			if err != nil {
				slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, fmt.Sprintf("Error parsing StatusDetails, err: %s", err.Error()))
			}
			if statusDetails.Description != "" {
				statusDesc = statusDetails.Description
			} else {
				statusDesc = statusDetails.Reason
			}
		} else { // For now, this will happen for manual posting only
			statusDesc = transaction.BatchErrorMessage
		}
	}
	return statusDesc
}

// Gather the response elements and return the final response
func (g *GetTransactionDetailStruct) getTransactionDetailsResponseGenerator(ctx context.Context, transaction *storage.TransactionsData, req *api.GetTransactionDetailRequest, batchID string) *api.GetTransactionDetailResponse {
	paymentDetail := fetchPaymentData(ctx, req.AccountID, batchID)
	account, _ := g.GetAccountOnce(ctx, req.AccountID)
	response := transactionpresenter.GetPaymentAndCasaTransactionDetail(ctx, transaction, paymentDetail, account)
	return response
}

// Gather the response elements and return the final response
func (g *GetTransactionDetailStruct) getCardTransactionDetailsResponseGenerator(ctx context.Context, transaction *storage.TransactionsData, req *api.GetTransactionDetailRequest, batchID string) *api.GetTransactionDetailResponse {
	cardDetail := fetchCardData(ctx, req.AccountID, batchID)
	response := cardtransactionpresenter.GetCardTransactionDetail(ctx, transaction, cardDetail)
	return response
}

// Gather the response elements and return the final response
func (g *GetTransactionDetailStruct) getLoanTransactionDetailsResponseGenerator(ctx context.Context, transaction *storage.TransactionsData, req *api.GetTransactionDetailRequest, batchID string) *api.GetTransactionDetailResponse {
	loanDetail := fetchLoanData(ctx, batchID)
	paymentDetail := fetchPaymentData(ctx, req.AccountID, batchID)
	account, _ := g.GetAccountOnce(ctx, req.AccountID)
	response := loantransactionpresenter.GetLoanCasaTransactionDetail(ctx, transaction, loanDetail, paymentDetail, account)
	return response
}

// func (g *GetTransactionDetailStruct) getBizDepositTransactionDetailsResponseGenerator(ctx context.Context, transaction *storage.TransactionsData, req *api.GetTransactionDetailRequest, batchID string) *api.GetTransactionDetailResponse {
// 	paymentDetail := fetchPaymentData(ctx, req.AccountID, batchID)
// 	account, _ := g.GetAccountOnce(ctx, req.AccountID)
// 	response := transactionpresenter.GetBizDepositTransactionDetail(ctx, transaction, paymentDetail, account)
// 	return response
// }

func (g *GetTransactionDetailStruct) getInternalCardTransactionDetailsResponseGenerator(ctx context.Context, transaction *storage.TransactionsData, req *api.GetInternalTransactionDetailRequest, batchID string) *api.GetInternalTransactionDetailResponse {
	cardDetail := fetchCardData(ctx, req.AccountID, batchID)
	return cardtransactionpresenter.GetInternalCardTransactionDetail(ctx, transaction, req, cardDetail)
}

func (g *GetTransactionDetailStruct) getInternalLoanTransactionDetailsResponseGenerator(ctx context.Context, transaction *storage.TransactionsData, req *api.GetInternalTransactionDetailRequest, batchID string) *api.GetInternalTransactionDetailResponse {
	loanDetail := fetchLoanData(ctx, transaction.ClientBatchID)
	paymentDetail := fetchPaymentData(ctx, transaction.AccountID, batchID)
	account, _ := g.GetAccountOnce(ctx, req.AccountID)
	return loantransactionpresenter.GetInternalLoanTransactionDetail(ctx, transaction, req, loanDetail, paymentDetail, account)
}

// Response in case of TransactionDetails not present
func emptyGetTransactionDetailsResponse() *api.GetTransactionDetailResponse {
	response := &api.GetTransactionDetailResponse{}
	return response
}

// getSourceOfFund will return the source of fund based on data from payments and TM.
// func getSourceOfFund(ctx context.Context, transaction *storage.TransactionsData, transactionsPaymentDetail map[string]*storage.PaymentDetail, accountServiceClient accountService.AccountService) string {
//	var sourceOfFund string
//	if transaction.DebitOrCredit != constants.DEBIT {
//		return sourceOfFund
//	}
//	if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
//		return sourceOfFundSubAccount(ctx, transaction)
//	}
//
//	if transaction.ClientBatchID != "" {
//		_, ok := transactionsPaymentDetail[transaction.ClientBatchID]
//		if ok {
//			return sourceOfFundPayment(ctx, transaction, accountServiceClient)
//		}
//	}
//
//	return sourceOfFund
// }

// func sourceOfFundPayment(ctx context.Context, transaction *storage.TransactionsData, accountServiceClient accountService.AccountService) string {
//	account, accountErr := getAccount(ctx, transaction.AccountID, accountServiceClient)
//	if accountErr != nil {
//		return ""
//	}
//
//	if account.Account.ParentAccountID == "" {
//		return constants.MainAccountSource
//	}
//	return account.Account.Name
// }

// func sourceOfFundSubAccount(ctx context.Context, transaction *storage.TransactionsData) string {
//	if constants.PocketFundingTransactionSubType == transaction.TransactionSubtype {
//		return constants.MainAccountSource
//	}
//	var batchDetails map[string]string
//	err := json.Unmarshal(transaction.BatchDetails, &batchDetails)
//	if err != nil {
//		slog.FromContext(ctx).Warn(constants.GetAllTransactionsLogicLogTag, fmt.Sprintf("Error parsing batchDetails, err: %s", err.Error()))
//		return ""
//	}
//	return batchDetails["source_display_name"]
// }
// func getAccount(ctx context.Context, accountID string, accountServiceClient accountService.AccountService) (*accountService.GetAccountResponse, error) {
//	account, accountFetchError := accountServiceClient.GetAccountDetailsByAccountID(ctx, &accountService.GetAccountRequest{
//		AccountID: accountID,
//	})
//
//	if accountFetchError != nil {
//		slog.FromContext(ctx).Warn(constants.GetAccountFromAccountServiceTag, fmt.Sprintf("Error in getting account details, err %s", accountFetchError.Error()))
//		return nil, accountFetchError
//	}
//	return account, nil
// }

// nolint: gocognit, funlen
func getTxnScenarioKey(txnData *storage.TransactionsData) string {
	var key string
	switch {
	case txnData.TransactionType == constants.TransferMoneyTxType && txnData.TransactionSubtype == constants.IntrabankTransactionSubtype && txnData.DebitOrCredit == constants.DEBIT:
		key = constants.TransferIntrabankDebitScenarioKey
	case txnData.TransactionType == constants.TransferMoneyTxType && txnData.TransactionSubtype == constants.IntrabankTransactionSubtype && txnData.DebitOrCredit == constants.CREDIT:
		key = constants.TransferIntrabankCreditScenarioKey
	case txnData.TransactionType == constants.TransferMoneyTxType && txnData.TransactionSubtype == constants.PocketFundingTransactionSubType && txnData.DebitOrCredit == constants.DEBIT:
		key = constants.TransferMainToPocketDebitScenarioKey
	case txnData.TransactionType == constants.TransferMoneyTxType && txnData.TransactionSubtype == constants.PocketFundingTransactionSubType && txnData.DebitOrCredit == constants.CREDIT:
		key = constants.TransferMainToPocketCreditScenarioKey
	case txnData.TransactionType == constants.TransferMoneyTxType && txnData.TransactionSubtype == constants.PocketWithdrawalTransactionSubType && txnData.DebitOrCredit == constants.DEBIT:
		key = constants.TransferPocketToMainDebitScenarioKey
	case txnData.TransactionType == constants.TransferMoneyTxType && txnData.TransactionSubtype == constants.PocketWithdrawalTransactionSubType && txnData.DebitOrCredit == constants.CREDIT:
		key = constants.TransferPocketToMainCreditScenarioKey
	case txnData.TransactionType == constants.TransferMoneyRevTxType && txnData.TransactionSubtype == constants.IntrabankTransactionSubtype && txnData.DebitOrCredit == constants.DEBIT:
		key = constants.TransferMoneyReversalIntrabankDebitScenarioKey
	case txnData.TransactionType == constants.TransferMoneyRevTxType && txnData.TransactionSubtype == constants.IntrabankTransactionSubtype && txnData.DebitOrCredit == constants.CREDIT:
		key = constants.TransferMoneyReversalIntrabankCreditScenarioKey
	case txnData.TransactionType == constants.FundInTxType:
		key = constants.FundInScenarioKey
	case txnData.TransactionType == constants.FundInRevTxType:
		key = constants.FundInReversalScenarioKey
	case txnData.TransactionType == constants.SendMoneyTxType && txnData.DebitOrCredit == constants.DEBIT:
		key = constants.SendMoneyDebitScenarioKey
	case txnData.TransactionType == constants.SendMoneyRevTxType && txnData.DebitOrCredit == constants.CREDIT:
		key = constants.SendMoneyReversalCreditScenarioKey
	case txnData.TransactionType == constants.ReceiveMoneyTxType && txnData.DebitOrCredit == constants.CREDIT:
		key = constants.ReceiveMoneyCreditScenarioKey
	case txnData.TransactionType == constants.ReceiveMoneyRevTxType && txnData.DebitOrCredit == constants.DEBIT:
		key = constants.ReceiveMoneyReversalDebitScenarioKey
	case txnData.TransactionType == constants.InterestPayoutTransactionType:
		key = constants.InterestPayoutScenarioKey
	case txnData.TransactionType == constants.Adjustment && txnData.DebitOrCredit == constants.DEBIT:
		key = constants.AdjustmentBankInitiatedDebitScenarioKey
	case txnData.TransactionType == constants.Adjustment && txnData.DebitOrCredit == constants.CREDIT:
		key = constants.AdjustmentBankInitiatedCreditScenarioKey
	case txnData.TransactionType == constants.ApplyEarmarkTransactionType:
		key = constants.ApplyEarmarkScenarioKey
	case txnData.TransactionType == constants.ReleaseEarmarkTransactionType:
		key = constants.ReleaseEarmarkScenarioKey
	default:
		key = "default"
	}
	return key
}

func (g *GetTransactionDetailStruct) isChildAccount(ctx context.Context, accountID string) (bool, error) {
	accountDetail, err := g.GetAccountOnce(ctx, accountID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, fmt.Sprintf("error while fetching account details: %s", err.Error()))
		return false, err
	}
	return accountDetail.Account.ParentAccountID != "", nil
}

func (g *GetTransactionDetailStruct) getCounterPartyNameForOpsTransactions(ctx context.Context, transaction *storage.TransactionsData) string {
	var counterPartyDisplayName string
	isChild, _ := g.isChildAccount(ctx, transaction.AccountID)
	if isChild {
		counterPartyDisplayName = localise.Translate(constants.BankAdjustment)
	} else {
		counterPartyDisplayName = localise.Translate(constants.BankAdjustment)
	}
	return counterPartyDisplayName
}

func (g *GetTransactionDetailStruct) getCounterPartyDisplayNameForPocketDBMY(ctx context.Context, transaction *storage.TransactionsData, batchDetails map[string]string) string {
	var counterPartyDisplayName string
	isChild, _ := g.isChildAccount(ctx, transaction.AccountID)
	if isChild {
		counterPartyDisplayName = localise.Translate(constants.DBMYBankPocket)
	} else {
		if transaction.DebitOrCredit == constants.CREDIT {
			counterPartyDisplayName = batchDetails["source_display_name"]
		} else {
			counterPartyDisplayName = batchDetails["destination_display_name"]
		}
	}
	return counterPartyDisplayName
}

// nolint: dupl
func (g *GetTransactionDetailStruct) getCounterPartyDisplayNamePocketView(ctx context.Context, transaction *storage.TransactionsData, transactionsPaymentDetail map[string]*storage.PaymentDetail) string {
	var counterPartyDisplayName string
	var batchDetails map[string]string

	err := json.Unmarshal(transaction.BatchDetails, &batchDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetTransactionDetailLogTag, fmt.Sprintf("Error parsing batchDetails, err: %s", err.Error()))
	}
	// For DBMY Pocket View
	switch config.GetTenant() {
	case tenants.TenantMY:
		if utils.SearchStringArray(constants.OpsInitiateTransactionType, transaction.TransactionType) {
			counterPartyDisplayName = g.getCounterPartyNameForOpsTransactions(ctx, transaction)
		} else if utils.SearchStringArray(constants.PocketTransfersTransactionSubTypes, transaction.TransactionSubtype) {
			counterPartyDisplayName = g.getCounterPartyDisplayNameForPocketDBMY(ctx, transaction, batchDetails)
		}
	}
	if counterPartyDisplayName != "" {
		return counterPartyDisplayName
	}
	return getCounterPartyDisplayName(ctx, transaction, transactionsPaymentDetail)
}

// Get the counterparty accountID from the PaymentDB
func getCounterPartyAccountIDForLending(_ context.Context, _ *storage.TransactionsData, payment *storage.PaymentDetail) string {
	var accountID string
	if payment != nil {
		accountID = payment.CounterPartyAccountID
	}
	return accountID
}

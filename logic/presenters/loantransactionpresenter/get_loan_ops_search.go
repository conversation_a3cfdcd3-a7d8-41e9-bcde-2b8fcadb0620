package loantransactionpresenter

import (
	"context"

	"gitlab.myteksi.net/dakota/transaction-history/dto"

	"github.com/samber/lo"
	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

func mapOpsSearchCounterPartiesForLoan(dtoCounterParties []dto.CounterParty) []api.CounterParty {
	var counterParties []api.CounterParty
	for _, counterParty := range dtoCounterParties {
		counterParties = append(counterParties, api.CounterParty{
			DisplayName:   counterParty.DisplayName,
			AccountNumber: counterParty.AccountNumber,
			SwiftCode:     counterParty.SwiftCode,
		})
	}
	return counterParties
}

// GetLoanTransactionOpsSearch returns the formatted OpsSearchResponse for LENDING domain
func GetLoanTransactionOpsSearch(ctx context.Context, transaction *storage.TransactionsData, transactionPaymentDetail map[storage.PaymentDataKey]*storage.PaymentDetail, transactionsLoanDetail map[storage.LoanDataKey]*storage.LoanDetail) api.OpsSearchResponse {
	var (
		displayName   string
		loanDetail    *storage.LoanDetail
		paymentDetail *storage.PaymentDetail
	)

	if transaction.ClientBatchID != "" {
		detail, ok := transactionsLoanDetail[storage.BuildLoanDataKey(transaction.ClientBatchID)]
		if ok {
			loanDetail = detail
		}
		detail2, ok := transactionPaymentDetail[storage.BuildPaymentDataKey(transaction.ClientBatchID, transaction.AccountID)]
		if ok {
			paymentDetail = detail2
		}
	}
	if loanDetail == nil {
		return defaultOpsSearchResponse(ctx, transaction)
	}

	key := transaction.GetTxnScenarioKey()
	helperFuncs := presenterhelper.TransactionResponseHelperFuncs[key]
	status := presenterhelper.GetLoanCasaTransactionStatus(transaction, loanDetail)
	displayName = presenterhelper.GetLoanTransactionDisplayNameForOpsSearch(ctx, transaction, loanDetail)
	swiftCode := lo.Ternary(paymentDetail != nil, paymentDetail.GetCounterPartyAccount(ctx).SwiftCode, "")
	transactionDescription, _ := helperFuncs.DescriptionFunc(ctx, transaction, displayName)
	amountInCents := transaction.FormattedAmount(ctx)
	batchValueTS := utils.TruncateTillSecond(transaction.BatchValueTimestamp)
	return api.OpsSearchResponse{
		Amount:        amountInCents,
		Currency:      transaction.TransactionCurrency,
		BatchID:       transaction.ClientBatchID,
		TransactionID: transaction.ClientTransactionID,
		CreditOrDebit: transaction.DebitOrCredit,
		CounterParty: &api.CounterParty{
			DisplayName:   displayName,
			AccountNumber: loanDetail.AccountID,
			SwiftCode:     swiftCode,
		},
		CounterParties:         mapOpsSearchCounterPartiesForLoan(presenterhelper.GetLoanCounterParties(ctx, transaction, paymentDetail, loanDetail)),
		Status:                 status,
		TransactionTimestamp:   batchValueTS,
		TransactionType:        transaction.TransactionType,
		TransactionSubtype:     transaction.TransactionSubtype,
		TransactionDescription: transactionDescription,
		AccountID:              transaction.AccountID,
	}
}

func defaultOpsSearchResponse(ctx context.Context, transaction *storage.TransactionsData) api.OpsSearchResponse {
	amountInCents := transaction.FormattedAmount(ctx)
	batchValueTS := utils.TruncateTillSecond(transaction.BatchValueTimestamp)
	return api.OpsSearchResponse{
		Amount:        amountInCents,
		Currency:      transaction.TransactionCurrency,
		BatchID:       transaction.ClientBatchID,
		TransactionID: transaction.ClientTransactionID,
		CreditOrDebit: transaction.DebitOrCredit,
		CounterParty: &api.CounterParty{
			DisplayName:   "",
			AccountNumber: "",
			SwiftCode:     "",
		},
		Status:                 transaction.BatchStatus,
		TransactionTimestamp:   batchValueTS,
		TransactionType:        transaction.TransactionType,
		TransactionSubtype:     transaction.TransactionSubtype,
		TransactionDescription: "",
		AccountID:              transaction.AccountID,
	}
}

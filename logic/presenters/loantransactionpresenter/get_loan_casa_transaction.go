// Package loantransactionpresenter ...
package loantransactionpresenter

import (
	"context"

	"github.com/samber/lo"

	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GenerateLoanCasaTxnResponse current loan transaction
func GenerateLoanCasaTxnResponse(ctx context.Context, transaction *storage.TransactionsData, isCASAAccount bool, transactionsLoanDetail map[string]*storage.LoanDetail) *api.TransactionHistoryResponse {
	var iconURL string
	var category *api.Category
	var txnLoanDetail *storage.LoanDetail

	if transaction.ClientBatchID != "" {
		detail, ok := transactionsLoanDetail[transaction.ClientBatchID]
		if ok {
			txnLoanDetail = detail
		}
	}
	counterPartyDisplayName := presenterhelper.GetLoanTransactionDisplayNameForListing(transaction)
	status := presenterhelper.GetLoanCasaTransactionStatus(transaction, txnLoanDetail)
	amountInCents := transaction.FormattedAmount(ctx)

	var txnScenarioKey = transaction.GetTxnScenarioKey()
	txnScenarioKey = lo.Ternary(isCASAAccount, "CASA."+txnScenarioKey, txnScenarioKey)

	iconURL = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].Icon
	category = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].Category

	return &api.TransactionHistoryResponse{
		TransactionID:     transaction.ClientTransactionID,
		BatchID:           transaction.ClientBatchID,
		DisplayName:       counterPartyDisplayName,
		IconURL:           iconURL,
		Amount:            amountInCents,
		Currency:          transaction.TransactionCurrency,
		Status:            status,
		CreationTimestamp: utils.TruncateTillSecond(transaction.BatchValueTimestamp),
		Category:          category,
	}
}

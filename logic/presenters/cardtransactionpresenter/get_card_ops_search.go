package cardtransactionpresenter

import (
	"context"

	"gitlab.myteksi.net/dakota/transaction-history/logic"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetCardTransactionOpsSearch returns the formatted OpsSearchResponse for card domain
func GetCardTransactionOpsSearch(ctx context.Context, transaction *storage.TransactionsData, transactionsCardDetail map[storage.CardDataKey]*storage.CardTransactionDetail) api.OpsSearchResponse {
	var (
		displayName                   string
		status                        string
		cardDetail                    *storage.CardTransactionDetail
		isPartialSettlement           = false
		captureAmountTillDate         int64
		captureOriginalAmountTillDate int64
	)

	if transaction.ClientBatchID != "" {
		detail, ok := transactionsCardDetail[storage.BuildCardDataKey(transaction.ClientBatchID, transaction.AccountID)]
		if ok {
			cardDetail = detail
		}
	}
	if cardDetail == nil {
		return defaultOpsSearchResponse(ctx, transaction)
	}

	key := transaction.GetTxnScenarioKey()
	helperFuncs := presenterhelper.TransactionResponseHelperFuncs[key]
	if cardDetail != nil {
		status = cardDetail.GetFormattedTxnStatusForTxnDetails()
		captureAmountTillDate = logic.FormattedAmountInCents(transaction, cardDetail.CaptureAmountTillDate)
		captureOriginalAmountTillDate = logic.FormattedAmountInCents(transaction, cardDetail.CaptureOriginalAmountTillDate)
		if cardDetail.CaptureAmountTillDate > 0 {
			isPartialSettlement = true
		}
	}
	displayName = presenterhelper.GetCardTransactionDisplayNameForListing(ctx, transaction, cardDetail)
	transactionDescription, _ := helperFuncs.DescriptionFunc(ctx, cardDetail, displayName)
	if _, ok := constants.ATMFeeTransactionTypes[transaction.TransactionType]; ok {
		transactionDescription = presenterhelper.GetDBMYCardAtmFeeTransactionDecription(transaction, displayName)
	}
	amountInCents := logic.GetTransactionAmount(ctx, transaction, cardDetail)
	batchValueTS := utils.TruncateTillSecond(transaction.BatchValueTimestamp)
	counterParty := api.CounterParty{
		DisplayName: displayName,
	}
	return api.OpsSearchResponse{
		Amount:                         amountInCents,
		Currency:                       transaction.TransactionCurrency,
		BatchID:                        transaction.ClientBatchID,
		TransactionID:                  transaction.ClientTransactionID,
		CreditOrDebit:                  transaction.DebitOrCredit,
		CounterParty:                   &counterParty,
		CounterParties:                 []api.CounterParty{counterParty},
		Status:                         status,
		TransactionTimestamp:           batchValueTS,
		TransactionType:                transaction.TransactionType,
		TransactionSubtype:             transaction.TransactionSubtype,
		TransactionDescription:         transactionDescription,
		AccountID:                      transaction.AccountID,
		CapturedAmountTillDate:         captureAmountTillDate,
		CapturedOriginalAmountTillDate: captureOriginalAmountTillDate,
		IsPartialSettlement:            isPartialSettlement,
	}
}

func defaultOpsSearchResponse(ctx context.Context, transaction *storage.TransactionsData) api.OpsSearchResponse {
	amountInCents := transaction.FormattedAmount(ctx)
	batchValueTS := utils.TruncateTillSecond(transaction.BatchValueTimestamp)
	return api.OpsSearchResponse{
		Amount:        amountInCents,
		Currency:      transaction.TransactionCurrency,
		BatchID:       transaction.ClientBatchID,
		TransactionID: transaction.ClientTransactionID,
		CreditOrDebit: transaction.DebitOrCredit,
		CounterParty: &api.CounterParty{
			DisplayName: "",
		},
		Status:                 transaction.BatchStatus,
		TransactionTimestamp:   batchValueTS,
		TransactionType:        transaction.TransactionType,
		TransactionSubtype:     transaction.TransactionSubtype,
		TransactionDescription: "",
		AccountID:              transaction.AccountID,
	}
}

// Package cardtransactionpresenter ...
package cardtransactionpresenter

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/transaction-history/logic"

	"gitlab.myteksi.net/dakota/transaction-history/constants"

	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

var (
	cardTxnRespFuncTag = "generateCardTxnResponse"
)

// GenerateCardTxnResponse ...
func GenerateCardTxnResponse(ctx context.Context, transaction *storage.TransactionsData, cardTransactionsDetail map[string]*storage.CardTransactionDetail) *api.TransactionHistoryResponse {
	var iconURL string
	var category *api.Category
	var status string
	logger := slog.FromContext(ctx)
	cardTxnDetail, ok := cardTransactionsDetail[transaction.ClientBatchID]
	if ok {
		status = cardTxnDetail.GetFormattedTxnStatus()
	} else {
		logger.Warn(cardTxnRespFuncTag, fmt.Sprintf("failed to find card txn detail: clientBatchID %s", transaction.ClientBatchID))
		return nil
	}
	counterPartyDisplayName := presenterhelper.GetCardTransactionDisplayNameForListing(ctx, transaction, cardTxnDetail)
	amountInCents := transaction.FormattedAmount(ctx)
	// for card txn that doesn't go through the card network, we don't need to rely on the card detail capture amount
	if status == constants.CompletedStatus && !transaction.IsAtmFeeTxn() && !transaction.IsCardMaintenanceFeeTxn() &&
		cardTxnDetail != nil && cardTxnDetail.TransferType != constants.CardTransferTypeRefund {
		if cardTxnDetail.CaptureAmountTillDate > 0 {
			amountInCents = logic.FormattedAmountInCents(transaction, cardTxnDetail.CaptureAmountTillDate)
		} else {
			amountInCents = logic.FormattedAmountInCents(transaction, cardTxnDetail.CaptureAmount)
		}
	}
	txnScenarioKey := transaction.GetTxnScenarioKey()
	iconURL = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].Icon
	category = presenterhelper.TransactionResponseHelperFuncs[txnScenarioKey].Category

	return &api.TransactionHistoryResponse{
		TransactionID:     transaction.ClientTransactionID,
		BatchID:           transaction.ClientBatchID,
		DisplayName:       counterPartyDisplayName,
		IconURL:           iconURL,
		Amount:            amountInCents,
		Currency:          transaction.TransactionCurrency,
		Status:            status,
		CreationTimestamp: utils.TruncateTillSecond(transaction.BatchValueTimestamp),
		Category:          category,
	}
}

package cardtransactionpresenter

import (
	"context"
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"gitlab.myteksi.net/dakota/common/tenants"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/internal/presenterhelper"
	"gitlab.myteksi.net/dakota/transaction-history/localise"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

func TestGetCardTransactionDetail(t *testing.T) {
	err := os.Setenv("LOCALISATION_PATH", "../../../localise")
	if err != nil {
		return
	}
	defer func() { config.SetTenant("") }()
	config.SetTenant(tenants.TenantMY)
	mockAppConfig := &config.AppConfig{
		Locale: config.Locale{Language: "en"},
	}
	localise.Init(mockAppConfig)

	presenterhelper.InitTransactionResponseHelperFuncs(constants.IconURLMap)

	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "payment-core", "batch_remarks": ""})
	transactionDetails, _ := json.Marshal(map[string]string{"external_id": ""})
	transactionTimestamp := time.Unix(**********, 0).UTC()

	txnData := &storage.TransactionsData{
		ClientTransactionID:         "70e029285b814c9eaf1dffc0e72f9f23",
		ClientBatchID:               "2935138f-c022-41bc-a7e6-514bdd8c9f71",
		TmPostingInstructionBatchID: "7df1ae5d-f3b8-406f-8991-ab2e9784063e",
		TmTransactionID:             "0bd80337-1c54-4e44-aebd-41c6398aac45",
		BatchStatus:                 "ACCEPTED",
		BatchDetails:                batchDetails,
		TransactionDomain:           "DEBIT_CARD",
		TransactionType:             constants.SpendCardAtmTransactionType,
		TmTransactionType:           "TRANSFER",
		TransactionSubtype:          "MASTERCARD",
		TransactionDetails:          transactionDetails,
		AccountID:                   "*************",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.01",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
	}

	refundTxnData := &storage.TransactionsData{
		ClientTransactionID:         "70e029285b814c9eaf1dffc0e72f9f23",
		ClientBatchID:               "2935138f-c022-41bc-a7e6-514bdd8c9f71",
		TmPostingInstructionBatchID: "7df1ae5d-f3b8-406f-8991-ab2e9784063e",
		TmTransactionID:             "0bd80337-1c54-4e44-aebd-41c6398aac45",
		BatchStatus:                 "ACCEPTED",
		BatchDetails:                batchDetails,
		TransactionDomain:           "DEBIT_CARD",
		TransactionType:             constants.SpendRefundTransactionType,
		TmTransactionType:           "TRANSFER",
		TransactionSubtype:          constants.MastercardTransactionSubtype,
		TransactionDetails:          transactionDetails,
		AccountID:                   "*************",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.01",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
	}

	t.Run("happy-path", func(t *testing.T) {
		settlementDate := time.Unix(**********, 0).UTC()
		statusDetails, _ := json.Marshal(map[string]string{"description": ""})
		metadata, _ := json.Marshal(map[string]string{"mcc": "6011", "networkID": "MCN", "terminalID": "1000", "pinValidation": "Y", "authorizationID": "12345", "cardProxyNumber": "*********", "transactionType": "DIRECT_DEBIT", "currencyConvType": "DCC", "maskedCardNumber": "533349XXXXXX3209", "threeDsValidation": "Y", "transactionCategory": "ATM", "transactionSubCategory": "WITHDRAWAL", "retrievalReferenceNumber": "LI1692693016"})
		cardDetail := &storage.CardTransactionDetail{
			ID:                            0,
			CardTransactionID:             "2935138f-c022-41bc-a7e6-514bdd8c9f71",
			CardID:                        "bbd65a08-912f-4d66-bb8d-7cf06529ce83",
			TransactionDomain:             "DEBIT_CARD",
			TransactionType:               constants.SpendCardAtmTransactionType,
			TransactionSubType:            "MASTERCARD",
			TransferType:                  "CHARGE",
			Amount:                        1,
			Currency:                      "MYR",
			OriginalAmount:                1,
			CaptureOriginalAmountTillDate: 0,
			OriginalCurrency:              "MYR",
			CaptureAmount:                 1,
			CaptureAmountTillDate:         0,
			AccountID:                     "*************",
			MerchantDescription:           "Linh Vuong",
			Status:                        "COMPLETED",
			StatusDetails:                 statusDetails,
			Metadata:                      metadata,
			TailCardNumber:                "3209",
			CreationTimestamp:             time.Unix(**********, 0).UTC(),
			ValueTimestamp:                time.Unix(**********, 0).UTC(),
			CreatedAt:                     time.Unix(**********, 0).UTC(),
			UpdatedAt:                     time.Unix(**********, 0).UTC(),
		}
		expectedResp := &api.GetTransactionDetailResponse{
			Amount:                 -1,
			Currency:               locale.Currency,
			TransactionID:          "70e029285b814c9eaf1dffc0e72f9f23",
			BatchID:                "2935138f-c022-41bc-a7e6-514bdd8c9f71",
			TransactionDescription: "ATM withdrawal",
			TransactionRemarks:     "",
			Status:                 "COMPLETED",
			StatusDescription:      "",
			SourceOfFund:           "",
			CounterParty: &api.CounterParty{
				IconURL:            constants.IconURLMap[constants.DebitCardDomain],
				TransactionDetails: map[string]string{"txn_scenario": "SPEND_CARD_ATM"},
				DisplayName:        "ATM withdrawal",
			},
			CardTransactionDetail: &api.CardTransactionDetail{
				CardID:         "bbd65a08-912f-4d66-bb8d-7cf06529ce83",
				TailCardNumber: "**3209",
				SettlementDate: &settlementDate,
				BankFee:        "Waived",
			},
			FormattedLocalAmount:   "RM0.01",
			FormattedForeignAmount: "",
			TransactionTimestamp:   &transactionTimestamp,
			HasReceipt:             false,
			Category:               &api.Category{Name: "ATM Withdrawal"},
		}
		actualResp := GetCardTransactionDetail(context.Background(), txnData, cardDetail)
		assert.Equal(t, expectedResp, actualResp)
	})
	t.Run("happy-path origin capture amount", func(t *testing.T) {
		settlementDate := time.Unix(**********, 0).UTC()
		statusDetails, _ := json.Marshal(map[string]string{"description": ""})
		metadata, _ := json.Marshal(map[string]string{"mcc": "6011", "networkID": "MCN", "terminalID": "1000", "pinValidation": "Y", "authorizationID": "12345", "cardProxyNumber": "*********", "transactionType": "DIRECT_DEBIT", "currencyConvType": "DCC", "maskedCardNumber": "533349XXXXXX3209", "threeDsValidation": "Y", "transactionCategory": "ATM", "transactionSubCategory": "WITHDRAWAL", "retrievalReferenceNumber": "LI1692693016"})
		cardDetail := &storage.CardTransactionDetail{
			ID:                            0,
			CardTransactionID:             "2935138f-c022-41bc-a7e6-514bdd8c9f71",
			CardID:                        "bbd65a08-912f-4d66-bb8d-7cf06529ce83",
			TransactionDomain:             "DEBIT_CARD",
			TransactionType:               constants.SpendCardAtmTransactionType,
			TransactionSubType:            "MASTERCARD",
			TransferType:                  "CHARGE",
			Amount:                        1,
			Currency:                      "MYR",
			OriginalAmount:                1,
			CaptureOriginalAmountTillDate: 1,
			OriginalCurrency:              "USD",
			CaptureAmount:                 1,
			CaptureAmountTillDate:         0,
			AccountID:                     "*************",
			MerchantDescription:           "Linh Vuong",
			Status:                        "COMPLETED",
			StatusDetails:                 statusDetails,
			Metadata:                      metadata,
			TailCardNumber:                "3209",
			CreationTimestamp:             time.Unix(**********, 0).UTC(),
			ValueTimestamp:                time.Unix(**********, 0).UTC(),
			CreatedAt:                     time.Unix(**********, 0).UTC(),
			UpdatedAt:                     time.Unix(**********, 0).UTC(),
		}
		expectedResp := &api.GetTransactionDetailResponse{
			Amount:                 -1,
			Currency:               locale.Currency,
			TransactionID:          "70e029285b814c9eaf1dffc0e72f9f23",
			BatchID:                "2935138f-c022-41bc-a7e6-514bdd8c9f71",
			TransactionDescription: "ATM withdrawal",
			TransactionRemarks:     "",
			Status:                 "COMPLETED",
			StatusDescription:      "",
			SourceOfFund:           "",
			CounterParty: &api.CounterParty{
				IconURL:            constants.IconURLMap[constants.DebitCardDomain],
				TransactionDetails: map[string]string{"txn_scenario": "SPEND_CARD_ATM"},
				DisplayName:        "ATM withdrawal",
			},
			CardTransactionDetail: &api.CardTransactionDetail{
				CardID:         "bbd65a08-912f-4d66-bb8d-7cf06529ce83",
				TailCardNumber: "**3209",
				SettlementDate: &settlementDate,
				BankFee:        "Waived",
			},
			FormattedLocalAmount:   "RM0.01",
			FormattedForeignAmount: "$0.01",
			TransactionTimestamp:   &transactionTimestamp,
			HasReceipt:             false,
			Category:               &api.Category{Name: "ATM Withdrawal"},
		}
		actualResp := GetCardTransactionDetail(context.Background(), txnData, cardDetail)
		assert.Equal(t, expectedResp, actualResp)
	})
	t.Run("happy-path refund", func(t *testing.T) {
		settlementDate := time.Unix(**********, 0).UTC()
		statusDetails, _ := json.Marshal(map[string]string{"description": ""})
		metadata, _ := json.Marshal(map[string]string{"mcc": "5422", "networkID": "MCN", "terminalID": "1000", "pinValidation": "Y", "authorizationID": "12345", "cardProxyNumber": "*********", "transactionType": "DIRECT_CREDIT", "currencyConvType": "DCC", "maskedCardNumber": "533349XXXXXX3209", "threeDsValidation": "Y", "transactionCategory": "POS", "transactionSubCategory": "CONTACTLESS", "retrievalReferenceNumber": "LI1692693016"})
		cardDetail := &storage.CardTransactionDetail{
			ID:                            0,
			CardTransactionID:             "2935138f-c022-41bc-a7e6-514bdd8c9f71",
			CardID:                        "bbd65a08-912f-4d66-bb8d-7cf06529ce83",
			TransactionDomain:             "DEBIT_CARD",
			TransactionType:               constants.SpendRefundTransactionType,
			TransactionSubType:            constants.MastercardTransactionSubtype,
			TransferType:                  constants.CardTransferTypeRefund,
			Amount:                        1,
			Currency:                      "MYR",
			OriginalAmount:                1,
			CaptureOriginalAmountTillDate: 1,
			OriginalCurrency:              "MYR",
			CaptureAmount:                 0,
			CaptureAmountTillDate:         0,
			AccountID:                     "*************",
			MerchantDescription:           "Test merchant name",
			Status:                        "COMPLETED",
			StatusDetails:                 statusDetails,
			Metadata:                      metadata,
			TailCardNumber:                "3209",
			CreationTimestamp:             time.Unix(**********, 0).UTC(),
			ValueTimestamp:                time.Unix(**********, 0).UTC(),
			CreatedAt:                     time.Unix(**********, 0).UTC(),
			UpdatedAt:                     time.Unix(**********, 0).UTC(),
		}
		expectedResp := &api.GetTransactionDetailResponse{
			Amount:                 1,
			Currency:               locale.Currency,
			TransactionID:          "70e029285b814c9eaf1dffc0e72f9f23",
			BatchID:                "2935138f-c022-41bc-a7e6-514bdd8c9f71",
			TransactionDescription: "Refund from Test merchant name",
			TransactionRemarks:     "",
			Status:                 "COMPLETED",
			StatusDescription:      "",
			SourceOfFund:           "",
			CounterParty: &api.CounterParty{
				IconURL:            constants.IconURLMap[constants.DebitCardDomain],
				TransactionDetails: map[string]string{"txn_scenario": "SPEND_CARD_REFUND"},
				DisplayName:        "Test merchant name",
			},
			CardTransactionDetail: &api.CardTransactionDetail{
				CardID:         "bbd65a08-912f-4d66-bb8d-7cf06529ce83",
				TailCardNumber: "**3209",
				SettlementDate: &settlementDate,
				BankFee:        "Waived",
			},
			FormattedLocalAmount:   "RM0.01",
			FormattedForeignAmount: "",
			TransactionTimestamp:   &transactionTimestamp,
			HasReceipt:             false,
			Category:               &api.Category{Name: "Refund Payment"},
		}
		actualResp := GetCardTransactionDetail(context.Background(), refundTxnData, cardDetail)
		assert.Equal(t, expectedResp, actualResp)
	})

	t.Run("card-details-nil", func(t *testing.T) {
		expectedResp := &api.GetTransactionDetailResponse{
			Amount:                 -1,
			Currency:               locale.Currency,
			TransactionID:          "70e029285b814c9eaf1dffc0e72f9f23",
			BatchID:                "2935138f-c022-41bc-a7e6-514bdd8c9f71",
			TransactionDescription: "",
			TransactionRemarks:     "",
			Status:                 "",
			StatusDescription:      "",
			SourceOfFund:           "",
			CounterParty: &api.CounterParty{
				IconURL: constants.IconURLMap[constants.DebitCardDomain],
			},
			CardTransactionDetail:  &api.CardTransactionDetail{},
			FormattedLocalAmount:   "RM0.01",
			FormattedForeignAmount: "",
			TransactionTimestamp:   &transactionTimestamp,
			HasReceipt:             false,
			Category:               &api.Category{Name: "ATM Withdrawal"},
		}
		actualResp := GetCardTransactionDetail(context.Background(), txnData, nil)
		assert.Equal(t, expectedResp, actualResp)
	})
}

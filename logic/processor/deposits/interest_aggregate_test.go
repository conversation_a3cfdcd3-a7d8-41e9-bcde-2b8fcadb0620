package deposits

import (
	"context"
	"testing"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"

	"github.com/stretchr/testify/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

func TestHandleDepositsCoreStreamForInterestAggHandlesNoTransactions(t *testing.T) {
	mockRedis := &redisMock.Client{}
	ctx := context.Background()
	aggregationConfig := config.InterestAggregationConfig{RetentionDay: 7}
	txData := &deposits_core_tx.DepositsCoreTx{}
	statsDClient := statsd.NewNoop()
	_, err := handleDepositsCoreStreamForInterestAgg(ctx, mockRedis, aggregationConfig, txData, statsDClient)

	assert.Nil(t, err)
}

func TestHandleDepositsCoreStreamForInterestAggHandlesInterestPayoutTransactionType(t *testing.T) {
	mockRedis := &redisMock.Client{}
	ctx := context.Background()
	aggregationConfig := config.InterestAggregationConfig{RetentionDay: 7}
	txData := &deposits_core_tx.DepositsCoreTx{
		ValueTimestamp: time.Now().Add(-48 * time.Hour),
		TransactionEnquiries: []deposits_core_tx.TransactionEnquiry{
			{
				TransactionCode: &deposits_core_tx.TransactionCode{
					Type: constants.InterestPayoutTransactionType,
				},
				CommittedPostings: []*deposits_core_tx.Posting{
					{
						Credit:         true,
						AccountID:      "testAccountID",
						AccountAddress: "testAccountAddress",
						Amount:         "10",
					},
				},
			},
			{
				TransactionCode: &deposits_core_tx.TransactionCode{
					Type: constants.InterestPayoutTransactionType,
				},
				CommittedPostings: []*deposits_core_tx.Posting{
					{
						Credit:         true,
						AccountID:      "testAccountID",
						AccountAddress: "testAccountAddress3",
						Amount:         "10",
					},
				},
			},
		},
	}
	mockLock := &redisMock.Lock{}
	mockRedis.On("TryLock", ctx, mock.Anything, mock.Anything).Return(mockLock, nil)
	mockLock.On("Unlock", mock.Anything).Return(nil)

	mockStorageV2DAO := &storage.MockIInterestAggregateV2DAO{}
	mockStorageV2DAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregateV2{{AccountID: "testAccountID", AccountAddress: "testAccountAddress3"}}, data.ErrNoData)
	mockStorageV2DAO.On("SaveBatch", mock.Anything, mock.Anything).Return(nil).Times(2)
	mockStorageV2DAO.On("Update", mock.Anything, mock.Anything).Return(nil).Times(1)
	storage.InterestAggregateV2D = mockStorageV2DAO
	statsDClient := statsd.NewNoop()
	_, err := handleDepositsCoreStreamForInterestAgg(ctx, mockRedis, aggregationConfig, txData, statsDClient)

	assert.Nil(t, err)
	mockRedis.AssertExpectations(t)
}

func TestHandleDepositsCoreStreamForInterestAggHandlesInterestPayoutTransactionTypeExistRecord(t *testing.T) {
	mockRedis := &redisMock.Client{}
	ctx := context.Background()
	aggregationConfig := config.InterestAggregationConfig{RetentionDay: 7}
	txData := &deposits_core_tx.DepositsCoreTx{
		TransactionEnquiries: []deposits_core_tx.TransactionEnquiry{
			{
				TransactionCode: &deposits_core_tx.TransactionCode{
					Type: constants.InterestPayoutTransactionType,
				},
				CommittedPostings: []*deposits_core_tx.Posting{
					{
						Credit:         true,
						AccountID:      "testAccountID",
						AccountAddress: "testAccountAddress",
						Amount:         "10",
					},
				},
			},
		},
	}
	mockLock := &redisMock.Lock{}
	mockRedis.On("TryLock", ctx, "interest_aggregate_testAccountID_testAccountAddress", 2*time.Second).Return(mockLock, nil)
	mockLock.On("Unlock", mock.Anything).Return(nil).Once()

	mockStorageV2DAO := &storage.MockIInterestAggregateV2DAO{}
	mockStorageV2DAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InterestAggregateV2{{AccountID: "testAccountID", AccountAddress: "testAccountAddress"}}, data.ErrNoData)
	mockStorageV2DAO.On("Update", mock.Anything, mock.Anything).Return(nil)
	storage.InterestAggregateV2D = mockStorageV2DAO
	statsDClient := statsd.NewNoop()
	_, err := handleDepositsCoreStreamForInterestAgg(ctx, mockRedis, aggregationConfig, txData, statsDClient)

	assert.Nil(t, err)
	mockRedis.AssertExpectations(t)
}

func Test_calculateDelta(t *testing.T) {
	location, _ := time.LoadLocation("Asia/Singapore")
	type args struct {
		ctx               context.Context
		interestAggRecord *storage.InterestAggregateV2
		eventTime         time.Time
		startTime         time.Time
		endTime           time.Time
		location          *time.Location
		amount            int64
		transactionType   string
		transactionID     string
	}
	tests := []struct {
		name string
		args args
		want *storage.InterestAggregateV2
	}{
		{
			name: "prev month data 1",
			args: args{
				ctx: context.Background(),
				interestAggRecord: &storage.InterestAggregateV2{
					RecentDay:           map[string]map[string]storage.RecentDayData{},
					MonthlyHistory:      map[string]map[string]int64{},
					TotalInterestEarned: map[string]int64{},
				},
				eventTime:       time.Date(2024, 5, 31, 12, 00, 00, 00, location),
				startTime:       time.Date(2024, 5, 25, 12, 00, 00, 00, location),
				endTime:         time.Date(2024, 6, 10, 12, 00, 00, 00, location),
				location:        location,
				amount:          10,
				transactionType: constants.InterestPayoutTransactionType,
				transactionID:   "11",
			},
			want: &storage.InterestAggregateV2{
				RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-05-31|11": storage.RecentDayData{Value: 10, Time: 1717128000000000000}}},
				MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-05": 10}},
				TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 10},
			},
		},
		{
			name: "prev month data 2",
			args: args{
				ctx: context.Background(),
				interestAggRecord: &storage.InterestAggregateV2{
					RecentDay:           map[string]map[string]storage.RecentDayData{},
					MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-05": 10}},
					TotalInterestEarned: map[string]int64{},
				},
				eventTime:       time.Date(2024, 5, 31, 12, 00, 00, 00, location),
				startTime:       time.Date(2024, 5, 25, 12, 00, 00, 00, location),
				endTime:         time.Date(2024, 6, 10, 12, 00, 00, 00, location),
				location:        location,
				amount:          10,
				transactionType: constants.InterestPayoutTransactionType,
				transactionID:   "11",
			},
			want: &storage.InterestAggregateV2{
				RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-05-31|11": storage.RecentDayData{Value: 10, Time: 1717128000000000000}}},
				MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-05": 20}},
				TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 10},
			},
		},
		{
			name: "current month data 1",
			args: args{
				ctx: context.Background(),
				interestAggRecord: &storage.InterestAggregateV2{
					RecentDay:           map[string]map[string]storage.RecentDayData{},
					MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-05": 10}},
					TotalInterestEarned: map[string]int64{},
				},
				eventTime:       time.Date(2024, 6, 10, 0, 00, 00, 00, location),
				startTime:       time.Date(2024, 5, 25, 12, 00, 00, 00, location),
				endTime:         time.Date(2024, 6, 10, 12, 00, 00, 00, location),
				location:        location,
				amount:          10,
				transactionType: constants.InterestPayoutTransactionType,
				transactionID:   "11",
			},
			want: &storage.InterestAggregateV2{
				RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-06-10|11": storage.RecentDayData{Value: 10, Time: 1717948800000000000}}},
				MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-05": 10, "2024-06": 10}},
				TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 10},
			},
		},
		{
			name: "current month data 1 2",
			args: args{
				ctx: context.Background(),
				interestAggRecord: &storage.InterestAggregateV2{
					RecentDay:           map[string]map[string]storage.RecentDayData{},
					MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-05": 10}},
					TotalInterestEarned: map[string]int64{},
				},
				eventTime:       time.Date(2024, 6, 8, 0, 00, 00, 00, location),
				startTime:       time.Date(2024, 5, 25, 12, 00, 00, 00, location),
				endTime:         time.Date(2024, 6, 10, 12, 00, 00, 00, location),
				location:        location,
				amount:          10,
				transactionType: constants.InterestPayoutTransactionType,
				transactionID:   "11",
			},
			want: &storage.InterestAggregateV2{
				RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-06-08|11": storage.RecentDayData{Value: 10, Time: 1717776000000000000}}},
				MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-05": 10, "2024-06": 10}},
				TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 10},
			},
		},
		{
			name: "turn of month",
			args: args{
				ctx: context.Background(),
				interestAggRecord: &storage.InterestAggregateV2{
					RecentDay:           map[string]map[string]storage.RecentDayData{},
					MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-04": 10}},
					TotalInterestEarned: map[string]int64{},
				},
				eventTime:       time.Date(2024, 6, 8, 0, 00, 00, 00, location),
				startTime:       time.Date(2024, 5, 25, 12, 00, 00, 00, location),
				endTime:         time.Date(2024, 6, 10, 12, 00, 00, 00, location),
				location:        location,
				amount:          10,
				transactionType: constants.InterestPayoutTransactionType,
				transactionID:   "11",
			},
			want: &storage.InterestAggregateV2{
				RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-06-08|11": storage.RecentDayData{Value: 10, Time: 1717776000000000000}}},
				MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-04": 10, "2024-06": 10}},
				TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 10},
			},
		},
		{
			name: "not turn of month",
			args: args{
				ctx: context.Background(),
				interestAggRecord: &storage.InterestAggregateV2{
					RecentDay:           map[string]map[string]storage.RecentDayData{},
					MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-04": 10, "2024-05": 0}},
					TotalInterestEarned: map[string]int64{},
				},
				eventTime:       time.Date(2024, 6, 8, 0, 00, 00, 00, location),
				startTime:       time.Date(2024, 5, 25, 12, 00, 00, 00, location),
				endTime:         time.Date(2024, 6, 10, 12, 00, 00, 00, location),
				location:        location,
				amount:          10,
				transactionType: constants.InterestPayoutTransactionType,
				transactionID:   "11",
			},
			want: &storage.InterestAggregateV2{
				RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-06-08|11": storage.RecentDayData{Value: 10, Time: 1717776000000000000}}},
				MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-04": 10, "2024-05": 0, "2024-06": 10}},
				TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 10},
			},
		},
		{
			name: "same day multiple record",
			args: args{
				ctx: context.Background(),
				interestAggRecord: &storage.InterestAggregateV2{
					RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-06-08|00": storage.RecentDayData{Value: 10, Time: 1717776000000000000}, "2024-06-08|01": storage.RecentDayData{Value: 10, Time: 1717776000000000000}}},
					MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-04": 10, "2024-05": 0, "2024-06": 20}},
					TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 20},
				},
				eventTime:       time.Date(2024, 6, 8, 0, 00, 00, 00, location),
				startTime:       time.Date(2024, 5, 25, 12, 00, 00, 00, location),
				endTime:         time.Date(2024, 6, 10, 12, 00, 00, 00, location),
				location:        location,
				amount:          10,
				transactionType: constants.InterestPayoutTransactionType,
				transactionID:   "11",
			},
			want: &storage.InterestAggregateV2{
				RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-06-08|00": storage.RecentDayData{Value: 10, Time: 1717776000000000000}, "2024-06-08|01": storage.RecentDayData{Value: 10, Time: 1717776000000000000}, "2024-06-08|11": storage.RecentDayData{Value: 10, Time: 1717776000000000000}}},
				MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-04": 10, "2024-05": 0, "2024-06": 30}},
				TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 30},
			},
		},
		{
			name: "same day duplicate record overwrite",
			args: args{
				ctx: context.Background(),
				interestAggRecord: &storage.InterestAggregateV2{
					RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-06-08": storage.RecentDayData{Value: 10, Time: 1717776000000000000}, "2024-06-08|01": storage.RecentDayData{Value: 10, Time: 1717776000000000000}}},
					MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-04": 10, "2024-05": 0, "2024-06": 20}},
					TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 20},
				},
				eventTime:       time.Date(2024, 6, 8, 1, 00, 00, 00, location),
				startTime:       time.Date(2024, 5, 25, 12, 00, 00, 00, location),
				endTime:         time.Date(2024, 6, 10, 12, 00, 00, 00, location),
				location:        location,
				amount:          11,
				transactionType: constants.InterestPayoutTransactionType,
				transactionID:   "01",
			},
			want: &storage.InterestAggregateV2{
				RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-06-08": storage.RecentDayData{Value: 10, Time: 1717776000000000000}, "2024-06-08|01": storage.RecentDayData{Value: 11, Time: 1717779600000000000}}},
				MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-04": 10, "2024-05": 0, "2024-06": 21}},
				TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 21},
			},
		},
		{
			name: "same day duplicate record skip",
			args: args{
				ctx: context.Background(),
				interestAggRecord: &storage.InterestAggregateV2{
					RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-06-08": storage.RecentDayData{Value: 10, Time: 1717776000000000000}, "2024-06-08|01": storage.RecentDayData{Value: 11, Time: 1717779600000000000}}},
					MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-04": 10, "2024-05": 0, "2024-06": 21}},
					TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 21},
				},
				eventTime:       time.Date(2024, 6, 8, 0, 00, 00, 00, location),
				startTime:       time.Date(2024, 5, 25, 12, 00, 00, 00, location),
				endTime:         time.Date(2024, 6, 10, 12, 00, 00, 00, location),
				location:        location,
				amount:          10,
				transactionType: constants.InterestPayoutTransactionType,
				transactionID:   "01",
			},
			want: &storage.InterestAggregateV2{
				RecentDay:           map[string]map[string]storage.RecentDayData{constants.InterestPayoutTransactionType: {"2024-06-08": storage.RecentDayData{Value: 10, Time: 1717776000000000000}, "2024-06-08|01": storage.RecentDayData{Value: 11, Time: 1717779600000000000}}},
				MonthlyHistory:      map[string]map[string]int64{constants.InterestPayoutTransactionType: {"2024-04": 10, "2024-05": 0, "2024-06": 21}},
				TotalInterestEarned: map[string]int64{constants.InterestPayoutTransactionType: 21},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			calculateDelta(tt.args.ctx, tt.args.interestAggRecord, tt.args.eventTime, tt.args.startTime, tt.args.endTime, tt.args.location, tt.args.amount, tt.args.transactionType, tt.args.transactionID)
			assert.Equalf(t, tt.want, tt.args.interestAggRecord, "calculateDelta(%v, %v, %v, %v, %v, %v, %v, %v, %v)", tt.args.ctx, tt.args.interestAggRecord, tt.args.eventTime, tt.args.startTime, tt.args.endTime, tt.args.location, tt.args.amount, tt.args.transactionType, tt.args.transactionID)
		})
	}
}

// Package deposits provides method to process deposits core kafka messages
package deposits

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/db/mysql/queries"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_balance_event"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

const (
	defaultMaxRetry      = 10
	defaultSleepDuration = 1
)

// HandleDBStream common method handling deposits-balance message stream
// nolint: gocognit
// TODO: once we switched to retryable stream permanently, we can simplify the message handling
func HandleDBStream(ctx context.Context, message *deposits_balance_event.DepositsBalanceEvent, db storage.Transaction, stats statsd.Client, conf config.TransactionHistoryServiceConfig) error {
	filters := []data.Condition{
		data.EqualTo("AccountID", message.Event.AccountID),
		data.EqualTo("TmPostingInstructionBatchID", message.Event.PostingInstructionBatchID),
		data.EqualTo("AccountPhase", message.Event.Phase),
		data.EqualTo("AccountAddress", message.Event.AccountAddress),
	}
	maxRetry := conf.MaxRetryForBalanceStream
	if maxRetry <= 0 {
		maxRetry = defaultMaxRetry
	}
	sleepDuration := conf.SleepDurationForBalanceStream
	if sleepDuration <= 0 {
		sleepDuration = defaultSleepDuration
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("max_retry", maxRetry), slog.CustomTag("sleep_duration", sleepDuration))
	featFlag := featureflag.FeatureFlagsFromContext(ctx)

	// Search for matching transfer with sleep-retry logic
	for attempt := 1; attempt <= maxRetry+1; attempt++ {
		count := attempt
		retryCount := attempt - 1
		ctx = slog.AddTagsToContext(ctx, slog.CustomTag("retry", retryCount), slog.CustomTag("remaining_retry", maxRetry-retryCount))
		dbResponse, err := storage.TransactionsDataD.Find(ctx, filters...)

		// if not found, sleep and go to next retry
		if err != nil || len(dbResponse) == 0 {
			if featFlag != nil && featFlag.IsRetryableDepositBalanceStreamEnabled() {
				slog.FromContext(ctx).Info(constants.DepositsBalanceStreamLogTag, fmt.Sprintf("matching entry not found for balanceID: %s and will be retried, err:%v", message.Event.BalanceID, err))
				// We put a sleep here to prevent sending retry message too aggressively and hence contain the cpu/mem consumption.
				time.Sleep(time.Duration(sleepDuration) * time.Second)
				return err
			}
			slog.FromContext(ctx).Info(constants.DepositsBalanceStreamLogTag, fmt.Sprintf("matching entry not found in attempt: %v for balanceID: %v, err:%v", count, message.Event.BalanceID, err))
			time.Sleep(time.Duration(sleepDuration) * time.Second)
		} else {
			// iterate over all matching rows
			for _, transactionData := range dbResponse {
				updateErr := updateTransactionBalance(ctx, transactionData, message.Event, db)
				if updateErr != nil {
					slog.FromContext(ctx).Warn(constants.DepositsBalanceStreamLogTag, fmt.Sprintf("failed to update data for balanceID: %v, err:%v", message.Event.BalanceID, updateErr.Error()))
					return updateErr
				}
			}
			return nil
		}
	}
	publishMetricStruct.PublishDepositsCoreBalanceMetrics(message, stats, errors.New("no_matching_balance"))
	return fmt.Errorf("failed to handle balance event with balanceID %v", message.Event.BalanceID)
}

func updateTransactionBalance(ctx context.Context, transactionData *storage.TransactionsData, balance deposits_balance_event.Balance, db storage.Transaction) error {
	slog.FromContext(ctx).Info(constants.DepositsBalanceStreamLogTag, fmt.Sprintf("Update balanceID: %v for transactionID: %v.", balance.BalanceID, transactionData.ID))
	metaData := map[string]string{"tm_balance_id": balance.BalanceID}
	metaDataBytes, _ := json.Marshal(metaData)
	sqlResult, dbErr := db.Exec(
		queries.SetBalanceWithBalanceID,
		metaDataBytes,
		balance.BalanceID,
		balance.Amount,
		time.Now(),
		transactionData.ID,
		fmt.Sprintf("%s%%", balance.BalanceID),
	)
	if dbErr != nil {
		return dbErr
	}
	rowsAffected, dbErr := sqlResult.RowsAffected()
	if dbErr != nil {
		return dbErr
	}
	if rowsAffected == 0 {
		slog.FromContext(ctx).Warn(constants.DepositsBalanceStreamLogTag, fmt.Sprintf("balanceID: %v is not allowed to update for transactionID: %v. Skip it.", balance.BalanceID, transactionData.ID))
	}
	slog.FromContext(ctx).Info(constants.DepositsBalanceStreamLogTag, fmt.Sprintf("Update balanceID: %v for transactionID: %v with %v rowsAffected.", balance.BalanceID, transactionData.ID, rowsAffected))
	return nil
}

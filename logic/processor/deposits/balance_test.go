package deposits

import (
	"context"
	"fmt"
	"testing"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
)

// TestHandleDBStream test cases for HandleDBStream method
func TestHandleDBStream(t *testing.T) {
	data := resources.DepositsBalanceSampleKafkaMessage()
	stats := statsd.NewNoop()

	t.Run("happy-path with invalid metadata", func(t *testing.T) {
		// Mocking TransactionData
		mockResult := &storage.MockResult{}
		mockDB := &storage.MockTransaction{}
		mockResult.On("RowsAffected").Return(int64(1), nil)
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{{Metadata: []byte("null")}}, nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		mockDB.On("Exec", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)

		err := HandleDBStream(context.Background(), data, mockDB, stats, config.TransactionHistoryServiceConfig{})
		assert.Nil(t, err)
	})

	t.Run("happy-path with valid metadata", func(t *testing.T) {
		// Mocking TransactionData
		mockResult := &storage.MockResult{}
		mockDB := &storage.MockTransaction{}
		mockResult.On("RowsAffected").Return(int64(1), nil)
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{{Metadata: []byte("{}")}}, nil)
		storage.TransactionsDataD = mockTransactionDataStorageDAO
		mockDB.On("Exec", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)

		err := HandleDBStream(context.Background(), data, mockDB, stats, config.TransactionHistoryServiceConfig{})
		assert.Nil(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		randomError := fmt.Errorf("failed to update, call")
		// Mocking TransactionData
		mockDB := &storage.MockTransaction{}
		mockTransactionDataStorageDAO := &storage.MockITransactionsDataDAO{}
		mockTransactionDataStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{{}}, nil)
		mockDB.On("Exec", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, randomError)
		storage.TransactionsDataD = mockTransactionDataStorageDAO

		err := HandleDBStream(context.Background(), data, mockDB, stats, config.TransactionHistoryServiceConfig{})
		assert.Error(t, randomError, err)
	})
}

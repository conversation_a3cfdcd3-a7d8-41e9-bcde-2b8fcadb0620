package deposits

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/utils"

	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
	"gitlab.myteksi.net/dakota/transaction-history/internal/metrics"
	"gitlab.myteksi.net/dakota/transaction-history/logic/helper"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"

	"github.com/cenkalti/backoff"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

const lockKey = "interest_aggregate_%s_%s"

func getLockKey(acctID, acctAddr string) string {
	return fmt.Sprintf(lockKey, acctID, acctAddr)
}

// NewExpRetry is the default exponential retry, implementation is not thread safe
func NewExpRetry() backoff.BackOff {
	return backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3)
}

type postingInfo struct {
	posting *deposits_core_tx.Posting
	txnType string
	txnID   string
}

// HandleDepositsCoreStreamForInterestAgg ...
func HandleDepositsCoreStreamForInterestAgg(ctx context.Context, redisClient redis.Client, aggregationConfig config.InterestAggregationConfig, txData *deposits_core_tx.DepositsCoreTx, stats statsd.Client) error {
	featFlag := featureflag.FeatureFlagsFromContext(ctx)
	if featFlag != nil && featFlag.IsRetryableDepositBalanceStreamEnabled() {
		shdRetry, err := handleDepositsCoreStreamForInterestAgg(ctx, redisClient, aggregationConfig, txData, stats)
		if err != nil && shdRetry {
			slog.FromContext(ctx).Info(constants.InterestAggStreamLogTag, fmt.Sprintf("HandleDepositsCoreStreamForInterestAgg failed for ID: %v, err: %v", txData.ID, err.Error()))
			return err
		}
		if err != nil {
			slog.FromContext(ctx).Info(constants.InterestAggStreamLogTag, fmt.Sprintf("failed to process message without retry HandleDepositsCoreStreamForInterestAgg for ID: %v, err: %v", txData.ID, err))
		}
		return nil
	}
	err := backoff.Retry(func() error {
		shdRetry, err := handleDepositsCoreStreamForInterestAgg(ctx, redisClient, aggregationConfig, txData, stats)
		if err != nil && shdRetry {
			return err
		}
		if err != nil {
			slog.FromContext(ctx).Info(constants.InterestAggStreamLogTag, fmt.Sprintf("HandleDepositsCoreStreamForInterestAgg fail unable to retry for ID : %v, err: %v", txData.ID, err))
		}
		return nil
	}, NewExpRetry())
	if err != nil {
		slog.FromContext(ctx).Info(constants.InterestAggStreamLogTag, fmt.Sprintf("HandleDepositsCoreStreamForInterestAgg fail after retry for ID : %v, err: %v", txData.ID, err))
	}
	return err
}

// nolint: funlen,gocognit
func handleDepositsCoreStreamForInterestAgg(ctx context.Context, redisClient redis.Client, aggregationConfig config.InterestAggregationConfig, txData *deposits_core_tx.DepositsCoreTx, stats statsd.Client) (bool, error) {
	location := helper.TimeZoneLocation
	t := time.Now().In(location)
	lastSecOfToday := time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 59, location)
	startTime := lastSecOfToday.AddDate(0, 0, -(aggregationConfig.RetentionDay + 1)).In(location) // exclusive
	eventTime := txData.ValueTimestamp.In(location)
	if helper.GetMonth(eventTime, location) < aggregationConfig.StartMonth { // skip old data
		return false, nil
	}
	endTime := lastSecOfToday.Add(time.Nanosecond) // exclusive

	postings := make([]postingInfo, 0)
	lockKeys := make(map[string]struct{})
	orClause := make([]data.Condition, 0)

	// Iterating over all transaction in batch
	for _, transfer := range txData.TransactionEnquiries {
		if transfer.TransactionCode.Type != constants.InterestPayoutTransactionType &&
			transfer.TransactionCode.Type != constants.InterestPayoutReversalTransactionType {
			continue
		}

		// Iterating over all accountIDs involved in transaction
		for _, posting := range transfer.CommittedPostings {
			if (posting.Credit && (transfer.TransactionCode.Type == constants.InterestPayoutTransactionType)) ||
				(!posting.Credit && transfer.TransactionCode.Type == constants.InterestPayoutReversalTransactionType) {
				if aggregationConfig.ExcludeAccounts[posting.AccountID] { // skip internal accounts
					continue
				}
				postings = append(postings, postingInfo{posting: posting, txnType: transfer.TransactionCode.Type, txnID: txData.BatchID})
				lockKeys[getLockKey(posting.AccountID, posting.AccountAddress)] = struct{}{}
				orClause = append(orClause, data.And(data.EqualTo("AccountID", posting.AccountID),
					data.EqualTo("AccountAddress", posting.AccountAddress)))
			}
		}
	}
	if len(postings) == 0 { // nothing to process
		return false, nil
	}

	// lock resource
	locks := make([]redis.Lock, 0)
	defer func() {
		for _, lock := range locks {
			unlockErr := lock.Unlock(ctx)
			if unlockErr != nil {
				slog.FromContext(ctx).Warn(constants.InterestAggStreamLogTag, "interest aggregation unable to release redis lock", slog.Error(unlockErr))
			}
		}
	}()
	for lk := range lockKeys {
		lock, err := redisClient.TryLock(ctx, lk, 2*time.Duration(len(lockKeys))*time.Second)
		if err != nil {
			if err != redis.ErrLockOccupied {
				slog.FromContext(ctx).Error(constants.InterestAggStreamLogTag, "interest aggregation error trying to obtain redis lock", slog.Error(err))
			} else {
				slog.FromContext(ctx).Warn(constants.InterestAggStreamLogTag, "interest aggregation lock occupied", slog.Error(err))
			}
			return true, err
		}
		locks = append(locks, lock)
	}
	existingRecords, err := storage.InterestAggregateV2D.Find(ctx, data.Or(orClause[0], orClause[1:]...)) // nolint: gosec
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Error(constants.InterestAggStreamLogTag, "failed to Find InterestAggregateV2D", slog.Error(err))
		return true, err
	}
	existingRecordMap := make(map[string]map[string]*storage.InterestAggregateV2)
	for _, r := range existingRecords {
		if existingRecordMap[r.AccountID] == nil {
			existingRecordMap[r.AccountID] = make(map[string]*storage.InterestAggregateV2)
		}
		existingRecordMap[r.AccountID][r.AccountAddress] = r
	}

	// Iterating over all accountIDs involved in transaction
	recordsToUpdate := make([]*storage.InterestAggregateV2, 0)
	recordsToInsert := make([]*storage.InterestAggregateV2, 0)
	recordsTracker := make(map[string]map[string]struct{})
	for _, ptInfo := range postings {
		amount := utils.GetAmountInCents(ctx, ptInfo.posting.Amount)

		recordFound := false
		interestAggV2 := &storage.InterestAggregateV2{}
		if existingRecordMap[ptInfo.posting.AccountID] != nil && existingRecordMap[ptInfo.posting.AccountID][ptInfo.posting.AccountAddress] != nil {
			interestAggV2 = existingRecordMap[ptInfo.posting.AccountID][ptInfo.posting.AccountAddress]
			recordFound = true
		} else {
			interestAggV2.AccountID = ptInfo.posting.AccountID
			interestAggV2.AccountAddress = ptInfo.posting.AccountAddress
			interestAggV2.Currency = ptInfo.posting.Currency
			if existingRecordMap[ptInfo.posting.AccountID] == nil {
				existingRecordMap[ptInfo.posting.AccountID] = make(map[string]*storage.InterestAggregateV2)
			}
			existingRecordMap[ptInfo.posting.AccountID][ptInfo.posting.AccountAddress] = interestAggV2
		}
		if interestAggV2.RecentDay == nil {
			interestAggV2.RecentDay = make(map[string]map[string]storage.RecentDayData)
		}
		if interestAggV2.TotalInterestEarned == nil {
			interestAggV2.TotalInterestEarned = make(map[string]int64)
		}
		if interestAggV2.MonthlyHistory == nil {
			interestAggV2.MonthlyHistory = make(map[string]map[string]int64)
		}

		needUpdate := calculateDelta(ctx, interestAggV2, eventTime, startTime, endTime, location, amount, ptInfo.txnType, ptInfo.txnID)
		if !needUpdate {
			continue
		}

		trackerKey := generateTrackerKey(ptInfo.posting.AccountID, ptInfo.posting.AccountAddress)
		if _, ok := recordsTracker[trackerKey]; ok { // alr in queue for DB operation
			recordsTracker[trackerKey][ptInfo.txnType] = struct{}{}
			continue
		}

		if recordFound {
			recordsToUpdate = append(recordsToUpdate, interestAggV2)
		} else {
			recordsToInsert = append(recordsToInsert, interestAggV2)
		}
		recordsTracker[trackerKey] = map[string]struct{}{ptInfo.txnType: {}}
	}

	if len(recordsToInsert) > 0 {
		err = storage.InterestAggregateV2D.SaveBatch(ctx, recordsToInsert)
		if err != nil {
			slog.FromContext(ctx).Error(constants.InterestAggStreamLogTag, "failed to SaveBatch", slog.Error(err))
			publishMetrics(recordsToInsert, recordsTracker, false, stats)
			return true, err
		}
		publishMetrics(recordsToInsert, recordsTracker, true, stats)
	}

	for _, record := range recordsToUpdate {
		err = storage.InterestAggregateV2D.Update(ctx, record)

		if err != nil {
			slog.FromContext(ctx).Error(constants.InterestAggStreamLogTag, "failed to update", slog.Error(err))
			publishMetrics([]*storage.InterestAggregateV2{record}, recordsTracker, false, stats)
			return true, err
		}
		publishMetrics([]*storage.InterestAggregateV2{record}, recordsTracker, true, stats)
	}
	return false, nil
}

func publishMetrics(records []*storage.InterestAggregateV2, recordsTracker map[string]map[string]struct{}, status bool, stats statsd.Client) {
	for _, record := range records {
		for txnType := range recordsTracker[generateTrackerKey(record.AccountID, record.AccountAddress)] {
			slog.FromContext(context.Background()).Info(metrics.DepositsCoreKafkaMessageForInterestAgg, fmt.Sprintf("interest sent out for user %v %v", txnType, status), slog.CustomTag("accountid", record.AccountID), slog.CustomTag("accountaddress", record.AccountAddress))
			publishMetricStruct.PublishDepositsCoreForInterestAggMetrics(txnType, stats, status)
		}
	}
}

func generateTrackerKey(accountID, accountAddress string) string {
	return fmt.Sprintf("%s|%s", accountID, accountAddress)
}

// nolint: gocognit,funlen
func calculateDelta(ctx context.Context, interestAggRecord *storage.InterestAggregateV2, eventTime, startTime, endTime time.Time, location *time.Location, amount int64, transactionType, transactionID string) bool {
	if !isDayWithinRange(eventTime, startTime, endTime) {
		return false
	}
	dayVal := helper.GetDay(eventTime, location)
	dayKey := dayVal + "|" + transactionID
	eventMonthKey := helper.GetMonth(eventTime, location)
	if _, ok := interestAggRecord.RecentDay[transactionType]; !ok {
		interestAggRecord.RecentDay[transactionType] = make(map[string]storage.RecentDayData)
	}

	if interestAggRecord.RecentDay[transactionType][dayKey].Time > eventTime.UnixNano() {
		return false
	}

	// daily duplicate check
	dayMap := make(map[string]struct{})
	for k := range interestAggRecord.RecentDay[transactionType] {
		tokens := strings.Split(k, "|")
		if len(tokens) == 0 {
			slog.FromContext(ctx).Error(constants.InterestAggStreamLogTag, "[interest aggregation v2] invalid data detected", slog.CustomTag("accountID", interestAggRecord.AccountID), slog.CustomTag("accountAddress", interestAggRecord.AccountAddress))
			continue
		}
		dayMap[tokens[0]] = struct{}{}
	}
	if _, ok := dayMap[dayVal]; ok {
		slog.FromContext(ctx).Error(constants.InterestAggStreamLogTag, "[interest aggregation v2] duplicate interest within same day detected", slog.CustomTag("txnID", transactionID), slog.CustomTag("txnType", transactionType), slog.CustomTag("day", dayVal), slog.CustomTag("accountID", interestAggRecord.AccountID), slog.CustomTag("accountAddress", interestAggRecord.AccountAddress))
	}
	// daily duplicate check

	oldAmount := interestAggRecord.RecentDay[transactionType][dayKey].Value // 0 if it is first record
	interestAggRecord.RecentDay[transactionType][dayKey] = storage.RecentDayData{Value: amount, Time: eventTime.UnixNano()}

	// update total interest earned
	interestAggRecord.TotalInterestEarned[transactionType] += amount - oldAmount

	if _, exist := interestAggRecord.MonthlyHistory[transactionType]; !exist {
		interestAggRecord.MonthlyHistory[transactionType] = make(map[string]int64)
	}
	if _, exist := interestAggRecord.MonthlyHistory[transactionType][eventMonthKey]; !exist {
		interestAggRecord.MonthlyHistory[transactionType][eventMonthKey] = amount
	} else {
		interestAggRecord.MonthlyHistory[transactionType][eventMonthKey] += amount - oldAmount
	}

	//clean up recentDay
	for txType, interestEntries := range interestAggRecord.RecentDay {
		for k := range interestEntries {
			tokens := strings.Split(k, "|")
			if len(tokens) == 0 {
				slog.FromContext(ctx).Error(constants.InterestAggStreamLogTag, "[interest aggregation v2] invalid data detected during cleaning", slog.CustomTag("accountID", interestAggRecord.AccountID), slog.CustomTag("accountAddress", interestAggRecord.AccountAddress))
				continue
			}
			dayTime, err := parseTimeStamp(tokens[0], location)
			if err != nil {
				slog.FromContext(ctx).Error(constants.InterestAggStreamLogTag, "[interest aggregation v2] failed to convert day string to time", slog.CustomTag("key", k), slog.Error(err))
				dayTime = time.Time{}
			}
			if !isDayWithinRange(dayTime, startTime, endTime) {
				delete(interestAggRecord.RecentDay[txType], k)
			}
		}
	}
	return true
}

func parseTimeStamp(dayString string, location *time.Location) (time.Time, error) {
	return time.ParseInLocation(constants.DayFormat, dayString, location)
}

func isDayWithinRange(date, startTime, endTime time.Time) bool {
	return date.Before(endTime) && date.After(startTime)
}

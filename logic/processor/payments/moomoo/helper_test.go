package moomoo

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
)

//nolint:goconst

// Test case for TestHandlePaymentsMooMooTx method
func TestHandlePaymentsMooMooTx(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		// Mocking Tx-History
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineMooMooSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandleMooMooTx(context.Background(), peMessage, transactionCode)
		assert.NoError(t, err)
	})

	t.Run("error-path-validation-failed", func(t *testing.T) {
		// Mocking Tx-History
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineMooMooSampleErrorInput()
		transactionCode := peMessage.TransactionCode
		err := HandleMooMooTx(context.Background(), peMessage, transactionCode)
		assert.NotNil(t, err)
	})

	t.Run("db-error-path-for-senderTx", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		// Error in upsert
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineMooMooSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandleMooMooTx(context.Background(), peMessage, transactionCode)
		assert.EqualError(t, err, errorMessage)
	})

	t.Run("db-error-path-for-receiverTx", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		// Error in upsert
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineMooMooSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandleMooMooTx(context.Background(), peMessage, transactionCode)
		assert.EqualError(t, err, errorMessage)
	})
}

// Test case for TestSendTransferMooMooEntry
func TestSendTransferMooMooEntry(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineMooMooSampleInput()
		transactionCode := peMessage.TransactionCode
		err := sendMooMooEntry(context.Background(), peMessage, transactionCode)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.SpendMoneyTxType)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		errorMessage := "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineMooMooSampleInput()
		transactionCode := peMessage.TransactionCode
		err := sendMooMooEntry(context.Background(), peMessage, transactionCode)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.SpendMoneyTxType)
		assert.EqualError(t, err, errorMessage)
	})
}

// Test case for TestReceiveTransferMooMooEntry
func TestReceiveTransferMooMooEntry(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineMooMooRevSampleInput()
		transactionCode := peMessage.TransactionCode
		err := receiveMooMooEntry(context.Background(), peMessage, transactionCode)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.SpendMoneyRevTxType)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		errorMessage := "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineMooMooRevSampleInput()
		transactionCode := peMessage.TransactionCode
		err := receiveMooMooEntry(context.Background(), peMessage, transactionCode)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.SpendMoneyRevTxType)
		assert.EqualError(t, err, errorMessage)
	})
}

package rentas

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	paymentExperienceMock "gitlab.myteksi.net/dakota/payment/payment-experience/api/mock"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
)

// Test case for HandlePaymentsInterbankTx method
func TestHandlePaymentsInterbankTx(t *testing.T) {
	paymentExp := &paymentExperienceMock.PaymentExperience{}
	paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil)
	t.Run("happy-path", func(t *testing.T) {
		// Mocking Tx-History
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRentasReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsInterbankTx(context.Background(), peMessage, transactionCode, paymentExp)
		assert.NoError(t, err)
	})

	t.Run("error-path-validation-failed", func(t *testing.T) {
		// Mocking Tx-History
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRentasReceiveTransferErrorInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsInterbankTx(context.Background(), peMessage, transactionCode, paymentExp)
		assert.NotNil(t, err)
	})

	t.Run("db-error-path-for-senderTx", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		// Error in upsert
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRentasReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsInterbankTx(context.Background(), peMessage, transactionCode, paymentExp)
		assert.EqualError(t, err, errorMessage)
	})

	t.Run("db-error-path-for-receiverTx", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		// Error in upsert
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRentasReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsInterbankTx(context.Background(), peMessage, transactionCode, paymentExp)
		assert.EqualError(t, err, errorMessage)
	})
}

// Test case for TestReceiveTransferRentasEntry
func TestReceiveTransferRentasEntry(t *testing.T) {
	paymentExp := &paymentExperienceMock.PaymentExperience{}
	paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil)
	t.Run("happy-path", func(t *testing.T) {
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRentasReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := receiveTransferInterbankEntry(context.Background(), peMessage, transactionCode, paymentExp)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.ReceiveMoneyTxType)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineRentasReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := receiveTransferInterbankEntry(context.Background(), peMessage, transactionCode, paymentExp)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.ReceiveMoneyTxType)
		assert.EqualError(t, err, errorMessage)
	})
}

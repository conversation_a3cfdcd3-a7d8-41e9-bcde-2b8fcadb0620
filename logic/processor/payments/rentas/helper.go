package rentas

import (
	"context"
	"errors"
	"fmt"

	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments/common"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// HandlePaymentsInterbankTx ...
func HandlePaymentsInterbankTx(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode, client paymentExperience.PaymentExperience) error {
	// validate the input parameters
	validationErrors := common.ValidationPaymentsIntrabankTx(data)
	if len(validationErrors) != 0 {
		slog.FromContext(ctx).Warn(constants.PaymentRentasTxTag, fmt.Sprintf("Validation failed, err: %v", validationErrors))
		return errors.New("validation failed")
	}
	return createTransferInterbankEntry(ctx, data, txCode, client)
}

func createTransferInterbankEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode, client paymentExperience.PaymentExperience) error {
	switch txCode.Type {
	// Inward RENTAS Transfer
	case constants.ReceiveMoneyTxType:
		return receiveTransferInterbankEntry(ctx, data, txCode, client)
	default:
		return fmt.Errorf("TransactionType Unknown: %s", txCode.Type)
	}
}

//nolint:dupl
func receiveTransferInterbankEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode, client paymentExperience.PaymentExperience) error {
	receiverTx, err := common.CreateReceiverTxEntryDBMY(ctx, data, txCode, client)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentRentasTxTag, fmt.Sprintf("Failed to create Receiver Tx, err: %s", err.Error()))
		return err
	}
	_, err = storage.BulkUpsertTx(ctx, []*storage.PaymentDetail{receiverTx})
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentRentasTxTag, fmt.Sprintf("Failed to upsert transactions, err: %s", err.Error()))
		return err
	}
	return err
}

package common

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/mock"
	paymentExperienceMock "gitlab.myteksi.net/dakota/payment/payment-experience/api/mock"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
)

// Happy test case forValidateTransactionTypeFields method
func TestValidateTransactionTypeFields(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		err := ValidateTransactionTypeFields(resources.PaymentEngineIntrabankTxHappyInput())
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		expectedError := errors.New("transaction code details not in proper format")
		message := &payment_engine_tx.PaymentEngineTx{
			TransactionCode: &deposits_core_tx.TransactionCode{},
		}
		err := ValidateTransactionTypeFields(message)
		assert.EqualError(t, err, expectedError.Error())
	})
}

// Happy test case for ParseProxyObject method
func TestParseProxyObjectHappyPath(t *testing.T) {
	proxyObjectData := dto.ProxyObject{Channel: "PAYNOW", Type: "NRIC"}
	peMessage := resources.PaymentEngineFASTSendTransferSampleInput()
	res := ParseProxyObject(peMessage)
	assert.Equal(t, proxyObjectData, res)
}

// Error test case for ParseProxyObject method when empty ProxyObject is passed
func TestParseProxyObjectEmptyObjectPath(t *testing.T) {
	var proxyObjectData dto.ProxyObject
	peMessage := &payment_engine_tx.PaymentEngineTx{}
	res := ParseProxyObject(peMessage)
	assert.Equal(t, proxyObjectData, res)
}

// Happy test case for PrepareStatusDetail method
func TestPrepareStatusDetailHappyPath(t *testing.T) {
	peMessage := resources.PaymentEngineIntrabankTxHappyInput()
	res, err := PrepareStatusDetail(peMessage)
	assert.NoError(t, err)
	assert.Equal(t, "{\"description\":\"transaction is completed\",\"reason\":\"transaction is completed\"}", string(res))
}

// Happy test case for TestGetBankNameHappyPath method
func TestGetBankNameHappyPath(t *testing.T) {
	paymentExp := &paymentExperienceMock.PaymentExperience{}
	paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil).Once()
	peMessage := resources.PaymentEngineRPPReceiveTransferSampleInput()
	peMessage.TransactionCode = &deposits_core_tx.TransactionCode{Domain: "DEPOSITS", Type: "FUND_IN", SubType: "RPP_NETWORK"}
	res, err := GetBankName(context.Background(), peMessage.DestinationAccount.SwiftCode, paymentExp)
	assert.NoError(t, err)
	assert.Equal(t, "Affin Bank Berhad", res)
}

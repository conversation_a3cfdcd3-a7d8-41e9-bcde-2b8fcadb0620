package common

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	paymentExperienceMock "gitlab.myteksi.net/dakota/payment/payment-experience/api/mock"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
)

func TestSenderEntryAccountsInfoHappyPath(t *testing.T) {
	t.Run("grab-transfer", func(t *testing.T) {
		peMessage := resources.PaymentEngineGrabSendTransferSampleInput()
		accountDetail, counterPartyDetail, err := senderEntryAccountsInfo(context.Background(), peMessage)
		assert.NoError(t, err)
		assert.Equal(t, "{\"pairingID\":\"\",\"number\":\"********\",\"swiftCode\":\"\",\"displayName\":\"testSender\",\"fullName\":\"\",\"proxy_object\":{}}", string(accountDetail))
		assert.Equal(t, "{\"pairingID\":\"\",\"number\":\"********\",\"swiftCode\":\"\",\"displayName\":\"testReceiver\",\"fullName\":\"\",\"proxy_object\":{}}", string(counterPartyDetail))
	})
	t.Run("FAST-Transfer", func(t *testing.T) {
		peMessage := resources.PaymentEngineFASTSendTransferSampleInput()
		accountDetail, counterPartyDetail, err := senderEntryAccountsInfo(context.Background(), peMessage)
		assert.NoError(t, err)
		assert.Equal(t, "{\"pairingID\":\"ABCD123\",\"number\":\"********\",\"swiftCode\":\"483838\",\"displayName\":\"testReceiver\",\"fullName\":\"\",\"proxy_object\":{\"channel\":\"PAYNOW\",\"type\":\"NRIC\"}}", string(counterPartyDetail))
		assert.Equal(t, "{\"pairingID\":\"ABCD123\",\"number\":\"********\",\"swiftCode\":\"483838\",\"displayName\":\"testSender\",\"fullName\":\"\",\"proxy_object\":{}}", string(accountDetail))
	})

	t.Run("RPP-Transfer", func(t *testing.T) {
		peMessage := resources.PaymentEngineRPPSendTransferSampleInput()
		accountDetail, counterPartyDetail, err := senderEntryAccountsInfo(context.Background(), peMessage)
		assert.NoError(t, err)
		assert.Equal(t, "{\"pairingID\":\"ABCD123\",\"number\":\"********\",\"swiftCode\":\"483838\",\"displayName\":\"testReceiver\",\"fullName\":\"\",\"proxy_object\":{\"channel\":\"Duitnow\",\"type\":\"Mobile Number\"}}", string(counterPartyDetail))
		assert.Equal(t, "{\"pairingID\":\"ABCD123\",\"number\":\"********\",\"swiftCode\":\"483838\",\"displayName\":\"testSender\",\"fullName\":\"\",\"proxy_object\":{}}", string(accountDetail))
	})
}

func TestSenderEntryMetaDataInfoHappyPath(t *testing.T) {
	t.Run("Fast_transfer", func(t *testing.T) {
		peMessage := resources.PaymentEngineIntrabankTxHappyInput()
		metaDataBytes, err := senderEntryMetaDataInfo(context.Background(), peMessage)
		assert.NoError(t, err)
		assert.Equal(t, "{\"counterPartyDisplayName\":\"\",\"external_id\":\"\",\"is_ops_transaction\":false,\"purposeCode\":\"OTHR\",\"remarks\":\"Happy Payment\",\"serviceType\":\"\",\"transferType\":\"INTRABANK\"}", string(metaDataBytes))
	})
	t.Run("Grab transfer", func(t *testing.T) {
		peMessage := resources.PaymentEngineGrabSendTransferSampleInput()
		metaDataBytes, err := senderEntryMetaDataInfo(context.Background(), peMessage)
		assert.NoError(t, err)
		assert.Equal(t, "{\"counterPartyDisplayName\":\"\",\"external_id\":\"\",\"grab_activity_id\":\"\",\"grab_activity_type\":\"Grab Ride\",\"is_ops_transaction\":false,\"purposeCode\":\"OTHR\",\"remarks\":\"Happy Payment\",\"serviceType\":\"\",\"transferType\":\"PAYMENT\"}", string(metaDataBytes))
	})
	t.Run("RPP Transfer", func(t *testing.T) {
		paymentExp := &paymentExperienceMock.PaymentExperience{}
		paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil).Once()
		peMessage := resources.PaymentEngineRPPSendTransferSampleInput()
		metaDataBytes, err := senderEntryMetaDataInfoDBMY(context.Background(), peMessage, paymentExp)
		assert.NoError(t, err)
		assert.Equal(t, "{\"cash_account_code\":\"\",\"external_id\":\"\",\"is_ops_transaction\":false,\"payment_description\":\"OTHR\",\"recipient_reference\":\"OTHR\",\"remarks\":\"Happy Payment\",\"serviceType\":\"\",\"transferType\":\"PAYMENT\"}", string(metaDataBytes))
	})
	t.Run("RPP Transfer - Grab", func(t *testing.T) {
		paymentExp := &paymentExperienceMock.PaymentExperience{}
		paymentExp.On("GetExperience", mock.Anything).Return(resources.PaymentEngineBankListRespSample(), nil).Once()
		peMessage := resources.PaymentEngineRPPSendTransferSampleInput()
		peMessage.TransactionCode.SubType = constants.Grab
		metaDataBytes, err := senderEntryMetaDataInfoDBMY(context.Background(), peMessage, paymentExp)
		assert.NoError(t, err)
		assert.Equal(t, "{\"cash_account_code\":\"\",\"external_id\":\"\",\"is_ops_transaction\":false,\"payment_description\":\"OTHR\",\"recipient_reference\":\"OTHR\",\"remarks\":\"Happy Payment\",\"serviceType\":\"\",\"transferType\":\"PAYMENT\"}", string(metaDataBytes))
	})
}

func TestCreateSenderTxEntry(t *testing.T) {
	peMessage := resources.PaymentEngineIntrabankTxHappyInput()
	transactionCode := peMessage.TransactionCode
	_, err := CreateSenderTxEntry(context.Background(), peMessage, transactionCode)
	assert.NoError(t, err)
}

func TestCreateSenderTxEntryDBMY(t *testing.T) {
	paymentExp := &paymentExperienceMock.PaymentExperience{}
	peMessage := resources.PaymentEngineIntrabankTxHappyInput()
	transactionCode := peMessage.TransactionCode
	_, err := CreateSenderTxEntryDBMY(context.Background(), peMessage, transactionCode, paymentExp)
	assert.NoError(t, err)
}

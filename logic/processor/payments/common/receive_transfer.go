package common // nolint:dupl

import (
	"context"
	"encoding/json"
	"fmt"

	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// CreateReceiverTxEntry method will create txHistory entry for the counterParty.
// nolint:dupl
func CreateReceiverTxEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, transactionCode *deposits_core_tx.TransactionCode) (*storage.PaymentDetail, error) {
	accountDetail, counterPartyDetail, err := receiverEntryAccountsInfo(ctx, data)
	if err != nil {
		return nil, err
	}

	statusDetail, err := PrepareStatusDetail(data)
	if err != nil {
		return nil, err
	}

	metaData, err := receiverEntryMetaDataInfo(ctx, data)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ReceiverTxEntryLogTag, fmt.Sprintf("Error to create ReceiverTx, err %s", err.Error()))
		return nil, err
	}

	res := PrepareReceiverTxEntry(data, transactionCode)
	res.Account = accountDetail
	res.CounterPartyAccount = counterPartyDetail
	res.StatusDetails = statusDetail
	res.Metadata = metaData
	return res, nil
}

// CreateReceiverTxEntryDBMY method will create txHistory entry for the counterParty - DBMY.
// nolint:dupl
func CreateReceiverTxEntryDBMY(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, transactionCode *deposits_core_tx.TransactionCode, client paymentExperience.PaymentExperience) (*storage.PaymentDetail, error) {
	accountDetail, counterPartyDetail, err := receiverEntryAccountsInfo(ctx, data)
	if err != nil {
		return nil, err
	}

	statusDetail, err := PrepareStatusDetail(data)
	if err != nil {
		return nil, err
	}

	metaData, err := receiverEntryMetaDataInfoDBMY(ctx, data, client)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ReceiverTxEntryLogTag, fmt.Sprintf("Error to create ReceiverTx, err %s", err.Error()))
		return nil, err
	}

	res := PrepareReceiverTxEntry(data, transactionCode)
	res.Account = accountDetail
	res.CounterPartyAccount = counterPartyDetail
	res.StatusDetails = statusDetail
	res.Metadata = metaData
	return res, nil
}

func receiverEntryAccountsInfo(ctx context.Context, data *payment_engine_tx.PaymentEngineTx) ([]byte, []byte, error) {
	var accountDetailBytes, counterPartyDetailBytes []byte
	var counterPartyDetail, accountDetail dto.AccountDetail
	proxyObject := ParseProxyObject(data)

	if data.DestinationAccount != nil {
		accountDetail = dto.AccountDetail{
			Number:      data.DestinationAccount.Number,
			DisplayName: data.DestinationAccount.DisplayName,
			PairingID:   data.DestinationAccount.PairingID,
			SwiftCode:   data.DestinationAccount.SwiftCode,
			Proxy:       proxyObject,
		}
	}

	if data.SourceAccount != nil {
		counterPartyDetail = dto.AccountDetail{
			Number:      data.SourceAccount.Number,
			DisplayName: data.SourceAccount.DisplayName,
			PairingID:   data.SourceAccount.PairingID,
			SwiftCode:   data.SourceAccount.SwiftCode,
		}
	}

	// Marshal account details
	accountDetailBytes, err := json.Marshal(accountDetail)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ReceiverTxEntryLogTag, fmt.Sprintf("Error to marshal accountDetail, err %s", err.Error()))
		return accountDetailBytes, counterPartyDetailBytes, err
	}
	// Marshal counter-party-account details
	counterPartyDetailBytes, err = json.Marshal(counterPartyDetail)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ReceiverTxEntryLogTag, fmt.Sprintf("Error to marshal counterPartyAccountDetail, err %s", err.Error()))
		return accountDetailBytes, counterPartyDetailBytes, err
	}
	return accountDetailBytes, counterPartyDetailBytes, nil
}

//nolint:dupl
func receiverEntryMetaDataInfo(ctx context.Context, data *payment_engine_tx.PaymentEngineTx) ([]byte, error) {
	var paymentMetadata map[string]string
	metadata := make(map[string]interface{}) // values can be of different types
	metadata["transferType"] = data.Type
	metadata["is_ops_transaction"] = data.IsOpsTransaction

	// Assignment of fields to metadata map
	if data.Properties != nil && data.Properties.FAST != nil {
		metadata["purposeCode"] = data.Properties.FAST.PurposeCode
	}
	metadata["remarks"] = data.Remarks
	metadata["external_id"] = data.ExternalID
	if data.TransactionCode.SubType == constants.Grab && data.Properties != nil {
		if data.Properties.PartnerPayload != nil {
			metadata["grab_activity_id"] = data.Properties.PartnerPayload.ActivityID
			metadata["grab_activity_type"] = data.Properties.PartnerPayload.ActivityType
		}
		if data.Properties.RefundProperties != nil {
			metadata["original_verdict_id"] = data.Properties.RefundProperties.OriginalVerdictID
			metadata["original_transaction_id"] = data.Properties.RefundProperties.OriginalTransactionID
		}
	}
	if HasReversalProperties(data) {
		metadata["original_transaction_id"] = data.Properties.ReversalProperties.OriginalTransactionID
	}
	err := json.Unmarshal(data.Metadata, &paymentMetadata)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ReceiverTxEntryLogTag, fmt.Sprintf("Error to unmarshal paymentEngine metadata, err %s", err.Error()))
	}
	metadata["serviceType"] = paymentMetadata["serviceType"]
	metadata["counterPartyDisplayName"] = paymentMetadata["counterPartyDisplayName"]
	metaDataBytes, err := json.Marshal(metadata)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ReceiverTxEntryLogTag, fmt.Sprintf("Error to marshal metadata, err %s", err.Error()))
		return metaDataBytes, err
	}
	return metaDataBytes, nil
}

//nolint:dupl
func receiverEntryMetaDataInfoDBMY(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, client paymentExperience.PaymentExperience) ([]byte, error) {
	var (
		bankNameErr     error
		paymentMetadata map[string]string
	)
	metadata := make(map[string]interface{}) // values can be of different types
	metadata["transferType"] = data.Type
	metadata["is_ops_transaction"] = data.IsOpsTransaction

	// Assignment of fields to metadata map
	if data.Properties != nil && data.Properties.FAST != nil {
		metadata["purposeCode"] = data.Properties.FAST.PurposeCode
	}
	if HasRPPProperties(data) {
		metadata["recipient_reference"] = data.Properties.RPP.RecipientReference
	}
	metadata["remarks"] = data.Remarks
	metadata["external_id"] = data.ExternalID
	if HasCounterPartyBank(data) {
		metadata["bank_name"], bankNameErr = GetBankName(ctx, data.SourceAccount.SwiftCode, client)
		if bankNameErr != nil {
			slog.FromContext(ctx).Warn(constants.ReceiverTxEntryLogTag, fmt.Sprintf("Unable to get bank name, err: %s", bankNameErr.Error()))
		}
	}
	if HasReversalProperties(data) {
		metadata["original_transaction_id"] = data.Properties.ReversalProperties.OriginalTransactionID
	}
	if data.TransactionCode.SubType == constants.Grab && data.Properties != nil {
		if data.Properties.PartnerPayload != nil {
			metadata["grab_activity_id"] = data.Properties.PartnerPayload.ActivityID
			metadata["grab_activity_type"] = data.Properties.PartnerPayload.ActivityType
		}
		if data.Properties.RefundProperties != nil {
			metadata["original_verdict_id"] = data.Properties.RefundProperties.OriginalVerdictID
			metadata["original_transaction_id"] = data.Properties.RefundProperties.OriginalTransactionID
		}
	}
	err := json.Unmarshal(data.Metadata, &paymentMetadata)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ReceiverTxEntryLogTag, fmt.Sprintf("Error to unmarshal paymentEngine metadata, err %s", err.Error()))
	}
	metadata["serviceType"] = paymentMetadata["serviceType"]
	metaDataBytes, err := json.Marshal(metadata)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ReceiverTxEntryLogTag, fmt.Sprintf("Error to marshal metadata, err %s", err.Error()))
		return metaDataBytes, err
	}
	return metaDataBytes, nil
}

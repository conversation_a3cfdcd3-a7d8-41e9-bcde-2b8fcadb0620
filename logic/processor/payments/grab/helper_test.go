package grab

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
)

//nolint:goconst

// Test case for HandlePaymentsGRABTx method
func TestHandlePaymentsGRABTx(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		// Mocking Tx-History
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineGrabSendTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsGRABTx(context.Background(), peMessage, transactionCode)
		assert.NoError(t, err)
	})

	t.Run("error-path-validation-failed", func(t *testing.T) {
		// Mocking Tx-History
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineFASTSendTransferErrorInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsGRABTx(context.Background(), peMessage, transactionCode)
		assert.NotNil(t, err)
	})

	t.Run("db-error-path-for-senderTx", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		// Error in upsert
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineGrabSendTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsGRABTx(context.Background(), peMessage, transactionCode)
		assert.EqualError(t, err, errorMessage)
	})

	t.Run("db-error-path-for-receiverTx", func(t *testing.T) {
		const errorMessage = "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		// Error in upsert
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineGrabReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsGRABTx(context.Background(), peMessage, transactionCode)
		assert.EqualError(t, err, errorMessage)
	})
}

// Test case for SendTransferFASTEntry
func TestSendTransferFASTEntry(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineGrabSendTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := spendTransferGRABEntry(context.Background(), peMessage, transactionCode)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.SpendMoneyTxType)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		errorMessage := "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineGrabSendTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := spendTransferGRABEntry(context.Background(), peMessage, transactionCode)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.SpendMoneyTxType)
		assert.EqualError(t, err, errorMessage)
	})
}

// Test case for ReceiveTransferFASTEntry
func TestReceiveTransferFASTEntry(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineGrabReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := receiveTransferGRABEntry(context.Background(), peMessage, transactionCode)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.ReceiveMoneyTxType)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		errorMessage := "some error occurred"
		mockStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(errorMessage))

		storage.PaymentDetailD = mockStorageDAO

		peMessage := resources.PaymentEngineGrabReceiveTransferSampleInput()
		transactionCode := peMessage.TransactionCode
		err := receiveTransferGRABEntry(context.Background(), peMessage, transactionCode)
		// to check that this method should only be called if assert holds true
		assert.Equal(t, transactionCode.Type, constants.ReceiveMoneyTxType)
		assert.EqualError(t, err, errorMessage)
	})
}

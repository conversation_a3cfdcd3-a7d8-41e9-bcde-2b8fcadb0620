package intrabank

import (
	"context"
	"errors"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments/common"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// HandlePaymentsIntrabankTx handles INTRABANK channel transactions from payment-engine
func HandlePaymentsIntrabankTx(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, transactionCode *deposits_core_tx.TransactionCode) error {
	// validate the input parameters
	validationErrors := common.ValidationPaymentsIntrabankTx(data)
	if len(validationErrors) != 0 {
		slog.FromContext(ctx).Warn(constants.PaymentsIntrabankTxTag, fmt.Sprintf("Validation failed, err: %v", validationErrors))
		return errors.New("validation failed")
	}
	// create sender transaction entry
	senderTx, err := common.CreateSenderTxEntry(ctx, data, transactionCode)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsIntrabankTxTag, fmt.Sprintf("Failed to create Sender Tx, err: %s", err.Error()))
		return err
	}
	// create receiver transaction entry
	receiverTx, err := common.CreateReceiverTxEntry(ctx, data, transactionCode)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsIntrabankTxTag, fmt.Sprintf("Failed to create Receiver Tx, err: %s", err.Error()))
		return err
	}
	// adding to DB
	_, bulkUpsertErr := storage.BulkUpsertTx(ctx, []*storage.PaymentDetail{senderTx, receiverTx})
	if bulkUpsertErr != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsIntrabankTxTag, fmt.Sprintf("Failed to upsert transactions, err: %s", bulkUpsertErr.Error()))
		return bulkUpsertErr
	}
	return err
}

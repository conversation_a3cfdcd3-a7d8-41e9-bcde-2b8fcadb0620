package intrabank

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
)

func TestHandlePaymentsInternalTx(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		// Mocking Tx-History
		mockTxHistoryStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockTxHistoryStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockTxHistoryStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockTxHistoryStorageDAO

		peMessage := resources.PaymentEngineIntrabankTxHappyInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsIntrabankTx(context.Background(), peMessage, transactionCode)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		// Mocking Tx-History
		mockTxHistoryStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockTxHistoryStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockTxHistoryStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.PaymentDetailD = mockTxHistoryStorageDAO

		peMessage := resources.PaymentEngineIntrabankTxErrorInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsIntrabankTx(context.Background(), peMessage, transactionCode)
		assert.NotNil(t, err)
	})

	t.Run("db-error-path", func(t *testing.T) {
		upsertErrMessage := "some error occurred in upsert operation"

		// Mocking Tx-History
		mockTxHistoryStorageDAO := &storage.MockIPaymentDetailDAO{}
		mockTxHistoryStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PaymentDetail{}, nil)
		mockTxHistoryStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(errors.New(upsertErrMessage))
		storage.PaymentDetailD = mockTxHistoryStorageDAO

		peMessage := resources.PaymentEngineIntrabankTxHappyInput()
		transactionCode := peMessage.TransactionCode
		err := HandlePaymentsIntrabankTx(context.Background(), peMessage, transactionCode)
		assert.EqualError(t, err, upsertErrMessage)
	})
}

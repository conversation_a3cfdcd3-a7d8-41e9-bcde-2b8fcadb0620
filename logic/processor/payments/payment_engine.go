package payments

import (
	"context"
	"fmt"

	paymentExperience "gitlab.myteksi.net/dakota/payment/payment-experience/api"

	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments/rpp"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments/common"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments/fast"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments/grab"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments/intrabank"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments/moomoo"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments/rentas"
	"gitlab.myteksi.net/dakota/transaction-history/utils/metrics"
)

var publishMetricStruct metrics.PublishMetricsImpl = &metrics.PublishMetrics{}

// HandlePEStream common method handling payment-engine message stream
func HandlePEStream(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, stats statsd.Client, client paymentExperience.PaymentExperience) error {
	transactionCode := data.TransactionCode
	err := common.ValidateTransactionTypeFields(data)
	if err != nil {
		return err
	}
	switch transactionCode.SubType {
	case constants.IntraBank:
		err = intrabank.HandlePaymentsIntrabankTx(ctx, data, transactionCode)
	case constants.RPP:
		err = rpp.HandlePaymentsInterbankTx(ctx, data, transactionCode, client)
	case constants.Grab, constants.CollectCustSubType: // handle Grab/Partner transactions
		err = grab.HandlePaymentsGRABTx(ctx, data, transactionCode)
	case constants.FastNetwork:
		err = fast.HandlePaymentsInterbankTx(ctx, data, transactionCode)
	case constants.RtolAJ:
		err = fast.HandlePaymentsInterbankTx(ctx, data, transactionCode)
	case constants.RtolAlto:
		err = fast.HandlePaymentsInterbankTx(ctx, data, transactionCode)
	case constants.MooMoo:
		err = moomoo.HandleMooMooTx(ctx, data, transactionCode)
	case constants.RENTAS:
		err = rentas.HandlePaymentsInterbankTx(ctx, data, transactionCode, client)

	default:
		slog.FromContext(ctx).Warn("HandlePEStream", fmt.Sprintf("No Hanlder for subtype: %s", transactionCode.SubType))
	}

	publishMetricStruct.PublishPaymentEngineMetrics(data, stats, err)
	return err
}

package fast

import (
	"context"
	"errors"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/logic/processor/payments/common"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// HandlePaymentsInterbankTx ...
func HandlePaymentsInterbankTx(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode) error {
	var err error
	// validate the input parameters
	validationErrors := common.ValidationPaymentsIntrabankTx(data)
	if len(validationErrors) != 0 {
		slog.FromContext(ctx).Warn(constants.PaymentsInterbankTxTag, fmt.Sprintf("Validation failed, err: %v", validationErrors))
		return errors.New("validation failed")
	}
	// Outward FAST Transfer
	if txCode.Type == constants.SendMoneyTxType || txCode.Type == constants.ReceiveMoneyRevTxType {
		err = sendTransferInterbankEntry(ctx, data, txCode)
	} else if txCode.Type == constants.ReceiveMoneyTxType || txCode.Type == constants.SendMoneyRevTxType { // Inward FAST Transfer
		err = receiveTransferInterbankEntry(ctx, data, txCode)
	} else {
		err = fmt.Errorf("TransactionType Unknown: %s", txCode.Type)
	}
	return err
}

//nolint:dupl
func sendTransferInterbankEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode) error {
	senderTx, err := common.CreateSenderTxEntry(ctx, data, txCode)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsInterbankTxTag, fmt.Sprintf("Failed to create Sender Tx, err: %s", err.Error()))
		return err
	}
	_, err = storage.BulkUpsertTx(ctx, []*storage.PaymentDetail{senderTx})
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsInterbankTxTag, fmt.Sprintf("Failed to upsert transactions, err: %s", err.Error()))
		return err
	}
	return err
}

//nolint:dupl
func receiveTransferInterbankEntry(ctx context.Context, data *payment_engine_tx.PaymentEngineTx, txCode *deposits_core_tx.TransactionCode) error {
	receiverTx, err := common.CreateReceiverTxEntry(ctx, data, txCode)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsInterbankTxTag, fmt.Sprintf("Failed to create Receiver Tx, err: %s", err.Error()))
		return err
	}
	_, err = storage.BulkUpsertTx(ctx, []*storage.PaymentDetail{receiverTx})
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PaymentsInterbankTxTag, fmt.Sprintf("Failed to upsert transactions, err: %s", err.Error()))
		return err
	}
	return err
}

package lending

import (
	"context"
	"encoding/json"
	errors2 "errors"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/loan_exp_tx"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
)

// HandleLoanExpTxStream is common method handling loan-exp tx message stream
func HandleLoanExpTxStream(ctx context.Context, txnData *loan_exp_tx.LoanExpTx) error {
	// insert/update txn data into loan_detail table
	err := upsertTxnDataInLoanDetailTable(ctx, txnData)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LoanExpTxStreamLogTag, fmt.Sprintf("data upsertion failed, err: %v", err.Error()))
		return err
	}
	return nil
}

func upsertTxnDataInLoanDetailTable(ctx context.Context, txnData *loan_exp_tx.LoanExpTx) error {
	amountInCents := utils.GetAmountInCents(ctx, txnData.Amount)
	accountLevelTransactionData := &storage.LoanDetail{
		LoanTransactionID:    txnData.LoanTransactionID,
		Amount:               amountInCents,
		Currency:             txnData.Currency,
		AccountID:            txnData.AccountID,
		AccountDetail:        formJSONForDBEntry(txnData.AccountDetail),
		PaymentTransactionID: txnData.PaymentTransactionID,
		TransactionDomain:    txnData.TransactionDomain,
		TransactionType:      txnData.TransactionType,
		TransactionSubType:   txnData.TransactionSubType,
		Status:               txnData.Status,
		StatusDetail:         formJSONForDBEntry(txnData.StatusDetail),
		DisbursementDetail:   formJSONForDBEntry(txnData.DisbursementDetail),
		RepaymentDetail:      formJSONForDBEntry(txnData.RepaymentDetail),
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}
	// check if entry for given accountID and transactionID exists in the db already
	filters := []data.Condition{
		data.EqualTo("LoanTransactionID", txnData.LoanTransactionID),
		data.EqualTo("AccountID", txnData.AccountID),
	}
	dbResponse, err := storage.LoanDetailD.Find(ctx, filters...)
	if err != nil && !errors2.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(constants.LoanExpTxStreamLogTag, fmt.Sprintf("Error getting matching transaction, %s", err.Error()))
		return err
	}
	// matching response not found : insert new entry in db and return
	if len(dbResponse) == 0 || errors2.Is(err, data.ErrNoData) {
		err = storage.LoanDetailD.Save(ctx, accountLevelTransactionData)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.LoanExpTxStreamLogTag, fmt.Sprintf("save failed, err:%v", err.Error()))
			return err
		}
		return nil
	}

	// matching response found : update the db with new values
	if dbResponse[0] != nil {
		accountLevelTransactionData.ID = dbResponse[0].ID
		accountLevelTransactionData.CreatedAt = dbResponse[0].CreatedAt
		err = storage.LoanDetailD.Update(ctx, accountLevelTransactionData)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("update failed, err:%v", err.Error()))
			return err
		}
	}
	return nil
}

func formJSONForDBEntry(jsonObject []byte) []byte {
	emptyJSONObject, _ := json.Marshal(map[string]string{})
	if jsonObject == nil {
		return emptyJSONObject
	}
	return jsonObject
}

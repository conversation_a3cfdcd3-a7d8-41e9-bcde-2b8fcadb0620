// Package lending provides methods to process loan core kafka messages
package lending

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_core_tx"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/loan_core_tx"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	im "gitlab.myteksi.net/dakota/transaction-history/internal/metrics"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils/metrics"
)

var publishMetricStruct metrics.PublishMetricsImpl = &metrics.PublishMetrics{}

// HandleLoanCoreTxStream is common method handling loan-core tx message stream
func HandleLoanCoreTxStream(ctx context.Context, data *loan_core_tx.LoanCoreTx, stats statsd.Client) error {
	// creating batch level data
	batchLevelData, err := createBatchLevelEntry(data)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("batchDetails marshal failed, err: %v", err.Error()))
		return err
	}

	receivedTransactionEvent := time.Now()
	slog.FromContext(ctx).Info(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("Posting instruction event for "+
		"ThoughtMachine BatchId : %s and client BatchId : %s",
		batchLevelData.TmPostingInstructionBatchID, batchLevelData.ClientBatchID))
	slog.FromContext(ctx).Info(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("Recieved Transaction Event: %s",
		receivedTransactionEvent))
	slog.FromContext(ctx).Info(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("Batch Value Timestamp: %s",
		data.ValueTimestamp))
	slog.FromContext(ctx).Info(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("Batch Insertion Timestamp: %s",
		data.InsertionTimestamp))

	postingLevelTransactionList, postingLevelErr := createPostingLevelTransactionData(ctx, data, batchLevelData)
	if postingLevelErr != nil {
		slog.FromContext(ctx).Error(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("Posting Level marshal failed, err:%v", postingLevelErr.Error()))
		return postingLevelErr
	}

	if len(postingLevelTransactionList) == 0 {
		slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("posting level transaction"+
			" list is empty, so skipping processing for TM batch ID: %s", batchLevelData.TmTransactionID))
		return nil
	}

	// searching of matching based on uniqueness constraint
	upsertErr := updateOrInsertTransfer(ctx, postingLevelTransactionList, stats)
	if upsertErr != nil {
		slog.FromContext(ctx).Error(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("upsert Error occured, err:%v", upsertErr.Error()))
		return upsertErr
	}
	for _, posting := range postingLevelTransactionList {
		recordAccountCalendarActivity(ctx, posting)
		publishMetricStruct.PublishLoanCoreTxMetrics(posting, stats)
	}
	slog.FromContext(ctx).Info(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("Time between receving and processing"+
		" complete of transaction event: %s", time.Since(receivedTransactionEvent)))
	return nil
}

func createPostingLevelTransactionData(ctx context.Context, data *loan_core_tx.LoanCoreTx, batchLevelData *storage.TransactionsData) ([]*storage.TransactionsData, error) {
	// Iterating over all transaction in batch
	var postingLevelTransactionList []*storage.TransactionsData
	for _, transfer := range data.TransactionEnquiries {
		transactionLevelData, marshalErr := createTransactionLevelEntry(ctx, batchLevelData, transfer)
		if marshalErr != nil {
			return nil, marshalErr
		}
		// Merge Committed & Rejected Postings
		var postings []*deposits_core_tx.Posting
		if transfer.CommittedPostings != nil {
			postings = append(postings, transfer.CommittedPostings...)
		}
		if transfer.RejectedPostings != nil {
			postings = append(postings, transfer.RejectedPostings...)
		}

		// Iterating over all accountIDs involved in transaction
		for _, posting := range postings {
			emptyMetaData, _ := json.Marshal(nil)
			postingLevelTransactionData := *transactionLevelData
			postingLevelTransactionData.AccountID = posting.AccountID
			postingLevelTransactionData.AccountAddress = posting.AccountAddress
			postingLevelTransactionData.AccountAsset = posting.Asset
			postingLevelTransactionData.AccountPhase = posting.Phase
			postingLevelTransactionData.TransactionCurrency = posting.Currency
			postingLevelTransactionData.TransactionAmount = posting.Amount
			postingLevelTransactionData.Metadata = emptyMetaData
			if posting.Credit {
				postingLevelTransactionData.DebitOrCredit = constants.CREDIT
			} else {
				postingLevelTransactionData.DebitOrCredit = constants.DEBIT
			}

			if checkToIgnoreDrawdownMockEntry(&postingLevelTransactionData) {
				return nil, nil
			}
			postingLevelTransactionList = append(postingLevelTransactionList, &postingLevelTransactionData)
		}
	}
	return postingLevelTransactionList, nil
}

// updateOrInsertTransfer searches matching Tx based on TmPostingInstructionBatchID
// and update/insert based on filter response
func updateOrInsertTransfer(ctx context.Context, postingLevelTransactionList []*storage.TransactionsData, stats statsd.Client) error {
	/*filters := []data.Condition{
		data.EqualTo("TmPostingInstructionBatchID", accountLevelTransactionData.TmPostingInstructionBatchID),
		data.EqualTo("TmTransactionID", accountLevelTransactionData.TmTransactionID),
		data.EqualTo("AccountID", accountLevelTransactionData.AccountID),
		data.EqualTo("AccountPhase", accountLevelTransactionData.AccountPhase),
		data.EqualTo("AccountAddress", accountLevelTransactionData.AccountAddress),
	}*/

	filters := []data.Condition{
		data.EqualTo("TmPostingInstructionBatchID", postingLevelTransactionList[0].TmPostingInstructionBatchID),
	}
	dbResponseList, err := storage.TransactionsDataD.Find(ctx, filters...)
	if err != nil && !errors.Is(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("Error getting matching transaction, %s", err.Error()))
		return err
	}
	// Inserting posting event records in Database, and no previous entry found
	if len(dbResponseList) == 0 || errors.Is(err, data.ErrNoData) {
		err = storage.TransactionsDataD.SaveBatch(ctx, postingLevelTransactionList)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf(" batch save failed, err:%v", err.Error()))
			return err
		}
		return nil
	}

	if len(dbResponseList) != 0 {
		slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("Duplicate record comes from "+
			"TM kafka with TM posting batch ID: %s ", postingLevelTransactionList[0].TmPostingInstructionBatchID))
		stats.Count1(string(servicename.TransactionHistory), im.PostingInstructionForLending,
			im.Action+im.Duplicate)
		slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("Kafka record posting list: %v ",
			postingLevelTransactionList))
		slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("DB records list: %v ",
			dbResponseList))

		if len(dbResponseList) != len(postingLevelTransactionList) {
			slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("DB record length: %d "+
				"doesn't match with kafka posting record length: %d for TM batch Id: %s",
				len(dbResponseList), len(postingLevelTransactionList),
				postingLevelTransactionList[0].TmPostingInstructionBatchID))
			stats.Count1(string(servicename.TransactionHistory), im.PostingInstructionForLending,
				im.Action+im.CountMismatch)
		}
		// If update operation is not required, then we should remove this block of code,
		// we have already checked with TM, There will be exactly duplicate records present,
		//so we can simply omit this update operation.
		//For time being, I am keeping it based on config flag, later on, better to remove this code for clean code
		featureFlags := featureflag.FeatureFlagsFromContext(ctx)
		if featureFlags != nil && featureFlags.IsUpdatePostingInstructionBatchForLendingEnabled() {
			updateError := performPostingUpdate(ctx, postingLevelTransactionList, dbResponseList)
			if updateError != nil {
				slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf(" update posting failed, err:%v", err.Error()))
				return updateError
			}
		}
	}
	return nil
}

//nolint:gocognit
func performPostingUpdate(ctx context.Context, postingLevelTransactionList []*storage.TransactionsData, dbTransactionsDataList []*storage.TransactionsData) error {
	var insertPostingLevelTransactionList []*storage.TransactionsData
	var updatePostingLevelTransactionList []*storage.TransactionsData

	for _, kafkaPosting := range postingLevelTransactionList {
		for index, dbPosting := range dbTransactionsDataList {
			if kafkaPosting.TmTransactionID == dbPosting.TmTransactionID &&
				kafkaPosting.AccountID == dbPosting.AccountID &&
				kafkaPosting.AccountPhase == dbPosting.AccountPhase &&
				kafkaPosting.AccountAddress == dbPosting.AccountAddress {
				kafkaPosting.ID = dbPosting.ID
				kafkaPosting.CreatedAt = dbPosting.CreatedAt
				kafkaPosting.BalanceAfterTransaction = dbPosting.BalanceAfterTransaction
				kafkaPosting.Metadata = dbPosting.Metadata
				updatePostingLevelTransactionList = append(updatePostingLevelTransactionList, kafkaPosting)
				break
			}
			if index == len(dbTransactionsDataList)-1 {
				insertPostingLevelTransactionList = append(insertPostingLevelTransactionList, kafkaPosting)
			}
		}
	}

	if len(insertPostingLevelTransactionList) != 0 {
		slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("inserting %v records "+
			"that are missing in DB based on kafka posting", len(insertPostingLevelTransactionList)))
		saveBatchErr := storage.TransactionsDataD.SaveBatch(ctx, insertPostingLevelTransactionList)
		if saveBatchErr != nil {
			slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf(" batch save failed in "+
				"update block, err:%v", saveBatchErr.Error()))
			return saveBatchErr
		}
	}
	if len(updatePostingLevelTransactionList) != 0 {
		for _, updatePosting := range updatePostingLevelTransactionList {
			updateErr := storage.TransactionsDataD.Update(ctx, updatePosting)
			if updateErr != nil {
				slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("update failed, err:%v", updateErr.Error()))
				return updateErr
			}
		}
	}
	return nil
}

// createTransactionLevelEntry create transaction level data
func createTransactionLevelEntry(ctx context.Context, batchLevelData *storage.TransactionsData, transfer deposits_core_tx.TransactionEnquiry) (*storage.TransactionsData, error) {
	// Marshaling violation
	violation, err := json.Marshal(transfer.Violations)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("violation marshal failed, err:%v", err.Error()))
		return nil, err
	}

	// Marshaling transactionDetails
	transactionDetails, err := json.Marshal(transfer.TransactionDetails)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("transactionDetails marshal failed, err:%v", err.Error()))
		return nil, err
	}
	transactionLevelData := batchLevelData
	transactionLevelData.TmTransactionID = transfer.ID
	transactionLevelData.ClientTransactionID = transfer.TransactionID
	transactionLevelData.TmTransactionType = transfer.TransactionType
	transactionLevelData.TransactionDetails = transactionDetails
	transactionLevelData.TransactionViolations = violation
	transactionLevelData.TransactionDomain = transfer.TransactionCode.Domain
	transactionLevelData.TransactionType = transfer.TransactionCode.Type
	transactionLevelData.TransactionSubtype = transfer.TransactionCode.SubType
	transactionLevelData.CreatedAt = time.Now()
	transactionLevelData.UpdatedAt = time.Now()
	return transactionLevelData, nil
}

func createBatchLevelEntry(data *loan_core_tx.LoanCoreTx) (*storage.TransactionsData, error) {
	batchDetails, err := json.Marshal(data.BatchDetails)
	if err != nil {
		return &storage.TransactionsData{}, err
	}
	return &storage.TransactionsData{
		TmPostingInstructionBatchID: data.ID,
		ClientBatchID:               data.BatchID,
		BatchDetails:                batchDetails,
		BatchValueTimestamp:         data.ValueTimestamp,
		BatchInsertionTimestamp:     data.InsertionTimestamp,
		BatchStatus:                 data.Status,
		BatchErrorType:              data.ErrorType,
		BatchErrorMessage:           data.ErrorMessage,
		BatchRemarks:                data.BatchRemarks,
	}, nil
}

func recordAccountCalendarActivity(ctx context.Context, accountLevelTransactionData *storage.TransactionsData) {
	_, err := storage.CheckAndUpdateCalendarActivity(ctx, accountLevelTransactionData)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LoanCoreTxStreamLogTag, fmt.Sprintf("Failed to record calendar activity, err: %s", err.Error()))
	}
}

// For Loan Drawdown, ignore the entry of AUTH and RELEASE as they are mock
// TODO: Remove this check after payments uses hard-settlement, as these value will not come.
func checkToIgnoreDrawdownMockEntry(accountLevelTransactionData *storage.TransactionsData) bool {
	if accountLevelTransactionData.TransactionType == constants.DrawdownTransactionType &&
		(accountLevelTransactionData.TmTransactionType == constants.OutboundAuthorisation || accountLevelTransactionData.TmTransactionType == constants.Release) {
		return true
	}
	return false
}

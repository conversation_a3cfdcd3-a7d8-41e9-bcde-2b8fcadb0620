// Package digicard ...
package digicard

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/digicard_transaction_tx"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
	"gitlab.myteksi.net/dakota/transaction-history/utils/metrics"
)

var publishMetricStruct metrics.PublishMetricsImpl = &metrics.PublishMetrics{}

// HandleDigicardTxStream ...
func HandleDigicardTxStream(ctx context.Context, txnData *digicard_transaction_tx.DigicardTransactionTx, stats statsd.Client) error {
	// upsert into card transaction table
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("cardTransactionID", txnData.TransactionID))
	err := upsertTxnDataInCardTransactionDetailTable(ctx, txnData)
	if err != nil {
		slog.FromContext(ctx).Error(constants.DigicardTxStreamLogTag, fmt.Sprintf("Data upsertion failed, err: %v", err.Error()))
		return err
	}
	publishMetricStruct.PublishDigicardTxnMetrics(txnData, stats)
	return nil
}

// nolint: funlen
func upsertTxnDataInCardTransactionDetailTable(ctx context.Context, txnData *digicard_transaction_tx.DigicardTransactionTx) error {
	account := json.RawMessage("{}")
	if txnData.Account != nil {
		account = txnData.Account
	}

	cardTransaction := &storage.CardTransactionDetail{
		CardTransactionID:             txnData.TransactionID,
		CardID:                        txnData.CardID,
		TransactionDomain:             txnData.TransactionCode.Domain,
		TransactionType:               txnData.TransactionCode.Type,
		TransactionSubType:            txnData.TransactionCode.SubType,
		TransferType:                  txnData.Type,
		Amount:                        txnData.RequestAmount,
		Currency:                      txnData.RequestCurrency,
		OriginalAmount:                txnData.OriginalAmount,
		CaptureOriginalAmountTillDate: txnData.CaptureOriginalAmountTillDate,
		OriginalCurrency:              txnData.OriginalCurrency,
		CaptureAmount:                 txnData.CaptureAmount,
		CaptureAmountTillDate:         txnData.CaptureAmountTillDate,
		AccountID:                     txnData.SourceAccountID,
		MerchantDescription:           txnData.MerchantDescription,
		TailCardNumber:                txnData.LastFourDigits,
		Status:                        txnData.Status,
		Account:                       account,
		StatusDetails:                 prepareStatusDetail(ctx, txnData),
		Metadata:                      txnData.Metadata,
		CreationTimestamp:             txnData.CreationTimestamp,
		ValueTimestamp:                *txnData.ValueTimestamp,
		CreatedAt:                     txnData.CreationTimestamp,
		UpdatedAt:                     txnData.UpdatedAt,
	}

	if utils.SearchStringArray(constants.CreditFundToCardTransactionTypes, txnData.TransactionCode.Type) {
		cardTransaction.AccountID = txnData.DestinationAccountID
	}

	// check if entry for given accountID and transactionID exists in the db already
	filters := []data.Condition{
		data.EqualTo("CardTransactionID", txnData.TransactionID),
		data.EqualTo("AccountID", cardTransaction.AccountID),
	}
	dbResponse, err := storage.CardTransactionDetailD.Find(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.DigicardTxStreamLogTag, fmt.Sprintf("Error getting matching transaction, %s", err.Error()))
		return err
	}
	// matching response not found : insert new entry in db and return
	if len(dbResponse) == 0 || err == data.ErrNoData {
		err = storage.CardTransactionDetailD.Save(ctx, cardTransaction)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.DigicardTxStreamLogTag, fmt.Sprintf("save failed, err:%v", err.Error()))
			return err
		}
		slog.FromContext(ctx).Info(constants.DigicardTxStreamLogTag, "save success")
		return nil
	}

	// matching response found
	if dbResponse[0] != nil {
		cardTransaction.ID = dbResponse[0].ID
		err = storage.CardTransactionDetailD.Update(ctx, cardTransaction)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.DigicardTxStreamLogTag, fmt.Sprintf("update failed, err:%v", err.Error()))
			return err
		}
	}
	slog.FromContext(ctx).Info(constants.DigicardTxStreamLogTag, "update success")
	return nil
}

func prepareStatusDetail(ctx context.Context, data *digicard_transaction_tx.DigicardTransactionTx) []byte {
	statusDetail := map[string]string{
		"description": data.StatusDescription,
	}
	statusDetailBytes, err := json.Marshal(statusDetail)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.DigicardTxStreamLogTag, fmt.Sprintf("failed to marshal statusDetail, err:%v", err.Error()))
	}
	return statusDetailBytes
}

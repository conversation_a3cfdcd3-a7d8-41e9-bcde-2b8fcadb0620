package digicard

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"gitlab.myteksi.net/dakota/servus/v2/statsd"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
)

func TestHandleDigicardTxStream(t *testing.T) {
	mockCardTransactionDetailsStorageDAO := &storage.MockICardTransactionDetailDAO{}
	mockCardTransactionDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
		[]*storage.CardTransactionDetail{}, nil)
	mockCardTransactionDetailsStorageDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
	storage.CardTransactionDetailD = mockCardTransactionDetailsStorageDAO
	stats := statsd.NewNoop()
	t.Run("happy-path-insert", func(t *testing.T) {
		data := resources.DigicardTxnSampleKafkaMessage()
		err := HandleDigicardTxStream(context.Background(), data, stats)
		assert.Nil(t, err)
	})

	mockCardTransactionDetails := &storage.CardTransactionDetail{ID: 1234}
	mockCardTransactionDetailsStorageDAO = &storage.MockICardTransactionDetailDAO{}
	mockCardTransactionDetailsStorageDAO.
		On("Find", mock.Anything, mock.Anything, mock.Anything).
		Return([]*storage.CardTransactionDetail{mockCardTransactionDetails}, nil)
	mockCardTransactionDetailsStorageDAO.On("Update", mock.Anything, mock.Anything).Return(nil)
	storage.CardTransactionDetailD = mockCardTransactionDetailsStorageDAO

	t.Run("happy-path-update", func(t *testing.T) {
		data := resources.DigicardTxnSampleKafkaMessage()
		err := HandleDigicardTxStream(context.Background(), data, stats)
		assert.Nil(t, err)
	})
}

func TestHandleDigicardTxStream_findError(t *testing.T) {
	scenarios := []struct {
		desc        string
		dbRecords   []*storage.CardTransactionDetail
		findDBErr   error
		saveDBErr   error
		updateDBErr error
		expectedErr error
	}{
		{
			desc:        "sad path - find error",
			findDBErr:   errors.New("find unknown error"),
			expectedErr: errors.New("find unknown error"),
		},
		{
			desc:        "sad path - save error",
			saveDBErr:   errors.New("save unknown error"),
			expectedErr: errors.New("save unknown error"),
		},
		{
			desc: "sad path - update error",
			dbRecords: []*storage.CardTransactionDetail{
				{
					CardID: "123",
				},
			},
			updateDBErr: errors.New("update unknown error"),
			expectedErr: errors.New("update unknown error"),
		},
	}
	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			stats := statsd.NewNoop()
			mockCardTransactionDetailsStorageDAO := &storage.MockICardTransactionDetailDAO{}
			mockCardTransactionDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
				scenario.dbRecords, scenario.findDBErr)
			mockCardTransactionDetailsStorageDAO.On("Save", mock.Anything, mock.Anything).Return(scenario.saveDBErr)
			mockCardTransactionDetailsStorageDAO.On("Update", mock.Anything, mock.Anything).Return(scenario.updateDBErr)
			storage.CardTransactionDetailD = mockCardTransactionDetailsStorageDAO
			data := resources.DigicardTxnSampleKafkaMessage()
			err := HandleDigicardTxStream(context.Background(), data, stats)
			assert.Equal(t, scenario.expectedErr, err)
		})
	}
}

func TestHandleDigicardTxStream_CreditFundToCardTransaction(t *testing.T) {
	t.Run("happy-path-credit-fund-txn", func(t *testing.T) {
		data := resources.DigicardTxnSampleKafkaMessage()
		data.TransactionCode.Type = constants.MoneySendTransactionType
		statusDetail := map[string]string{
			"description": data.StatusDescription,
		}
		stats := statsd.NewNoop()
		statusDetailBytes, _ := json.Marshal(statusDetail)
		expectedCardTransaction := &storage.CardTransactionDetail{
			CardTransactionID:     data.TransactionID,
			CardID:                data.CardID,
			TransactionDomain:     data.TransactionCode.Domain,
			TransactionType:       data.TransactionCode.Type,
			TransactionSubType:    data.TransactionCode.SubType,
			TransferType:          data.Type,
			Amount:                data.RequestAmount,
			Currency:              data.RequestCurrency,
			OriginalAmount:        data.OriginalAmount,
			OriginalCurrency:      data.OriginalCurrency,
			CaptureAmount:         data.CaptureAmount,
			CaptureAmountTillDate: data.CaptureAmountTillDate,
			AccountID:             data.DestinationAccountID,
			MerchantDescription:   data.MerchantDescription,
			Account:               json.RawMessage("{}"),
			TailCardNumber:        data.LastFourDigits,
			Status:                data.Status,
			StatusDetails:         statusDetailBytes,
			Metadata:              data.Metadata,
			CreationTimestamp:     data.CreationTimestamp,
			ValueTimestamp:        *data.ValueTimestamp,
			CreatedAt:             data.CreationTimestamp,
			UpdatedAt:             data.UpdatedAt,
		}
		mockCardTransactionDetailsStorageDAO := &storage.MockICardTransactionDetailDAO{}
		mockCardTransactionDetailsStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
			[]*storage.CardTransactionDetail{}, nil)
		mockCardTransactionDetailsStorageDAO.On("Save", mock.Anything, expectedCardTransaction).Return(nil)
		storage.CardTransactionDetailD = mockCardTransactionDetailsStorageDAO
		err := HandleDigicardTxStream(context.Background(), data, stats)
		assert.Nil(t, err)
		mockCardTransactionDetailsStorageDAO.AssertExpectations(t)
	})
}

//go:build local

package workerlogic

import (
	"os"
	"sync"
	"time"

	godata "gitlab.myteksi.net/gophers/go/commons/data"
	"gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker"
	"gitlab.myteksi.net/gophers/go/commons/util/time/grabtime"
	"gitlab.myteksi.net/gophers/go/staples/statsd/statsdapi"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

var transDataDAO storage.ITransactionsDataDAO

func testDB() bool {
	return os.Getenv("TEST_DB") == "true"
}

func init() {
	if testDB() {
		gosqlConfig := godata.MysqlMasterSlave{
			Master: &godata.MysqlConfig{
				Dsn:             "root:@tcp(localhost:3306)/transaction_history_test?parseTime=true&loc=UTC",
				MaxIdle:         2,
				MaxOpen:         10,
				ConnMaxLifetime: grabtime.Duration{Duration: 1 * time.Minute},
				ConnectOnce:     sync.Once{},
			},
			Slave: &godata.MysqlConfig{
				Dsn:             "root:@tcp(localhost:3306)/transaction_history_test?parseTime=true&loc=UTC",
				MaxIdle:         2,
				MaxOpen:         10,
				ConnMaxLifetime: grabtime.Duration{Duration: 1 * time.Minute},
				ConnectOnce:     sync.Once{},
			},
		}
		sqlConfig := &data.MysqlConfig{
			MasterCB: data.CBSetting{
				Setting: circuitbreaker.Setting{
					Timeout: 10000,
				},
			},
			SlaveCB: data.CBSetting{
				Setting: circuitbreaker.Setting{
					Timeout: 10000,
				},
			},
		}
		sqlConfig.MysqlMasterSlaveConfig = gosqlConfig
		dbConfig := &servus.DataConfig{
			MySQL: sqlConfig,
		}
		transDataDAO = storage.NewTransactionsDataDAO(dbConfig, statsdapi.NewNoop())
	}
}

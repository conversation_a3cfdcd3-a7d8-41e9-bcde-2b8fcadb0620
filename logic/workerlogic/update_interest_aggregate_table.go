// Package workerlogic holds methods related to workers
package workerlogic

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/internal/metrics"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"

	yslog "gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"

	"github.com/Rhymond/go-money"
	"gitlab.myteksi.net/dakota/flow"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
)

// UpdateInterestAggregateTableForDailyInterestPayout ...
func UpdateInterestAggregateTableForDailyInterestPayout(ctx context.Context, startTime, endTime time.Time, conf *config.AppConfig) error {
	pipe := (&aggregateInterestExecutor{
		startTime:                         startTime,
		endTime:                           endTime,
		conf:                              conf,
		accountInterestMap:                make(map[string]dto.Money),
		batchSizeInRow:                    conf.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.BatchSizeInRow,
		batchCOPInSecond:                  conf.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.BatchCOPInSecond,
		updateStrategy:                    UpdateOnlyStrategy,
		parentBatchSubPaginationInSeconds: conf.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.ParentBatchSubPaginationInSeconds,
		refreshAggregation:                conf.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.RefreshAggregation,
		updateBatchSizeInRow:              conf.WorkerConfig.UpdateInterestAggregateDBForDailyInterestPayout.UpdateBatchSizeInRow,
	}).BackfillDefault()
	steps := flow.Seq(
		pipe.BuildBatchFilters(),
		pipe.GetBatchBoundaryIDs(),
		pipe.AggregateInterestByBatch(),
		pipe.UpdateInterestByStrategy(),
	)
	err := flow.Exec(ctx, steps)
	if err != nil {
		return err
	}
	return nil
}

// LegacyUpdateInterestAggregateTableForDailyInterestPayout ...
// 1. Fetch the Interest Payout Entry for previous data and add it to map for accountID,accountAddress Key
// 2. Fetch the Interest Payout Reversal Entry  for previous data and subtract it to map for accountID,accountAddress Key
// 3. Iterate over all entry and update in interest_aggregate table
// deprecated
func LegacyUpdateInterestAggregateTableForDailyInterestPayout(ctx context.Context, startTime, endTime time.Time) {
	slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("startTime: %s, endTime: %s", startTime, endTime))
	accountIDToInterestEarnedMap := make(map[string]dto.Money)
	accountIDToInterestEarnedMap = CreateInterestPayoutMapForAccountIDAndAddress(ctx, startTime, endTime, accountIDToInterestEarnedMap)
	accountIDToInterestEarnedMap = CreateInterestPayoutRevMapForAccountIDAndAddress(ctx, startTime, endTime, accountIDToInterestEarnedMap)
	workerIDTag := fmt.Sprintf("%s%v", metrics.WorkerIDTag, yslog.TagFromContext(ctx, string(constants.WorkerIDTagKey)).Value())
	// iterate over all entries and update the interest
	// We consider each account processing as a single job
	for compositeKey, moneyDTO := range accountIDToInterestEarnedMap {
		jobCtx := slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.JobIDTagKey), utils.NewUUID()))
		arr := strings.Split(compositeKey, ",")
		err := storage.UpdateInterestEarned(jobCtx, arr[0], arr[1], moneyDTO)
		if err != nil {
			statsD.Count1(metrics.WorkerMetricTag, metrics.DailyInterestAggregateCountMetric, metrics.FailedTag, workerIDTag)
			slog.FromContext(jobCtx).Warn(constants.UpdateInterestAggregateDBLogTag,
				fmt.Sprintf("failed to update the interest earned for accountID:%s, accountAddress: %s, err: %s", arr[0], arr[1], err.Error()))
		} else {
			statsD.Count1(metrics.WorkerMetricTag, metrics.DailyInterestAggregateCountMetric, metrics.SuccessTag, workerIDTag)
			slog.FromContext(jobCtx).Info(constants.UpdateInterestAggregateDBLogTag,
				fmt.Sprintf("Updated the interest earned for accountID:%s, accountAddress: %s", arr[0], arr[1]))
		}
	}
}

// CreateInterestPayoutMapForAccountIDAndAddress ...
func CreateInterestPayoutMapForAccountIDAndAddress(ctx context.Context, startTime, endTime time.Time, accountIDToInterestEarnedMap map[string]dto.Money) map[string]dto.Money {
	filters := []data.Condition{
		data.GreaterThan("BatchInsertionTimestamp", startTime),
		data.LessThanOrEqualTo("BatchInsertionTimestamp", endTime),
		data.EqualTo("TransactionType", constants.InterestPayoutTransactionType),
		data.EqualTo("BatchStatus", constants.BatchStatusAccepted),
	}

	dbData, err := storage.TransactionsDataD.FindOnSlave(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("error in getting interest payout enteries, err: %s", err.Error()))
		return accountIDToInterestEarnedMap
	}

	if len(dbData) == 0 {
		slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, "no matching entry for interest payout")
		return accountIDToInterestEarnedMap
	}

	for _, txData := range dbData {
		if txData.DebitOrCredit == constants.CREDIT {
			key := fmt.Sprintf("%s,%s", txData.AccountID, txData.AccountAddress)
			value, ok := accountIDToInterestEarnedMap[key]
			amountInCents := money.New(utils.GetAmountInCents(ctx, txData.TransactionAmount), txData.TransactionCurrency).Amount()
			// tracking this anomaly which interest is found but its amount is 0
			if amountInCents == 0 {
				slog.FromContext(ctx).Warn(constants.UpdateInterestAggregateDBLogTag,
					fmt.Sprintf("Interest found but amount is 0. AccountID: %s, TxDataID: %s", txData.AccountID, txData.ClientTransactionID))
			}
			if !ok {
				// create a new entry in map
				accountIDToInterestEarnedMap[key] = dto.Money{
					Currency: txData.TransactionCurrency,
					Amount:   amountInCents,
				}
			} else {
				// add to the existing value
				accountIDToInterestEarnedMap[key] = dto.Money{
					Currency: txData.TransactionCurrency,
					Amount:   value.Amount + amountInCents,
				}
			}
		}
	}
	return accountIDToInterestEarnedMap
}

// CreateInterestPayoutRevMapForAccountIDAndAddress ...
func CreateInterestPayoutRevMapForAccountIDAndAddress(ctx context.Context, startTime, endTime time.Time, accountIDToInterestEarnedMap map[string]dto.Money) map[string]dto.Money {
	filters := []data.Condition{
		data.GreaterThan("BatchInsertionTimestamp", startTime),
		data.LessThanOrEqualTo("BatchInsertionTimestamp", endTime),
		data.EqualTo("TransactionType", constants.InterestPayoutReversalTransactionType),
		data.EqualTo("BatchStatus", constants.BatchStatusAccepted),
	}

	dbData, err := storage.TransactionsDataD.FindOnSlave(ctx, filters...)
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("error in getting interest payout reversal enteries, err: %s", err.Error()))
		return accountIDToInterestEarnedMap
	}

	if len(dbData) == 0 {
		slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, "no matching entry for interest payout reversal")
		return accountIDToInterestEarnedMap
	}

	for _, txData := range dbData {
		if txData.DebitOrCredit == constants.DEBIT {
			key := fmt.Sprintf("%s,%s", txData.AccountID, txData.AccountAddress)
			value, ok := accountIDToInterestEarnedMap[key]
			amountInCents := money.New(utils.GetAmountInCents(ctx, txData.TransactionAmount), txData.TransactionCurrency).Amount()
			if !ok {
				// create a new entry in map
				accountIDToInterestEarnedMap[key] = dto.Money{
					Currency: txData.TransactionCurrency,
					Amount:   -1 * amountInCents,
				}
			} else {
				// add to the existing value
				accountIDToInterestEarnedMap[key] = dto.Money{
					Currency: txData.TransactionCurrency,
					Amount:   value.Amount + (-1 * amountInCents),
				}
			}
		}
	}
	return accountIDToInterestEarnedMap
}

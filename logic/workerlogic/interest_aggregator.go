package workerlogic

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	yslog "gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"

	"github.com/Rhymond/go-money"
	"github.com/samber/lo"
	"gitlab.myteksi.net/dakota/flow"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/internal/metrics"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
)

type aggregateInterestExecutor struct {
	startTime    time.Time
	endTime      time.Time
	batchStartID uint64
	batchEndID   uint64
	// batchSizeInRow is the max number of rows to fetch in a single batch
	batchSizeInRow       int
	conf                 *config.AppConfig
	accountInterestMap   map[string]dto.Money
	filters              []data.Condition
	batchCOPInSecond     int
	updateBatchSizeInRow int
	updateStrategy       updateStrategy
	// parentBatchSubPaginationInSeconds is the pagination unit when scanning the parent batch min and max ID
	parentBatchSubPaginationInSeconds int
	// refreshAggregation is the flag to refresh the aggregation, otherwise the value will be added to the existing value
	// instead of replacing it (hence the term refresh)
	refreshAggregation bool
}

type updateStrategy string

const (
	// UpdateOnlyStrategy will only update the interest aggregate table
	UpdateOnlyStrategy updateStrategy = "update"
	// UpsertStrategy will upsert the interest aggregate table
	UpsertStrategy updateStrategy = "upsert"
)

const (
	defaultParentBatchSubPaginationInSeconds = 3600
	defaultBatchSizeInRow                    = 1000
	defaultBatchCOPInSecond                  = 1
	defaultUpdateBatchSizeInRow              = 100
)

var (
	// ErrShortCircuit is used to short circuit the flow which usually is harmless
	ErrShortCircuit = fmt.Errorf("short circuit")
	// ErrExceedMaxIteration is used to indicate that the iteration has exceeded the max limit
	ErrExceedMaxIteration = fmt.Errorf("exceeded max iteration count")
)

func (p *aggregateInterestExecutor) BackfillDefault() *aggregateInterestExecutor {
	if p.parentBatchSubPaginationInSeconds == 0 {
		p.parentBatchSubPaginationInSeconds = defaultParentBatchSubPaginationInSeconds
	}
	if p.batchSizeInRow == 0 {
		p.batchSizeInRow = defaultBatchSizeInRow
	}
	if p.batchCOPInSecond == 0 {
		p.batchCOPInSecond = defaultBatchCOPInSecond
	}
	if p.updateBatchSizeInRow == 0 {
		p.updateBatchSizeInRow = defaultUpdateBatchSizeInRow
	}
	return p
}

// BuildBatchFilters builds the common filters for the parent batch query
func (p *aggregateInterestExecutor) BuildBatchFilters() flow.StepFn {
	return func(ctx context.Context) error {
		if len(p.filters) > 0 {
			return nil
		}
		p.filters = []data.Condition{
			data.GreaterThan("BatchInsertionTimestamp", p.startTime),
			data.LessThanOrEqualTo("BatchInsertionTimestamp", p.endTime),
			data.ContainedIn("TransactionType", constants.InterestPayoutTransactionType, constants.InterestPayoutReversalTransactionType),
			data.EqualTo("BatchStatus", constants.BatchStatusAccepted),
		}
		return nil
	}
}

// GetBatchBoundaryIDs fetches the first and last ID of the parent batch
// It will paginate the records within the batch time frame with a smaller time window and identify the min/max ID within the batch
// nolint:gocognit, funlen
func (p *aggregateInterestExecutor) GetBatchBoundaryIDs() flow.StepFn {
	return func(ctx context.Context) error {
		// we do not expect this ids will grow out of control as on each iteration we only store the min and max ID and there should be limited iteration.
		ids := make([]uint64, 0, 24) // 24 is a good starting point as we assume the common usecase is daily batch with hour pagination
		const query = "select coalesce(min(id), 0), coalesce(max(id), 0) from transactions_data where batch_insertion_timestamp > ? and batch_insertion_timestamp <= ? and transaction_type in (?, ?) and batch_status = ?"

		if p.endTime.Before(p.startTime) {
			return fmt.Errorf("end time %s is before or equal to start time %s", p.endTime, p.startTime)
		}
		const timeFormat = "2006-01-02 15:04:05.000000"
		var (
			endTime        time.Time
			startTime      = p.startTime
			timePagination = time.Duration(defaultParentBatchSubPaginationInSeconds) * time.Second
		)
		if p.parentBatchSubPaginationInSeconds > 0 {
			timePagination = time.Duration(p.parentBatchSubPaginationInSeconds) * time.Second
		}
		// to safeguard infinite loop
		maxIter := 50
		iterCount := 1

		for startTime.Before(p.endTime) {
			if iterCount > maxIter {
				slog.FromContext(ctx).Warn(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("Exceeded max iteration count %d", maxIter))
				return ErrExceedMaxIteration
			}
			var iterFirstID, iterLastID uint64
			// upper boundary check to prevent breaching the desired end time
			endTime = lo.Ternary(startTime.Add(timePagination).After(p.endTime), p.endTime, startTime.Add(timePagination))
			fields := lo.Map([]*uint64{&iterFirstID, &iterLastID}, func(v *uint64, _ int) interface{} {
				return interface{}(v)
			})
			args := lo.Map([]string{
				startTime.Format(timeFormat), endTime.Format(timeFormat), constants.InterestPayoutTransactionType,
				constants.InterestPayoutReversalTransactionType, constants.BatchStatusAccepted},
				func(v string, _ int) any {
					return interface{}(v)
				},
			)
			slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("Fetching interest payout enteries min/max id from %s to %s. iter=%d", startTime, endTime, iterCount))
			err := storage.TransactionsDataD.NativeQueryOneRowOnSlave(ctx, query, fields, args...)

			if err != nil {
				if errors.Is(err, data.ErrNoData) {
					return ErrShortCircuit
				}
				return err
			}
			slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("Fetched interest payout enteries min/max id min=%d max=%d. iter=%d", iterFirstID, iterLastID, iterCount))
			if iterFirstID > 0 {
				ids = append(ids, iterFirstID)
			}
			if iterLastID > 0 {
				ids = append(ids, iterLastID)
			}
			startTime = endTime
			iterCount++
		}

		p.batchStartID = lo.Min(ids)
		p.batchEndID = lo.Max(ids)
		slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("Aggregated batch boundary min/max id min=%d max=%d", p.batchStartID, p.batchEndID))
		if p.batchStartID == 0 && p.batchEndID == 0 {
			return ErrShortCircuit
		}
		return nil
	}
}

// AggregateInterestByBatch ...
// nolint: funlen, gocognit
func (p *aggregateInterestExecutor) AggregateInterestByBatch() flow.StepFn {
	return func(ctx context.Context) error {
		commonFilters := []data.Condition{
			data.ContainedIn("TransactionType", constants.InterestPayoutTransactionType, constants.InterestPayoutReversalTransactionType),
			data.EqualTo("BatchStatus", constants.BatchStatusAccepted),
			data.AscendingOrder("ID"),
			data.Limit(p.batchSizeInRow),
		}
		startID := p.batchStartID
		workerID := yslog.TagFromContext(ctx, string(constants.WorkerIDTagKey)).Value()
		for {
			now := time.Now().UTC()
			// the upper bound is to prevent the db scanning too much rows
			upperBoundID := startID + utils.MustConvertToUint64(p.batchSizeInRow*3)
			slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("Fetching interest payout enteries from ID: %d", startID))
			results, err := storage.TransactionsDataD.FindOnSlave(
				ctx, append(commonFilters, data.GreaterThanOrEqualTo("ID", startID),
					data.LessThan("ID", upperBoundID))...,
			)
			if err != nil && err != data.ErrNoData {
				slog.FromContext(ctx).Warn(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("error in getting interest payout enteries, err: %s", err.Error()))
				return err
			}
			slog.FromContext(ctx).Info(constants.UpdateInterestAggregateDBLogTag, fmt.Sprintf("Fetched %d interest payout enteries within current batch", len(results)))

			for _, txData := range results {
				// since we are filtering by ID with single bound, we need to check if the record is within the time frame window
				if txData.ID > p.batchEndID || txData.BatchInsertionTimestamp.Before(p.startTime) || txData.BatchInsertionTimestamp.After(p.endTime) {
					continue
				}

				// aggregate credit entry for customer account interest posting, internal account will be excluded
				if txData.TransactionType == constants.InterestPayout && txData.DebitOrCredit == constants.CREDIT {
					key := fmt.Sprintf("%s,%s", txData.AccountID, txData.AccountAddress)
					value, ok := p.accountInterestMap[key]
					amountInCents := money.New(utils.GetAmountInCents(ctx, txData.TransactionAmount), txData.TransactionCurrency).Amount()
					// tracking this anomaly which interest is found but its amount is 0
					if amountInCents == 0 {
						slog.FromContext(ctx).Warn(constants.UpdateInterestAggregateDBLogTag,
							fmt.Sprintf("Interest found but amount is 0. AccountID: %s, TxDataID: %s", txData.AccountID, txData.ClientTransactionID))
					}
					if !ok {
						// create a new entry in map
						p.accountInterestMap[key] = dto.Money{
							Currency: txData.TransactionCurrency,
							Amount:   amountInCents,
						}
					} else {
						// add to the existing value
						p.accountInterestMap[key] = dto.Money{
							Currency: txData.TransactionCurrency,
							Amount:   value.Amount + amountInCents,
						}
					}
				}

				// aggregate debit entry for customer account interest reversal, internal account will be excluded
				if txData.TransactionType == constants.InterestPayoutReversalTransactionType && txData.DebitOrCredit == constants.DEBIT {
					key := fmt.Sprintf("%s,%s", txData.AccountID, txData.AccountAddress)
					value, ok := p.accountInterestMap[key]
					amountInCents := money.New(utils.GetAmountInCents(ctx, txData.TransactionAmount), txData.TransactionCurrency).Amount()
					if !ok {
						// create a new entry in map
						p.accountInterestMap[key] = dto.Money{
							Currency: txData.TransactionCurrency,
							Amount:   -1 * amountInCents,
						}
					} else {
						// add to the existing value
						p.accountInterestMap[key] = dto.Money{
							Currency: txData.TransactionCurrency,
							Amount:   value.Amount + (-1 * amountInCents),
						}
					}
				}
			}
			statsD.Duration(
				metrics.WorkerMetricTag, metrics.InterestAggregateOneTimerDurationMetric, now, metrics.CalculateBatchAggregatedInterestOpID,
				fmt.Sprintf("%s%v", metrics.WorkerIDTag, workerID),
			)
			var lastID uint64
			if len(results) == 0 {
				// for empty page, just use the upper bound as the last ID so that we use it to compute next page start ID
				lastID = upperBoundID - 1
			} else {
				lastID = results[len(results)-1].ID
			}

			// We have reached the end of the batch, stop the query
			if lastID >= p.batchEndID {
				break
			}
			startID = lastID + 1
			if p.batchCOPInSecond > 0 {
				time.Sleep(time.Duration(p.batchCOPInSecond) * time.Second)
			}
		}
		return nil
	}
}

// UpdateInterestByStrategy ...
func (p *aggregateInterestExecutor) UpdateInterestByStrategy() flow.StepFn {
	return func(ctx context.Context) error {
		if p.updateStrategy == UpdateOnlyStrategy {
			return p.updateInterest(ctx)
		}
		return p.upsertInterest(ctx)
	}
}

func (p *aggregateInterestExecutor) updateInterest(ctx context.Context) error {
	count := 0
	workerIDTag := fmt.Sprintf("%s%v", metrics.WorkerIDTag, yslog.TagFromContext(ctx, string(constants.WorkerIDTagKey)).Value())
	// iterate over all entries and update the interest
	// We consider each account processing as a single job
	for compositeKey, moneyDTO := range p.accountInterestMap {
		count++
		jobCtx := slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.JobIDTagKey), utils.NewUUID()))
		arr := strings.Split(compositeKey, ",")
		err := storage.UpdateInterestEarned(jobCtx, arr[0], arr[1], moneyDTO)
		if err != nil {
			statsD.Count1(metrics.WorkerMetricTag, metrics.DailyInterestAggregateCountMetric, metrics.FailedTag, workerIDTag)
			slog.FromContext(jobCtx).Warn(constants.UpdateInterestAggregateDBLogTag,
				fmt.Sprintf("failed to update the interest earned for accountID:%s, accountAddress: %s, err: %s", arr[0], arr[1], err.Error()))
		} else {
			statsD.Count1(metrics.WorkerMetricTag, metrics.DailyInterestAggregateCountMetric, metrics.SuccessTag, workerIDTag)
			slog.FromContext(jobCtx).Info(constants.UpdateInterestAggregateDBLogTag,
				fmt.Sprintf("Updated the interest earned for accountID:%s, accountAddress: %s", arr[0], arr[1]))
		}
		if p.updateBatchSizeInRow > 0 && count%p.updateBatchSizeInRow == 0 && p.batchCOPInSecond > 0 {
			time.Sleep(time.Duration(p.batchCOPInSecond) * time.Second)
		}
	}
	return nil
}

// nolint: gocognit
func (p *aggregateInterestExecutor) upsertInterest(ctx context.Context) error {
	i := 0
	totalAccountAddressCount := len(p.accountInterestMap)
	workerID := yslog.TagFromContext(ctx, string(constants.WorkerIDTagKey)).Value()
	for compositeKey, moneyDTO := range p.accountInterestMap {
		now := time.Now()
		i++
		slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag, fmt.Sprintf("Upsert account aggregated interest [%d/%d]", i, totalAccountAddressCount))
		arr := strings.Split(compositeKey, ",")
		accountID, accountAddress, moneyDTO2 := arr[0], arr[1], moneyDTO
		var interestAggregateRecord *storage.InterestAggregate

		filters := []data.Condition{
			data.EqualTo("AccountID", accountID),
			data.EqualTo("AccountAddress", accountAddress),
		}
		result, err := storage.InterestAggregateD.Find(ctx, filters...)

		if err != nil && !errors.Is(err, data.ErrNoData) {
			slog.FromContext(ctx).Warn(constants.PopulateInterestAggregateOneTimerLogTag,
				fmt.Sprintf("failed to find the interest earned for accountID:%s, accountAddress: %s, err: %s", accountID, accountAddress, err.Error()))
		}

		if len(result) == 0 {
			interestAggregateRecord = &storage.InterestAggregate{
				AccountID:           accountID,
				AccountAddress:      accountAddress,
				TotalInterestEarned: moneyDTO2.Amount,
				Currency:            moneyDTO2.Currency,
				CreatedAt:           time.Now(),
				UpdatedAt:           time.Now(),
			}
		} else {
			interestAggregateRecord = result[0]
			interestAggregateRecord.TotalInterestEarned = lo.Ternary(p.refreshAggregation, moneyDTO2.Amount, moneyDTO2.Amount+result[0].TotalInterestEarned)
			interestAggregateRecord.UpdatedAt = time.Now()
		}
		upsertErr := storage.InterestAggregateD.Upsert(ctx, interestAggregateRecord)
		if upsertErr != nil {
			slog.FromContext(ctx).Warn(constants.PopulateInterestAggregateOneTimerLogTag,
				fmt.Sprintf("failed to update the interest earned for accountID:%s, accountAddress: %s, err: %s", accountID, accountAddress, upsertErr.Error()))
			return upsertErr
		}
		slog.FromContext(ctx).Info(constants.PopulateInterestAggregateOneTimerLogTag,
			fmt.Sprintf("Updated the interest earned for accountID=%s, accountAddress=%s, amount=%d", accountID, accountAddress, interestAggregateRecord.TotalInterestEarned))
		workerIDTag := fmt.Sprintf("%s%v", metrics.WorkerIDTag, workerID)
		if err != nil {
			statsD.Count1(
				metrics.WorkerMetricTag, metrics.InterestAggregateOneTimerAccountCountMetric, metrics.UpsertAggregatedInterestOpID,
				workerIDTag, metrics.FailedTag, fmt.Sprintf("%s%s", metrics.StatusReasonTag, err.Error()),
			)
		}
		statsD.Duration(
			metrics.WorkerMetricTag, metrics.InterestAggregateOneTimerDurationMetric, now, metrics.UpsertAggregatedInterestOpID,
			workerIDTag,
		)
		if p.updateBatchSizeInRow > 0 && i%p.updateBatchSizeInRow == 0 && p.batchCOPInSecond > 0 {
			time.Sleep(time.Duration(p.batchCOPInSecond) * time.Second)
		}
	}
	return nil
}

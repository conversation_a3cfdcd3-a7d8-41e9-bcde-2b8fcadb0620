package workerlogic

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
)

func Test_createOrOverrideExistingData(t *testing.T) {
	accountID := "12345"
	accountAddress := "DEFAULT"
	locale := utils.GetLocale()
	money := dto.Money{Amount: 200, Currency: locale.Currency}

	t.Run("happy-path-row-exist", func(t *testing.T) {
		mockDBResponse := []*storage.InterestAggregate{{
			AccountID: accountID, AccountAddress: accountAddress, TotalInterestEarned: 20, Currency: locale.Currency,
		}}

		mockStorageDAO := &storage.MockIInterestAggregateDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.InterestAggregateD = mockStorageDAO

		err := createOrOverrideExistingData(context.Background(), accountID, accountAddress, money)
		assert.NoError(t, err)
	})

	t.Run("happy-path", func(t *testing.T) {
		var mockDBResponse []*storage.InterestAggregate

		mockStorageDAO := &storage.MockIInterestAggregateDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		storage.InterestAggregateD = mockStorageDAO

		err := createOrOverrideExistingData(context.Background(), accountID, accountAddress, money)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		expectedError := errors.New("random error string")
		var mockDBResponse []*storage.InterestAggregate

		mockStorageDAO := &storage.MockIInterestAggregateDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(expectedError)
		storage.InterestAggregateD = mockStorageDAO

		err := createOrOverrideExistingData(context.Background(), accountID, accountAddress, money)
		assert.EqualError(t, err, expectedError.Error())
	})
}

func Test_computeBatchCount(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		endDate := time.Unix(**********, 0).UTC()
		startDate := time.Unix(**********, 0).UTC()
		result := computeBatchCount(endDate, startDate, 10)
		assert.Equal(t, 1, result)
	})
}

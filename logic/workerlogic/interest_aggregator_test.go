package workerlogic

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"testing"
	"time"

	"github.com/Rhymond/go-money"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
)

// nolint: gocognit
func Test_aggregateInterestExecutor_GetBatchBoundaryIDs(t *testing.T) {
	t.Run("should set start and end id", func(t *testing.T) {
		executor := aggregateInterestExecutor{
			startTime:                         time.Date(2023, 1, 1, 1, 0, 0, 0, time.UTC),
			endTime:                           time.Date(2023, 1, 1, 2, 0, 0, 0, time.UTC),
			parentBatchSubPaginationInSeconds: 3600,
		}
		oriDAO := storage.TransactionsDataD
		defer func() {
			storage.TransactionsDataD = oriDAO
		}()
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		storage.TransactionsDataD = mockStorageDAO
		queryParams := lo.RepeatBy(8, func(_ int) interface{} { return mock.Anything })
		mockStorageDAO.On("NativeQueryOneRowOnSlave", queryParams...).Run(func(args mock.Arguments) {
			var ids []*uint64
			for _, id := range args.Get(2).([]interface{}) {
				ids = append(ids, id.(*uint64))
			}
			firstID := ids[0]
			*firstID = 10
			lastID := ids[1]
			*lastID = 20
		}).Return(nil)
		fn := executor.GetBatchBoundaryIDs()
		ctx := context.Background()
		err := fn(ctx)
		require.NoError(t, err)
		assert.Equal(t, uint64(10), executor.batchStartID)
		assert.Equal(t, uint64(20), executor.batchEndID)
	})

	t.Run("should return error when query failed", func(t *testing.T) {
		executor := aggregateInterestExecutor{
			startTime: time.Date(2023, 1, 1, 1, 0, 0, 0, time.UTC),
			endTime:   time.Date(2023, 1, 1, 2, 0, 0, 0, time.UTC),
		}
		oriDAO := storage.TransactionsDataD
		defer func() {
			storage.TransactionsDataD = oriDAO
		}()
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		storage.TransactionsDataD = mockStorageDAO
		err := errors.New("error")
		queryParams := lo.RepeatBy(8, func(_ int) interface{} { return mock.Anything })
		mockStorageDAO.On("NativeQueryOneRowOnSlave", queryParams...).Return(errors.New("error"))
		fn := executor.GetBatchBoundaryIDs()
		ctx := context.Background()
		err2 := fn(ctx)
		require.Error(t, err)
		assert.Equal(t, err, err2)
	})

	t.Run("should return short cicrcuit error when there is no row result", func(t *testing.T) {
		executor := aggregateInterestExecutor{
			startTime: time.Date(2023, 1, 1, 1, 0, 0, 0, time.UTC),
			endTime:   time.Date(2023, 1, 1, 2, 0, 0, 0, time.UTC),
		}
		oriDAO := storage.TransactionsDataD
		defer func() {
			storage.TransactionsDataD = oriDAO
		}()
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		storage.TransactionsDataD = mockStorageDAO
		queryParams := lo.RepeatBy(8, func(_ int) interface{} { return mock.Anything })
		mockStorageDAO.On("NativeQueryOneRowOnSlave", queryParams...).Return(data.ErrNoData)
		fn := executor.GetBatchBoundaryIDs()
		ctx := context.Background()
		err := fn(ctx)
		require.Error(t, err)
		assert.Equal(t, ErrShortCircuit, err)
	})

	t.Run("should set start and end id", func(t *testing.T) {
		if !testDB() {
			t.Logf("skipping db test for %s", t.Name())
			return
		}
		oriDAO := storage.TransactionsDataD
		defer func() {
			storage.TransactionsDataD = oriDAO
		}()
		ctx := context.Background()
		storage.TransactionsDataD = transDataDAO
		txDatas := resources.InterestPayoutTransactionData()
		uidPrefix := "9158EF81-C8B3-4A16-91FC-6AD739B4D85"
		// set unique client batch id
		txDatas = lo.Map(txDatas, func(txData *storage.TransactionsData, i int) *storage.TransactionsData {
			txData.ClientBatchID = uidPrefix + strconv.Itoa(i)
			return txData
		})
		clientBatchIDs := lo.Map(txDatas, func(txData *storage.TransactionsData, _ int) string {
			return txData.ClientBatchID
		})
		// clean up existing data
		existingTxDatas, err := transDataDAO.Find(ctx, data.ContainedIn("ClientBatchID", lo.Map(clientBatchIDs, func(id string, _ int) interface{} {
			return id
		})...))
		if err != nil && !errors.Is(err, data.ErrNoData) {
			require.NoError(t, err)
		}
		ids := lo.Map(existingTxDatas, func(txData *storage.TransactionsData, _ int) uint64 {
			return txData.ID
		})

		for _, id := range ids {
			_ = storage.TransactionsDataD.Delete(ctx, strconv.FormatUint(id, 10))
		}
		// insert test data
		startTime := time.Date(2023, 9, 9, 16, 1, 1, 1, time.UTC)
		endTime := time.Date(2023, 9, 10, 1, 1, 1, 1, time.UTC)
		for i, ele := range txDatas {
			txData := ele
			txData.BatchInsertionTimestamp = startTime.Add(time.Minute * time.Duration(i))
			txData.Metadata = json.RawMessage(`{}`)
			txData.BatchDetails = json.RawMessage(`{}`)
			txData.TransactionDetails = json.RawMessage(`{}`)
			txData.TransactionViolations = json.RawMessage(`{}`)
			dbErr := storage.TransactionsDataD.Save(ctx, txData)
			require.NoError(t, dbErr)
		}

		defer func() {
			for _, ele := range txDatas {
				txData := ele
				_ = storage.TransactionsDataD.Delete(ctx, strconv.FormatUint(txData.ID, 10))
			}
		}()

		// get ids that match the filter
		filterIDs := lo.FilterMap(txDatas, func(txData *storage.TransactionsData, _ int) (uint64, bool) {
			shouldInclude := !txData.BatchInsertionTimestamp.Before(startTime) &&
				txData.BatchInsertionTimestamp != startTime &&
				!txData.BatchInsertionTimestamp.After(endTime) &&
				(txData.TransactionType == constants.InterestPayout || txData.TransactionType == constants.InterestPayoutReversalTransactionType) &&
				txData.BatchStatus == constants.BatchStatusAccepted
			return txData.ID, shouldInclude
		})

		executor := aggregateInterestExecutor{
			startTime: startTime,
			endTime:   endTime,
		}
		fn := executor.GetBatchBoundaryIDs()
		err = fn(ctx)
		require.NoError(t, err)
		assert.Equal(t, lo.Min(filterIDs), executor.batchStartID)
		assert.Equal(t, lo.Max(filterIDs), executor.batchEndID)
	})
}

func Test_aggregateInterestExecutor_AggregateInterestByBatch(t *testing.T) {
	t.Run("should set accountInterestMap", func(t *testing.T) {
		executor := aggregateInterestExecutor{
			accountInterestMap: make(map[string]dto.Money),
			startTime:          time.Date(2023, 1, 1, 1, 1, 1, 1, time.Local),
			endTime:            time.Date(2023, 1, 2, 1, 1, 1, 1, time.Local),
			batchStartID:       1,
			batchEndID:         5,
		}
		oriDAO := storage.TransactionsDataD
		defer func() {
			storage.TransactionsDataD = oriDAO
		}()
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		storage.TransactionsDataD = mockStorageDAO
		accountID := "**********"
		internalAccountID := "interest-payout"
		results := []*storage.TransactionsData{
			{
				ID:                      1,
				TransactionAmount:       "50",
				AccountID:               accountID,
				DebitOrCredit:           constants.DEBIT,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayoutReversalTransactionType,
			},
			{
				ID:                      2,
				TransactionAmount:       "100",
				DebitOrCredit:           constants.CREDIT,
				AccountID:               accountID,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayout,
			},
			{
				ID:                      3,
				TransactionAmount:       "200",
				DebitOrCredit:           constants.CREDIT,
				AccountID:               accountID,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayout,
			},
			{
				ID:                      4,
				TransactionAmount:       "100",
				DebitOrCredit:           constants.DEBIT,
				AccountID:               internalAccountID,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayout,
			},
			{
				ID:                      5,
				TransactionAmount:       "50",
				DebitOrCredit:           constants.CREDIT,
				AccountID:               internalAccountID,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayoutReversalTransactionType,
			},
		}
		mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(results, nil)
		fn := executor.AggregateInterestByBatch()
		ctx := context.Background()
		ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.WorkerIDTagKey), "logtag"))
		err := fn(ctx)
		// (100 + 200 - 50) * 100
		total := dto.Money{
			Currency: "",
			Amount:   25000,
		}
		require.NoError(t, err)
		assert.Equal(t, total.Amount, executor.accountInterestMap[accountID+","+constants.DefaultAccountAddress].Amount)
	})

	t.Run("should exclude amount when timestamp not within range", func(t *testing.T) {
		executor := aggregateInterestExecutor{
			accountInterestMap: make(map[string]dto.Money),
			startTime:          time.Date(2023, 1, 1, 1, 1, 1, 1, time.Local),
			endTime:            time.Date(2023, 1, 2, 1, 1, 1, 1, time.Local),
			batchStartID:       1,
			batchEndID:         3,
		}
		oriDAO := storage.TransactionsDataD
		defer func() {
			storage.TransactionsDataD = oriDAO
		}()
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		storage.TransactionsDataD = mockStorageDAO
		accountID := "**********"
		results := []*storage.TransactionsData{
			{
				ID:                      1,
				TransactionAmount:       "50",
				AccountID:               accountID,
				DebitOrCredit:           constants.CREDIT,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayout,
			},
			{
				ID:                      2,
				TransactionAmount:       "100",
				DebitOrCredit:           constants.CREDIT,
				AccountID:               accountID,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayout,
			},
			{
				ID:                      3,
				TransactionAmount:       "200",
				DebitOrCredit:           constants.CREDIT,
				AccountID:               accountID,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour * 48),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayout,
			},
		}
		mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(results, nil)
		fn := executor.AggregateInterestByBatch()
		ctx := context.Background()
		ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.WorkerIDTagKey), "logtag"))
		err := fn(ctx)
		// (100 + 50) * 100
		amount := money.New(15000, "")
		require.NoError(t, err)
		assert.Equal(t, amount.Amount(), executor.accountInterestMap[accountID+","+constants.DefaultAccountAddress].Amount)
	})

	t.Run("should exclude amount when id not within range", func(t *testing.T) {
		executor := aggregateInterestExecutor{
			accountInterestMap: make(map[string]dto.Money),
			startTime:          time.Date(2023, 1, 1, 1, 1, 1, 1, time.Local),
			endTime:            time.Date(2023, 1, 2, 1, 1, 1, 1, time.Local),
			batchStartID:       1,
			batchEndID:         3,
		}
		oriDAO := storage.TransactionsDataD
		defer func() {
			storage.TransactionsDataD = oriDAO
		}()
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		storage.TransactionsDataD = mockStorageDAO
		accountID := "**********"
		results := []*storage.TransactionsData{
			{
				ID:                      1,
				TransactionAmount:       "50",
				AccountID:               accountID,
				DebitOrCredit:           constants.CREDIT,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayout,
			},
			{
				ID:                      2,
				TransactionAmount:       "100",
				DebitOrCredit:           constants.CREDIT,
				AccountID:               accountID,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayout,
			},
			{
				ID:                      4,
				TransactionAmount:       "200",
				DebitOrCredit:           constants.CREDIT,
				AccountID:               accountID,
				BatchInsertionTimestamp: executor.startTime.Add(time.Hour),
				AccountAddress:          constants.DefaultAccountAddress,
				TransactionType:         constants.InterestPayout,
			},
		}
		mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(results, nil).Once()
		fn := executor.AggregateInterestByBatch()
		ctx := context.Background()
		ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.WorkerIDTagKey), "logtag"))
		err := fn(ctx)
		// (100 + 50) * 100
		amount := money.New(15000, "")
		require.NoError(t, err)
		assert.Equal(t, amount.Amount(), executor.accountInterestMap[accountID+","+constants.DefaultAccountAddress].Amount)
	})

	t.Run("should paginate correctly", func(t *testing.T) {
		if !testDB() {
			t.Logf("skipping db test for %s", t.Name())
			return
		}
		oriDAO := storage.TransactionsDataD
		defer func() {
			storage.TransactionsDataD = oriDAO
		}()
		ctx := context.Background()
		ctx = slog.AddTagsToContext(ctx, slog.CustomTag(string(constants.WorkerIDTagKey), "workerID"))
		storage.TransactionsDataD = transDataDAO
		// insert test data
		startTime := time.Date(2023, 9, 9, 16, 1, 1, 1, time.UTC)
		endTime := time.Date(2023, 9, 20, 1, 1, 1, 1, time.UTC)
		batchSizeInRow := 1

		locale := utils.GetLocale()
		txDatas := []*storage.TransactionsData{{
			ID:                  1,
			BatchStatus:         "ACCEPTED",
			TransactionDomain:   "DEPOSITS",
			TransactionType:     "INTEREST_PAYOUT",
			TransactionSubtype:  "SAVINGS",
			AccountID:           "**********",
			AccountAddress:      "ACCRUED_DEPOSIT_INTEREST",
			DebitOrCredit:       "debit",
			TransactionAmount:   "5.13",
			TransactionCurrency: locale.Currency,
		}, {
			ID:                  2,
			BatchStatus:         "ACCEPTED",
			TransactionDomain:   "DEPOSITS",
			TransactionType:     "INTEREST_PAYOUT",
			TransactionSubtype:  "SAVINGS",
			AccountID:           "**********",
			AccountAddress:      "DEFAULT",
			DebitOrCredit:       "credit",
			TransactionAmount:   "5.13",
			TransactionCurrency: locale.Currency,
		}, {
			ID:                  3,
			BatchStatus:         "ACCEPTED",
			TransactionDomain:   "DEPOSITS",
			TransactionType:     "INTEREST_PAYOUT",
			TransactionSubtype:  "SAVINGS",
			AccountID:           "**********",
			AccountAddress:      "ACCRUED_DEPOSIT_INTEREST_*************",
			DebitOrCredit:       "debit",
			TransactionAmount:   "5.50",
			TransactionCurrency: locale.Currency,
		}, {
			ID:                  4,
			BatchStatus:         "ACCEPTED",
			TransactionDomain:   "DEPOSITS",
			TransactionType:     "INTEREST_PAYOUT",
			TransactionSubtype:  "SAVINGS",
			AccountID:           "**********",
			AccountAddress:      "*************",
			DebitOrCredit:       "credit",
			TransactionAmount:   "5.50",
			TransactionCurrency: locale.Currency,
		}, {
			ID:                  5,
			BatchStatus:         "ACCEPTED",
			TransactionDomain:   "DEPOSITS",
			TransactionType:     "INTEREST_PAYOUT",
			TransactionSubtype:  "SAVINGS",
			AccountID:           "**********",
			AccountAddress:      "ACCRUED_DEPOSIT_INTEREST_*************",
			DebitOrCredit:       "debit",
			TransactionAmount:   "5.50",
			TransactionCurrency: locale.Currency,
		}, {
			ID:                  6,
			BatchStatus:         "ACCEPTED",
			TransactionDomain:   "DEPOSITS",
			TransactionType:     "INTEREST_PAYOUT",
			TransactionSubtype:  "SAVINGS",
			AccountID:           "**********",
			AccountAddress:      "*************",
			DebitOrCredit:       "credit",
			TransactionAmount:   "5.50",
			TransactionCurrency: locale.Currency,
		}}

		uidPrefix := "9158EF81-C8B3-4A16-91FC-6AD739B4D84"
		// set unique client batch id
		txDatas = lo.Map(txDatas, func(txData *storage.TransactionsData, i int) *storage.TransactionsData {
			txData.ClientBatchID = uidPrefix + strconv.Itoa(i)
			return txData
		})
		clientBatchIDs := lo.Map(txDatas, func(txData *storage.TransactionsData, _ int) string {
			return txData.ClientBatchID
		})
		// clean up existing data
		existingTxDatas, err := transDataDAO.Find(ctx, data.ContainedIn("ClientBatchID", lo.Map(clientBatchIDs, func(id string, _ int) interface{} {
			return id
		})...))
		if err != nil && !errors.Is(err, data.ErrNoData) {
			require.NoError(t, err)
		}
		ids := lo.Map(existingTxDatas, func(txData *storage.TransactionsData, _ int) uint64 {
			return txData.ID
		})

		for _, id := range ids {
			_ = storage.TransactionsDataD.Delete(ctx, strconv.FormatUint(id, 10))
		}

		// insert test data
		for i, ele := range txDatas {
			txData := ele
			txData.BatchInsertionTimestamp = startTime.Add(time.Minute * time.Duration(i))
			txData.Metadata = json.RawMessage(`{}`)
			txData.BatchDetails = json.RawMessage(`{}`)
			txData.TransactionDetails = json.RawMessage(`{}`)
			txData.TransactionViolations = json.RawMessage(`{}`)
			dbErr := storage.TransactionsDataD.Save(ctx, txData)
			require.NoError(t, dbErr)
		}

		ids = lo.Map(txDatas, func(txData *storage.TransactionsData, _ int) uint64 {
			return txData.ID
		})

		defer func() {
			for _, ele := range txDatas {
				txData := ele
				_ = storage.TransactionsDataD.Delete(ctx, strconv.FormatUint(txData.ID, 10))
			}
		}()

		executor := aggregateInterestExecutor{
			accountInterestMap: make(map[string]dto.Money),
			startTime:          startTime,
			endTime:            endTime,
			batchStartID:       lo.Min(ids),
			batchEndID:         lo.Max(ids),
			batchSizeInRow:     batchSizeInRow,
		}
		fn := executor.AggregateInterestByBatch()
		err = fn(ctx)
		require.NoError(t, err)
		want := map[string]dto.Money{
			"**********,*************": {Amount: 1100, Currency: "IDR"},
			"**********,DEFAULT":       {Amount: 513, Currency: "IDR"},
		}
		assert.Equal(t, want, executor.accountInterestMap)
		t.Logf("output %+v", executor.accountInterestMap)
	})
}

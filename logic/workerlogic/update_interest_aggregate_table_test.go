package workerlogic

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
)

//nolint:dupl
func Test_createInterestPayoutMapForAccountIDAndAddress(t *testing.T) {
	locale := utils.GetLocale()
	t.Run("happy-path", func(t *testing.T) {
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestPayoutTransactionData(), nil)
		storage.TransactionsDataD = mockStorageDAO

		emptyInterestEarnedMap := make(map[string]dto.Money)
		expectedData := map[string]dto.Money{
			"**********,DEFAULT":       {Amount: 513, Currency: locale.Currency},
			"**********,*************": {Amount: 1100, Currency: locale.Currency},
		}
		data := CreateInterestPayoutMapForAccountIDAndAddress(context.Background(), time.Now(), time.Now(), emptyInterestEarnedMap)
		assert.Equal(t, expectedData, data)
	})

	t.Run("no-matching-db-entry", func(t *testing.T) {
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockStorageDAO

		emptyInterestEarnedMap := make(map[string]dto.Money)
		expectedData := map[string]dto.Money{}
		data := CreateInterestPayoutMapForAccountIDAndAddress(context.Background(), time.Now(), time.Now(), emptyInterestEarnedMap)
		assert.Equal(t, expectedData, data)
	})

	t.Run("error-from-db", func(t *testing.T) {
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New(""))
		storage.TransactionsDataD = mockStorageDAO

		emptyInterestEarnedMap := make(map[string]dto.Money)
		expectedData := map[string]dto.Money{}
		data := CreateInterestPayoutMapForAccountIDAndAddress(context.Background(), time.Now(), time.Now(), emptyInterestEarnedMap)
		assert.Equal(t, expectedData, data)
	})
}

//nolint:dupl
func Test_createInterestPayoutRevMapForAccountIDAndAddress(t *testing.T) {
	locale := utils.GetLocale()
	t.Run("happy-path", func(t *testing.T) {
		emptyInterestEarnedMap := make(map[string]dto.Money)
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(resources.InterestPayoutReversalTransactionData(), nil)
		storage.TransactionsDataD = mockStorageDAO

		expectedData := map[string]dto.Money{
			"**********,DEFAULT":       {Amount: -513, Currency: locale.Currency},
			"**********,*************": {Amount: -1100, Currency: locale.Currency},
		}
		data := CreateInterestPayoutRevMapForAccountIDAndAddress(context.Background(), time.Now(), time.Now(), emptyInterestEarnedMap)
		assert.Equal(t, expectedData, data)
	})

	t.Run("no-matching-db-entry", func(t *testing.T) {
		emptyInterestEarnedMap := make(map[string]dto.Money)
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.TransactionsData{}, nil)
		storage.TransactionsDataD = mockStorageDAO

		expectedData := map[string]dto.Money{}
		data := CreateInterestPayoutRevMapForAccountIDAndAddress(context.Background(), time.Now(), time.Now(), emptyInterestEarnedMap)
		assert.Equal(t, expectedData, data)
	})

	t.Run("error-from-db", func(t *testing.T) {
		emptyInterestEarnedMap := make(map[string]dto.Money)
		mockStorageDAO := &storage.MockITransactionsDataDAO{}
		mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New(""))
		storage.TransactionsDataD = mockStorageDAO

		expectedData := map[string]dto.Money{}
		data := CreateInterestPayoutRevMapForAccountIDAndAddress(context.Background(), time.Now(), time.Now(), emptyInterestEarnedMap)
		assert.Equal(t, expectedData, data)
	})
}

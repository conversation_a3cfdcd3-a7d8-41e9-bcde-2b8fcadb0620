package logic

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
)

func TestGetTransactionAmount(t *testing.T) {
	t.Run("DOMESTIC_ATM_FEE", func(t *testing.T) {
		txData := &storage.TransactionsData{
			ClientBatchID:     "batch-123",
			TransactionAmount: "1.00",
			DebitOrCredit:     "debit",
			TransactionType:   constants.DomesticAtmFeeTransactionType,
		}

		cardTxnDetail := &storage.CardTransactionDetail{
			CardTransactionID: "batch-123",
			TransactionType:   constants.SpendCardAtmTransactionType,
			TransferType:      constants.TransferTypeCharge,
			Amount:            10000,
			Status:            constants.CompletedStatus,
		}

		response := GetTransactionAmount(context.Background(), txData, cardTxnDetail)
		assert.Equal(t, utils.MustConvertToInt64(-100), response)
	})

	t.Run("DOMESTIC_ATM_FEE_WAIVER", func(t *testing.T) {
		txData := &storage.TransactionsData{
			ClientBatchID:     "batch-123",
			TransactionAmount: "1.00",
			DebitOrCredit:     "credit",
			TransactionType:   constants.DomesticAtmFeeWaiverTransactionType,
		}

		cardTxnDetail := &storage.CardTransactionDetail{
			CardTransactionID: "batch-123",
			TransactionType:   constants.SpendCardAtmTransactionType,
			TransferType:      constants.TransferTypeCharge,
			Amount:            10000,
			Status:            constants.CompletedStatus,
		}

		response := GetTransactionAmount(context.Background(), txData, cardTxnDetail)
		assert.Equal(t, utils.MustConvertToInt64(100), response)
	})

	t.Run("SPEND_CARD_ATM", func(t *testing.T) {
		txData := &storage.TransactionsData{
			ClientBatchID:     "batch-123",
			TransactionAmount: "100",
			DebitOrCredit:     "debit",
			TransactionType:   constants.SpendCardAtmTransactionType,
		}

		cardTxnDetail := &storage.CardTransactionDetail{
			CardTransactionID: "batch-123",
			TransactionType:   constants.SpendCardAtmTransactionType,
			TransferType:      constants.TransferTypeCharge,
			Amount:            10000,
			CaptureAmount:     10000,
			Status:            constants.CompletedStatus,
		}

		response := GetTransactionAmount(context.Background(), txData, cardTxnDetail)
		assert.Equal(t, utils.MustConvertToInt64(-10000), response)
	})
}

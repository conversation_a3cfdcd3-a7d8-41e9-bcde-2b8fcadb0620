module gitlab.myteksi.net/dakota/transaction-history

replace (
	github.com/gogo/protobuf => github.com/gogo/protobuf v1.3.2
	github.com/miekg/dns => github.com/miekg/dns v1.1.25
	gitlab.myteksi.net/dbmy/transaction-history/api => ./api
	golang.org/x/crypto => golang.org/x/crypto v0.35.0
	golang.org/x/text => golang.org/x/text v0.3.8
	gopkg.in/yaml.v2 => gopkg.in/yaml.v2 v2.4.0
)

go 1.24.1

require (
	github.com/DATA-DOG/go-sqlmock v1.5.2
	github.com/Rhymond/go-money v1.0.3
	github.com/cenkalti/backoff v2.2.1+incompatible
	github.com/enescakir/emoji v1.0.0
	github.com/go-co-op/gocron v1.15.0
	github.com/google/uuid v1.6.0
	github.com/myteksi/hystrix-go v1.1.3
	github.com/nicksnyder/go-i18n/v2 v2.2.0
	github.com/onsi/ginkgo v1.16.5
	github.com/onsi/gomega v1.26.0
	github.com/samber/lo v1.39.0
	github.com/shopspring/decimal v1.3.1
	github.com/stretchr/testify v1.9.0
	gitlab.myteksi.net/bersama/core-banking/account-service/api v1.45.0
	gitlab.myteksi.net/dakota/common/context v0.17.0
	gitlab.myteksi.net/dakota/common/currency v1.6.0
	gitlab.myteksi.net/dakota/common/redis v0.20.0
	gitlab.myteksi.net/dakota/common/retryable-stream v1.8.0
	gitlab.myteksi.net/dakota/common/servicename v1.55.0
	gitlab.myteksi.net/dakota/common/tenants v1.1.0
	gitlab.myteksi.net/dakota/common/testauto v1.18.0
	gitlab.myteksi.net/dakota/common/tracing v1.10.0
	gitlab.myteksi.net/dakota/common/transaction-code v1.16.0
	gitlab.myteksi.net/dakota/core-banking/account-service/api v1.77.1
	gitlab.myteksi.net/dakota/customer-master/api/v2 v2.6.0
	gitlab.myteksi.net/dakota/flow v1.2.0
	gitlab.myteksi.net/dakota/klient v1.21.0
	gitlab.myteksi.net/dakota/payment/pairing-service/api v1.55.1
	gitlab.myteksi.net/dakota/payment/payment-experience/api v1.20.0
	gitlab.myteksi.net/dakota/schemas v1.16.76
	gitlab.myteksi.net/dakota/servus/v2 v2.53.0
	gitlab.myteksi.net/dbmy/common/active-profile v1.4.0
	gitlab.myteksi.net/dbmy/core-banking/account-service/api v1.64.0-dbmy
	gitlab.myteksi.net/dbmy/core-banking/external-lib v1.2.1-dbmy
	gitlab.myteksi.net/dbmy/customer-experience/api v1.57.1
	gitlab.myteksi.net/dbmy/customer-master/api/v2 v2.53.0-dbmy
	gitlab.myteksi.net/dbmy/transaction-history/api v1.9.0
	gitlab.myteksi.net/dbmy/transaction-statements/api v1.2.0-dbmy
	gitlab.myteksi.net/gophers/go/commons/data v1.0.11
	gitlab.myteksi.net/gophers/go/commons/util/log/yall v1.0.18
	gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent v1.0.0
	gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker v1.1.1
	gitlab.myteksi.net/gophers/go/commons/util/tags v1.1.7
	gitlab.myteksi.net/gophers/go/commons/util/time/grabtime v1.0.0
	gitlab.myteksi.net/gophers/go/staples/statsd v1.0.14
	gitlab.myteksi.net/snd/streamsdk v0.6.18
	golang.org/x/text v0.23.0
)

require (
	github.com/DataDog/datadog-agent/pkg/obfuscate v0.36.1 // indirect
	github.com/DataDog/datadog-go v4.8.3+incompatible // indirect
	github.com/DataDog/datadog-go/v5 v5.1.1 // indirect
	github.com/DataDog/sketches-go v1.4.1 // indirect
	github.com/Microsoft/go-winio v0.5.2 // indirect
	github.com/alecholmes/xfccparser v0.1.0 // indirect
	github.com/alecthomas/participle v0.4.1 // indirect
	github.com/aws/aws-sdk-go v1.44.294 // indirect
	github.com/cactus/go-statsd-client/v4 v4.0.0 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dgraph-io/ristretto v0.1.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/eapache/go-resiliency v1.2.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20180814174437-776d5712da21 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/flosch/pongo2 v0.0.0-20200913210552-0d938eb266f3 // indirect
	github.com/fsnotify/fsnotify v1.5.4 // indirect
	github.com/garyburd/redigo v1.6.3 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-openapi/errors v0.19.4 // indirect
	github.com/go-redis/redis v6.15.9+incompatible // indirect
	github.com/go-sql-driver/mysql v1.6.0 // indirect
	github.com/gofrs/uuid v4.0.0+incompatible // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/glog v1.2.4 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/gorilla/mux v1.8.0 // indirect
	github.com/gorilla/schema v1.4.1 // indirect
	github.com/grpc-ecosystem/grpc-opentracing v0.0.0-20170512040955-6c130eed1e29 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.2 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-retryablehttp v0.7.7 // indirect
	github.com/hashicorp/go-uuid v1.0.2 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.0.0 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.2 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/julienschmidt/httprouter v1.3.0 // indirect
	github.com/klauspost/compress v1.15.11 // indirect
	github.com/lightstep/lightstep-tracer-common/golang/gogo v0.0.0-20210210170715-a8dfcb80d3a7 // indirect
	github.com/lightstep/lightstep-tracer-go v0.25.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/myteksi/schema v0.0.0-20180214071320-149151f79a92 // indirect
	github.com/nxadm/tail v1.4.8 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/philhofer/fwd v1.1.2 // indirect
	github.com/pierrec/lz4/v4 v4.1.14 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/pquerna/ffjson v0.0.0-20190930134022-aa0246cd15f7 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/redis/go-redis/v9 v9.7.3 // indirect
	github.com/reterVision/go-kinesis v0.0.0-20150928061512-c0f0783318c3 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/rs/cors v1.5.0 // indirect
	github.com/shirou/gopsutil/v3 v3.22.4 // indirect
	github.com/showa-93/go-mask v0.6.1 // indirect
	github.com/spiffe/go-spiffe/v2 v2.1.1 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/tinylib/msgp v1.1.8 // indirect
	github.com/tklauser/go-sysconf v0.3.10 // indirect
	github.com/tklauser/numcpus v0.4.0 // indirect
	github.com/xdg/scram v0.0.0-20180814205039-7eeb5667e42c // indirect
	github.com/xdg/stringprep v1.0.3 // indirect
	github.com/yusufpapurcu/wmi v1.2.2 // indirect
	gitlab.myteksi.net/dakota/common/aws v1.4.2 // indirect
	gitlab.myteksi.net/dakota/common/env-injection v1.0.0 // indirect
	gitlab.myteksi.net/dakota/common/secrets-injection v1.0.3 // indirect
	gitlab.myteksi.net/dakota/gaia v0.1.1 // indirect
	gitlab.myteksi.net/dakota/servus v1.64.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/algo/cmap v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/deprecation v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/public/misc/systems v1.0.14 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/aws/grabkinesis v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/conf v1.0.2 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/mode v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/encoding/grabjson v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/ldflags v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/internal/logdefaults v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/logging v1.1.7 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/timerlog v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/monitor/statsd v1.0.10 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/network/iputil v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/redis/gredis v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/redis/gredismigrate v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/resilience/hystrix v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/server/swaggergen v1.0.4 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/tracer v1.0.5 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/validate v1.1.4 // indirect
	gitlab.myteksi.net/gophers/go/spartan/lechuck v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/staples/gredis3 v1.0.8 // indirect
	gitlab.myteksi.net/gophers/go/staples/logging v1.0.0 // indirect
	gitlab.myteksi.net/snd/sarama v1.34.0 // indirect
	gitlab.myteksi.net/spartan/hystrix-go/v2 v2.0.1 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.8.0 // indirect
	go.uber.org/zap v1.21.0 // indirect
	golang.org/x/crypto v0.36.0 // indirect
	golang.org/x/exp v0.0.0-20220303212507-bbda1eaf7a17 // indirect
	golang.org/x/net v0.38.0 // indirect
	golang.org/x/sync v0.3.0 // indirect
	golang.org/x/sys v0.31.0 // indirect
	golang.org/x/time v0.3.0 // indirect
	golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2 // indirect
	google.golang.org/genproto v0.0.0-20230711160842-782d3b101e98 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20230711160842-782d3b101e98 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20230711160842-782d3b101e98 // indirect
	google.golang.org/grpc v1.58.3 // indirect
	google.golang.org/protobuf v1.33.0 // indirect
	gopkg.in/DataDog/dd-trace-go.v1 v1.38.1 // indirect
	gopkg.in/redsync.v1 v1.0.1 // indirect
	gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7 // indirect
	gopkg.in/tylerb/graceful.v1 v1.2.15 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

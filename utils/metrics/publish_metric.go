// Package metrics contains method that push app data
package metrics

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/digicard_transaction_tx"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_balance_event"

	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/payment_engine_tx"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/internal/metrics"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// PublishMetrics ...
type PublishMetrics struct{}

// PublishMetricsImpl ...
//
//go:generate mockery --name PublishMetricsImpl --case underscore --inpackage
type PublishMetricsImpl interface {
	PublishPaymentEngineMetrics(data *payment_engine_tx.PaymentEngineTx, stats statsd.Client, err error)
	PublishDepositsCoreMetrics(data *storage.TransactionsData, stats statsd.Client)
	PublishDepositsCoreBalanceMetrics(data *deposits_balance_event.DepositsBalanceEvent, stats statsd.Client, err error)
	PublishDigicardTxnMetrics(data *digicard_transaction_tx.DigicardTransactionTx, stats statsd.Client)
	PublishLoanCoreTxMetrics(data *storage.TransactionsData, stats statsd.Client)
	PublishDepositsCoreForInterestAggMetrics(txnType string, stats statsd.Client, isSuccess bool)
}

// PublishPaymentEngineMetrics publish data of payment engine
func (p *PublishMetrics) PublishPaymentEngineMetrics(data *payment_engine_tx.PaymentEngineTx, stats statsd.Client, err error) {
	ctx := context.Background()
	var errorString string
	if err != nil {
		errorString = err.Error()
	}

	slog.FromContext(ctx).Info(metrics.PaymentEngineKafkaMessage, "Publishing Payment Engine Kafka Metrics")
	stats.Count1(string(servicename.TransactionHistory), metrics.PaymentEngineKafkaMessage,
		fmt.Sprintf("status:%s", data.Status),
		fmt.Sprintf("type:%s", data.TransactionCode.Type),
		fmt.Sprintf("subtype:%s", data.TransactionCode.SubType),
		fmt.Sprintf("errorOccured:%s", errorString))
}

// PublishDepositsCoreMetrics publish data of deposits core
func (p *PublishMetrics) PublishDepositsCoreMetrics(data *storage.TransactionsData, stats statsd.Client) {
	ctx := context.Background()
	slog.FromContext(ctx).Info(metrics.DepositsCoreKafkaMessage, "Publishing Deposits Core Kafka Metrics")
	stats.Count1(string(servicename.TransactionHistory), metrics.DepositsCoreKafkaMessage,
		fmt.Sprintf("status:%s", data.BatchStatus),
		fmt.Sprintf("transactionType:%s", data.TransactionType),
		fmt.Sprintf("transactionSubtype:%s", data.TransactionSubtype),
		fmt.Sprintf("tmTransactionType:%s", data.TmTransactionType),
		fmt.Sprintf("accountPhase:%s", data.AccountPhase))
}

// PublishDepositsCoreBalanceMetrics publish data of deposits core
func (p *PublishMetrics) PublishDepositsCoreBalanceMetrics(_ *deposits_balance_event.DepositsBalanceEvent, stats statsd.Client, err error) {
	slog.FromContext(context.Background()).Info(metrics.DepositsCoreBalanceKafkaMessage, "Publishing Deposits Core Balance Kafka Metrics")
	if err != nil {
		stats.Count1(
			string(servicename.TransactionHistory), metrics.DepositsCoreBalanceKafkaMessage, metrics.FailedTag,
			fmt.Sprintf("errorOccured:%s", err.Error()),
		)
		return
	}
	stats.Count1(string(servicename.TransactionHistory), metrics.DepositsCoreBalanceKafkaMessage)
}

// PublishDigicardTxnMetrics publish data of digicard txn
func (p *PublishMetrics) PublishDigicardTxnMetrics(data *digicard_transaction_tx.DigicardTransactionTx, stats statsd.Client) {
	ctx := context.Background()
	slog.FromContext(ctx).Info(metrics.DigicardTxnKafkaMessage, "Publishing Digicard Txn Kafka Metrics")
	stats.Count1(string(servicename.TransactionHistory), metrics.DigicardTxnKafkaMessage,
		fmt.Sprintf("status:%s", data.Status),
		fmt.Sprintf("transactionDomain:%s", data.TransactionCode.Domain),
		fmt.Sprintf("transactionType:%s", data.TransactionCode.Type))
}

// PublishLoanCoreTxMetrics publish data of loan core tx
func (p *PublishMetrics) PublishLoanCoreTxMetrics(data *storage.TransactionsData, stats statsd.Client) {
	ctx := context.Background()
	slog.FromContext(ctx).Info(metrics.LoanCoreTxKafkaMessage, "Publishing Loan Core Tx Kafka Metrics")
	stats.Count1(string(servicename.TransactionHistory), metrics.LoanCoreTxKafkaMessage,
		fmt.Sprintf("status:%s", data.BatchStatus),
		fmt.Sprintf("transactionType:%s", data.TransactionType),
		fmt.Sprintf("transactionSubtype:%s", data.TransactionSubtype),
		fmt.Sprintf("tmTransactionType:%s", data.TmTransactionType),
		fmt.Sprintf("accountPhase:%s", data.AccountPhase))
}

// PublishDepositsCoreForInterestAggMetrics publish data of deposits core
func (p *PublishMetrics) PublishDepositsCoreForInterestAggMetrics(txnType string, stats statsd.Client, isSuccess bool) {
	stats.Count1(string(servicename.TransactionHistory), metrics.DepositsCoreKafkaMessageForInterestAgg,
		fmt.Sprintf("transactionType:%s", txnType),
		fmt.Sprintf("isSuccess:%t", isSuccess))
}

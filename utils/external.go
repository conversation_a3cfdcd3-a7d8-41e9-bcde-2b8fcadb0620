package utils

import (
	"context"
	"fmt"

	accountService "gitlab.myteksi.net/dakota/core-banking/account-service/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
)

// GetAssociatedAccounts ...
func GetAssociatedAccounts(ctx context.Context, accountServiceClient accountService.AccountService, associatedAccountsReq accountService.ListAllAssociatedAccountsRequest, logTag string) (dto.ListAssociatedAccountsResponseDTO, error) {
	associatedAccountsResp, err := accountServiceClient.ListAllAssociatedAccounts(ctx, &associatedAccountsReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("error while fetching associated account details with account id %s: %s", associatedAccountsReq.AccountID, err.Error()))
		return dto.ListAssociatedAccountsResponseDTO{}, err
	}

	var associatedAccountsList []dto.AccountDetailDTO
	for _, associatedAccount := range associatedAccountsResp.AssociatedAccounts {
		associatedAccountsList = append(associatedAccountsList, dto.AccountDetailDTO{
			AccountID:          associatedAccount.AccountID,
			Status:             associatedAccount.Status,
			AccountType:        associatedAccount.AccountType,
			ProductVariantCode: associatedAccount.ProductVariantCode,
			SubStatus:          associatedAccount.SubStatus,
		})
	}

	return dto.ListAssociatedAccountsResponseDTO{
		CifNumber:          associatedAccountsResp.CifNumber,
		AssociatedAccounts: associatedAccountsList,
	}, nil
}

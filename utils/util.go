// Package utils ...
package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strconv"
	"time"

	"github.com/google/uuid"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	accountServiceDBMY "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	customerMasterDBMY "gitlab.myteksi.net/dbmy/customer-master/api/v2"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile"
)

// TimestampMonthFormat for formatting Time and getting month
const TimestampMonthFormat = "01"

// SearchStringArray ...
func SearchStringArray(arr []string, key string) bool {
	for _, str := range arr {
		if str == key {
			return true
		}
	}
	return false
}

// MinInt ...
func MinInt(a, b int) int {
	if a > b {
		return b
	}
	return a
}

// ConvertToInterface converts a slice of string to slice of interface{}
func ConvertToInterface(arr []string) []interface{} {
	interfaceArr := make([]interface{}, len(arr))
	for i, elem := range arr {
		interfaceArr[i] = elem
	}

	return interfaceArr
}

// GetTraceID returns the TraceID from the context to include in logs for tracing
func GetTraceID(ctx context.Context) slog.Tag {
	span := commonCtx.GetSpan(ctx)
	return slog.TraceID(SafeIntToString(span.Context().TraceID()))
}

// ToJSON converts struct to JSON string
func ToJSON(i interface{}) string {
	s, _ := json.Marshal(i)
	return string(s)
}

// GetAmountInCents converts the string amount into int64 and in cents
func GetAmountInCents(ctx context.Context, transactionAmount string) int64 {
	float64Amount, err := strconv.ParseFloat(transactionAmount, 64)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.AmountConversionLogTag, fmt.Sprintf("Error formatting amount, err: %s", err.Error()))
	}
	amount := int64(math.Round(float64Amount * 100))
	return amount
}

// GetLocalTimeFirstDayInUTC returns the firstDay of the month of Local time in UTC according to minutes offSet.
func GetLocalTimeFirstDayInUTC(currTime time.Time, offSetInMinutes int) time.Time {
	return time.Date(currTime.Year(), currTime.Month(), 1, 0, offSetInMinutes, 0, 0, time.UTC)
}

// MapPaginationParameters maps the request parameters to the pagination parameters.
func MapPaginationParameters(id string, startingBefore string, endingAfter string, startDate string, endDate string, pageSize int64) dto.PaginationParameters {
	return dto.PaginationParameters{
		AccountID:      id,
		StartingBefore: startingBefore,
		EndingAfter:    endingAfter,
		StartDate:      startDate,
		EndDate:        endDate,
		PageSize:       pageSize,
	}
}

// GetUniqueStringSliceInDescendingOrder returns unique set string from a slice of strings
func GetUniqueStringSliceInDescendingOrder(stringSlice []string) []string {
	keys := make(map[string]bool)
	var list []string
	for _, entry := range stringSlice {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}

	sort.Slice(list, func(i, j int) bool {
		return list[i] > list[j]
	})
	return list
}

// GetAccountList returns the list accounts created for a customer by CIF number
func GetAccountList(ctx context.Context, client accountService.AccountService, cifNumber string) ([]accountService.CASAAccountDetail, error) {
	casaAccountDetailList, err := client.ListCASAAccountsForCustomerDetail(ctx, &accountService.ListCASAAccountsForCustomerDetailRequest{
		CifNumber: cifNumber,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAccountFromAccountServiceTag, fmt.Sprintf("Unable to fetch accounts via account-service: %s", err.Error()), GetTraceID(ctx))
		return nil, err
	}

	return casaAccountDetailList.Accounts, nil
}

// GetAccount ...
func GetAccount(ctx context.Context, client accountService.AccountService, accountID string) (*accountService.Account, error) {
	accountDetail, err := client.GetAccount(ctx, &accountService.GetAccountRequest{
		AccountID:    accountID,
		FetchBalance: false,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAccountFromAccountServiceTag, fmt.Sprintf("Unable to fetch account %s via account-service: %s", accountID, err.Error()), GetTraceID(ctx))
		return nil, err
	}

	return accountDetail.Account, nil
}

// GetAccountIdsFromList ...
func GetAccountIdsFromList(casaAccounts []accountService.CASAAccountDetail) []string {
	var accountIds []string
	for _, account := range casaAccounts {
		accountIds = append(accountIds, account.Id)
	}
	return accountIds
}

// ToPreviousMonth ...
func ToPreviousMonth(date time.Time) time.Time {
	firstOfMonth := time.Date(date.Year(), date.Month()-1, 1, 0, 0, 0, 0, date.Location())
	lastOfMonth := firstOfMonth.AddDate(0, 1, -1)
	return lastOfMonth
}

// NewUUID ...
func NewUUID() string {
	return uuid.New().String()
}

// TruncateTillSecond ...
func TruncateTillSecond(t time.Time) time.Time {
	return t.Truncate(time.Second)
}

// ConvertAmountInCentsToAmount ...
func ConvertAmountInCentsToAmount(amountInCents int64) float64 {
	float64Amount := float64(amountInCents)
	amount := float64Amount / 100
	return amount
}

// IsValidTimeStampFormat ...
func IsValidTimeStampFormat(timestamp string) bool {
	if len(timestamp) > 0 {
		_, err := time.Parse(constants.TimeStampLayout, timestamp)
		return err == nil
	}
	return true
}

// GetCustomerIDWithActiveProfile ...
func GetCustomerIDWithActiveProfile(ctx context.Context, accountID string, accountServiceDBMYClient accountServiceDBMY.AccountService, customerMasterDBMYClient customerMasterDBMY.CustomerMaster) (string, error) {
	var customerID string
	slog.FromContext(ctx).Info(constants.GetActiveProfileIDLogTag, "Getting profileID from the headers")
	profileObj, ok := GetActiveProfile(ctx)
	if ok {
		customerID = profileObj.ProfileID
	}

	if customerID == "" {
		// profile ID is empty in the header
		slog.FromContext(ctx).Info(constants.GetActiveProfileIDLogTag, "Unable to fetch profileID from the headers")
		// Getting CIF number from account service
		account, err := GetAccountFromAccountService(ctx, accountID, accountServiceDBMYClient)
		if err != nil {
			return "", err
		}
		challengeID := account.CifNumber
		customerIdentity, err := GetCustomerCurrentActiveIdentity(ctx, challengeID, customerMasterDBMYClient)
		if err != nil {
			return "", err
		}
		customerID = customerIdentity.GetCustomerID()
	}

	return customerID, nil
}

// SafeIntToString converts int to string
func SafeIntToString[T uint64 | int](i T) string {
	return fmt.Sprintf("%d", i)
}

// MustConvertToInt64 converts uint to int
// nolint: gosec
func MustConvertToInt64[T uint64 | int](u T) int64 {
	switch val := any(u).(type) {
	case uint64:
		if val > math.MaxInt64 {
			panic(fmt.Errorf("overflow: cannot convert %d to int64", val))
		}
	}
	return int64(u)
}

// MustConvertToUint64 converts int to uint
// nolint: gosec
func MustConvertToUint64[T int64 | int](u T) uint64 {
	if u < 0 {
		panic(fmt.Errorf("overflow: cannot convert %d to uint64", u))
	}
	return uint64(u)
}

// AddActiveProfileToHeader ...
func AddActiveProfileToHeader(ctx context.Context, activeProfileObj activeProfile.ActiveProfile) context.Context {
	return context.WithValue(ctx, activeProfile.CtxActiveProfileKey, activeProfileObj)
}

// GetActiveProfile retrieves the active profile object from the context.
func GetActiveProfile(ctx context.Context) (activeProfile.ActiveProfile, bool) {
	activeProfileObj, ok := ctx.Value(activeProfile.CtxActiveProfileKey).(activeProfile.ActiveProfile)
	if !ok {
		return activeProfile.ActiveProfile{}, false
	}
	return activeProfileObj, true
}

// GetProfileID returns the profile ID from the context if present in it else returns it from CustomerMaster
func GetProfileID(ctx context.Context, c customerMaster.CustomerMaster) (string, error) {
	slog.FromContext(ctx).Info(constants.GetActiveProfileIDLogTag, "Getting profileID from the headers")
	if profileObj, ok := GetActiveProfile(ctx); ok && profileObj.ProfileID != "" {
		return profileObj.ProfileID, nil
	}
	// profile ID is empty in the header
	slog.FromContext(ctx).Info(constants.GetActiveProfileIDLogTag, "Unable to fetch profileID from the headers")
	//Look Up for CIF Number from customer-master
	slog.FromContext(ctx).Info(constants.GetActiveProfileIDLogTag, "Fetching CIF number from Customer Master", GetTraceID(ctx))
	cifNumber, lookUpErr := LookUpCifFromCustomerMaster(ctx, c)
	if lookUpErr != nil {
		return "", lookUpErr
	}
	return cifNumber, nil
}

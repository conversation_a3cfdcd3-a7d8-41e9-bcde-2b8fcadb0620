// Package validations ..
package validations

import (
	"context"
	"net/http"
	"strconv"

	"gitlab.myteksi.net/dakota/transaction-history/utils"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// ValidateIdentifier ...
func ValidateIdentifier(ctx context.Context, accountID string, externalID string, txnID string, batchID string) error {
	if accountID == "" && externalID == "" && txnID == "" && batchID == "" {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureEvent, "Missing identifier")
		return errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrMissingIdentifier.Code), 10), errors.ErrMissingIdentifier.Message)
	}
	return nil
}

// ValidateIdentifierCombination ...
// Logic:
// 1. ExternalID cannot be search together with any other identifier
// 2. AccountID cannot be search together with TxnID AND BatchID
// 3. TxnID cannot be search together with BatchID
// The above combinations have redundant filters because it is already unique enough to get the specific txn
func ValidateIdentifierCombination(ctx context.Context, accountID string, externalID string, txnID string, batchID string) error {
	switch {
	case accountID != "" && txnID != "" && batchID != "":
	case externalID != "" && (txnID != "" || batchID != "" || accountID != ""):
	case txnID != "" && batchID != "":
	default:
		return nil
	}
	slog.FromContext(ctx).Warn(constants.RequestValidationFailureEvent, "Invalid identifier combinations")
	return errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidIdentifier.Code), 10), errors.ErrInvalidIdentifier.Message)
}

// ValidateEmptyAttributeFilters validates the attribute filters are all empty
// Logic:
// If txn identifier is passed in, no attribute filter should have value
// because txn identifier is already unique enough to be searched for
func ValidateEmptyAttributeFilters(ctx context.Context, req *api.GetOpsSearchRequest) error {
	if req.Status == "" && req.TransactionType == "" && req.TransactionSubtype == "" && req.FromAmount == 0 && req.ToAmount == 0 && req.StartDate == "" && req.EndDate == "" && req.PageSize == 0 && req.StartingBefore == "" && req.EndingAfter == "" {
		return nil
	}
	slog.FromContext(ctx).Warn(constants.RequestValidationFailureEvent, "Invalid filter combinations")
	return errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidFilter.Code), 10), errors.ErrInvalidFilter.Message)
}

// ValidateTimestampAttribute validates the timestamp attributes are not all empty
// Logic:
// If want to filter by txn status, timestamp need to input together
// due to pagination logic, if no timestamp given, some required data might be filtered out from default time range
func ValidateTimestampAttribute(ctx context.Context, req *api.GetOpsSearchRequest) error {
	if req.StartDate == "" && req.EndDate == "" && req.StartingBefore == "" && req.EndingAfter == "" {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureEvent, "Missing timestamp filter")
		return errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrMissingTimestampFilter.Code), 10), errors.ErrMissingTimestampFilter.Message)
	}
	return nil
}

// ValidateStatus ...
func ValidateStatus(ctx context.Context, status string) error {
	if _, ok := utils.TxnStatusMap[status]; !ok {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureEvent, "Invalid status")
		return errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnStatus.Code), 10), errors.ErrInvalidTxnStatus.Message)
	}
	return nil
}

// ValidateTxnType ...
func ValidateTxnType(ctx context.Context, txnType string) error {
	if _, ok := utils.TxnTypeMap[txnType]; !ok {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureEvent, "Invalid transaction type")
		return errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnType.Code), 10), errors.ErrInvalidTxnType.Message)
	}
	return nil
}

// ValidateTxnSubtype ...
func ValidateTxnSubtype(ctx context.Context, txnSubtype string) error {
	if _, ok := utils.TxnSubtypeMap[txnSubtype]; !ok {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureEvent, "Invalid transaction subtype")
		return errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnSubtype.Code), 10), errors.ErrInvalidTxnSubtype.Message)
	}
	return nil
}

// ValidateAmount ...
func ValidateAmount(ctx context.Context, amountFrom int64, amountTo int64) error {
	if amountFrom != 0 && amountTo != 0 {
		if amountFrom > amountTo {
			slog.FromContext(ctx).Warn(constants.RequestValidationFailureEvent, "Invalid transaction amount range")
			return errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnAmountRange.Code), 10), errors.ErrInvalidTxnAmountRange.Message)
		}
	}
	if amountFrom < 0 || amountTo < 0 {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureEvent, "Invalid transaction amount")
		return errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidTxnAmount.Code), 10), errors.ErrInvalidTxnAmount.Message)
	}
	return nil
}

// ValidatePageSize ...
func ValidatePageSize(ctx context.Context, pageSize int64, minPageSize int64, maxPageSize int64) error {
	if pageSize < minPageSize || pageSize > maxPageSize {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureEvent, "Invalid page size")
		return errors.BuildCustomErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(errors.ErrInvalidPageSize.Code), 10), errors.ErrInvalidPageSize.Message)
	}
	return nil
}

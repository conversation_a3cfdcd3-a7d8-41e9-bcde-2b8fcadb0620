package utils

import (
	"context"
	"errors"
	"fmt"
	"reflect"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	utils "gitlab.myteksi.net/dakota/transaction-history/utils/dto"
	customErr "gitlab.myteksi.net/dakota/transaction-history/utils/errors"
	accountServiceDBMY "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	customerMasterDBMY "gitlab.myteksi.net/dbmy/customer-master/api/v2"
)

var (
	// ErrCifMappingNotFoundMsg To signify that cif number mapping not found
	ErrCifMappingNotFoundMsg = errors.New("CIF_MAPPING_NOT_FOUND:cif number not found")
)

// getServiceIDFromHeader ...
func getServiceIDFromHeader(ctx context.Context) string {
	return commonCtx.GetServiceID(ctx)
}

// getUserIDFromHeader ...
func getUserIDFromHeader(ctx context.Context) string {
	return commonCtx.GetUserID(ctx)
}

// CheckPermissionAccountService ...
func CheckPermissionAccountService(ctx context.Context, accountID string, cifNumber string, a accountService.AccountService) (string, error) {
	slog.FromContext(ctx).Info(constants.CheckAuthorizationLogTag, "calling accounts service", GetTraceID(ctx))
	req := &accountService.CheckPermissionsForAccountRequest{AccountID: accountID, CifNumber: cifNumber}
	checkPermissionResponse, err := a.CheckPermissionsForAccount(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CheckPermissionTag, fmt.Sprintf("Unable to check permission: %s", err.Error()), GetTraceID(ctx))
		return "", customErr.BuildErrorResponse(customErr.InternalServerError, "Failed to connect accounts-service")
	}
	status := fmt.Sprintf("%v", checkPermissionResponse.Status)
	slog.FromContext(ctx).Info(constants.CheckAuthorizationLogTag, fmt.Sprintf("Permission from accounts-service: %s", status), GetTraceID(ctx))
	return status, nil
}

// LookUpCifFromCustomerMaster ...
func LookUpCifFromCustomerMaster(ctx context.Context, c customerMaster.CustomerMaster) (string, error) {
	var serviceID, userID string
	serviceID = getServiceIDFromHeader(ctx)
	userID = getUserIDFromHeader(ctx)

	req := &customerMaster.LookupCIFNumberRequest{
		ID: userID,
		Target: &customerMaster.TargetGroup{
			ServiceID: serviceID,
		},
	}
	slog.FromContext(ctx).Info(constants.CheckAuthorizationLogTag, "calling customer master", GetTraceID(ctx))
	lookUpCifResponse, err := c.LookupCIFNumber(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LookUpCifTag, fmt.Sprintf("Unable to fetch cif number via customer-master: %s", err.Error()), GetTraceID(ctx))
		if reflect.DeepEqual(err.Error(), ErrCifMappingNotFoundMsg.Error()) {
			return "", customErr.BuildErrorResponse(customErr.ResourceNotFound, "cif number not found")
		}
		return "", customErr.BuildErrorResponse(customErr.InternalServerError, "Failed to call customer-master")
	}

	slog.FromContext(ctx).Info(constants.CheckAuthorizationLogTag, fmt.Sprintf("CifNumber from customer-master: %s", lookUpCifResponse.CifNumber), GetTraceID(ctx))
	return lookUpCifResponse.CifNumber, nil
}

// GetAccountFromAccountService ...
func GetAccountFromAccountService(ctx context.Context, accountID string, a accountServiceDBMY.AccountService) (*accountServiceDBMY.Account, error) {
	slog.FromContext(ctx).Info(constants.CheckPermissionAndGetParentTag, "calling accounts service", GetTraceID(ctx))
	req := &accountServiceDBMY.GetAccountRequest{AccountID: accountID}
	account, err := a.GetAccountDetailsByAccountID(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAccountFromAccountServiceTag, fmt.Sprintf("Unable to get account: %s", err.Error()), GetTraceID(ctx))
		return nil, customErr.BuildErrorResponse(customErr.InternalServerError, "Failed to connect accounts-service")
	}
	return account.Account, nil
}

// GetCustomerCurrentActiveIdentity ...
func GetCustomerCurrentActiveIdentity(ctx context.Context, challengeID string, c customerMasterDBMY.CustomerMaster) (*utils.CustomerActiveIdentity, error) {
	serviceID := getServiceIDFromHeader(ctx)
	userID := getUserIDFromHeader(ctx)

	if serviceID == "" || userID == "" {
		slog.FromContext(ctx).Warn(constants.LookUpCustomerIdentityLogTag, "Unable to fetch serviceID/userID from the headers")
		return nil, customErr.BuildErrorResponse(customErr.ResourceNotFound, customErr.ResourceNotFound.ErrorMessage())
	}

	apiRes, err := c.GetBusinessInfo(ctx, &customerMasterDBMY.GetBusinessInfoRequest{
		SafeID: userID,
		Status: customerMasterDBMY.BusinessRelationshipStatus_ACTIVE,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(constants.LookUpCustomerIdentityLogTag, fmt.Sprintf("Unable to fetch customer identity via customer-master for: %s", challengeID))
		return nil, customErr.BuildErrorResponse(customErr.InternalServerError, customErr.InternalServerError.ErrorMessage())
	}

	// CIF/BIF is not available to the caller, we will derive the identity on best effort based on biz profile presence
	if challengeID == "" {
		bif := ""
		if bizProfile, ok := utils.FindLatestBizProfile(apiRes.BusinessRelationships); ok {
			bif = *bizProfile.Bif
		}
		return &utils.CustomerActiveIdentity{
			SafeID: userID,
			CIF:    apiRes.CIF,
			BIF:    bif,
		}, nil
	}

	for _, business := range apiRes.BusinessRelationships {
		if challengeID == *business.Bif {
			return &utils.CustomerActiveIdentity{
				SafeID: userID,
				BIF:    *business.Bif,
				CIF:    apiRes.CIF,
			}, nil
		}
	}
	if challengeID == apiRes.CIF {
		return &utils.CustomerActiveIdentity{
			SafeID: userID,
			BIF:    "",
			CIF:    challengeID,
		}, nil
	}

	slog.FromContext(ctx).Warn(constants.LookUpCustomerIdentityLogTag, fmt.Sprintf("The given customerID %s does not match any of the retrieved profiles", challengeID))
	return nil, customErr.BuildErrorResponse(customErr.ResourceNotFound, customErr.ResourceNotFound.ErrorMessage())
}

package localise

import (
	"os"
	"testing"

	"gitlab.myteksi.net/dakota/common/tenants"

	"gitlab.myteksi.net/dakota/transaction-history/server/config"

	"github.com/stretchr/testify/assert"
)

func TestLocalise(t *testing.T) {
	t.Run("Should return English Locale when ID not present", func(t *testing.T) {
		mockAppConfig := &config.AppConfig{
			Locale: config.Locale{Language: "id"},
		}
		os.Setenv("LOCALISATION_PATH", "./localisetest")
		Init(mockAppConfig)
		response := Translate("interestEarned")
		assert.Equal(t, response, "Interest Earned")
	})
	t.Run("Should return bahasa locale when bahasa is present", func(t *testing.T) {
		mockAppConfig := &config.AppConfig{
			Locale: config.Locale{Language: "id"},
		}
		os.Setenv("LOCALISATION_PATH", "./localisetest")
		Init(mockAppConfig)
		response := Translate("taxOnInterest")
		assert.Equal(t, response, "Pajak atas Bunga")
	})
	t.Run("Should translate using DBMY en.json when tenant is MY", func(t *testing.T) {
		defer func() { config.SetTenant("") }()
		config.SetTenant(tenants.TenantMY)
		mockAppConfig := &config.AppConfig{
			Locale: config.Locale{Language: "en"},
		}
		os.Setenv("LOCALISATION_PATH", "./localisetest/dbmy")
		Init(mockAppConfig)
		response := Translate("gxbank")
		assert.Equal(t, response, "GX Bank")
	})
}

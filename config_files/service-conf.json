{"name": "transaction-history Service", "serviceName": "transaction-history", "host": "0.0.0.0", "port": 8080, "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "root@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10}, "slave": {"dsn": "root@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10}}}, "statsd": {"host": "localhost", "port": 8125}, "trace": {"host": "localhost", "port": 8126}, "logger": {"syslogTag": "structuredlog.transaction-history", "workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": true}, "peConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "dev-payment-engine-tx", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "transaction-history-local", "enable": true}, "dcConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "dev-deposits-posting-batch-event", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "local-transaction-history"}, "dbConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "dev-deposits-balance-event", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "local-transaction-history-client"}, "sqsConfig": {"queueURL": "arn:aws:sqs:ap-southeast-1:404825353787:dev-backend-transaction-history-sqs-dlq20231210084151477700000005", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 1}, "digicardTxnKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "digicard-txn-transaction-history-local", "clusterType": "critical", "topicName": "dev-digicard-txn-tx", "enableTLS": true, "initOffset": "oldest"}, "loanCoreTxKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "dev-loan-core-tx", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "transaction-history-local", "enable": true}, "loanExpTxKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "dev-loan-exp-tx", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "transaction-history-local", "enable": true}, "interestAggKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "dev-deposits-posting-batch-event", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "interest-aggregate-local", "enable": true, "consumerGroupID": "dev-interest-aggregate-consumerID"}, "customerMasterConfig": {"baseURL": "https://backend.dev.g-bank.app/customer-master"}, "customerExperienceConfig": {"baseURL": "http://customer-experience.onboarding.svc.cluster.local"}, "accountServiceConfig": {"baseURL": "https://backend.dev.g-bank.app/account-service"}, "paymentExperienceConfig": {"baseURL": "https://backend.dev.g-bank.app/payment-experience"}, "transactionStatementsConfig": {"baseURL": "https://backend.dev.g-bank.app/transaction-statements", "statementReadyByDays": 7, "_statementReadyByDays": "All statements will be ready by x days"}, "transactionHistoryClientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}, "transactionHistoryServiceConfig": {"defaultCurrency": "MYR", "pastMonthsThresholdForCalendarActivity": 12, "maxRetryForBalanceStream": 10, "sleepDurationForBalanceStream": 1, "transactionsSearchDurationLimitInMonths": 6, "apiRequestTimeoutInSec": 30}, "iconConfig": {"interestEarn": "https://assets.dev.g-bank.app/txn-history/interest_earn.png", "bankAdjustment": "https://assets.dev.g-bank.app/txn-history/bank_adjustment.png", "pocketFunding": "https://assets.dev.g-bank.app/txn-history/pocket_funding.png", "mainPocketWithdrawal": "https://assets.dev.g-bank.app/txn-history/main_pocket_withdrawal.png", "pocketPocketWithdrawal": "https://assets.dev.g-bank.app/txn-history/pocket_pocket_withdrawal.png", "savingsPocketTransfer": "https://assets.dev.g-bank.app/txn-history/pocket_moneyin.png", "savingsPocketWithdrawal": "https://assets.dev.g-bank.app/txn-history/pocket_withdrawal.png", "grab": "https://assets.dev.g-bank.app/txn-history/grab.png", "rewards": "https://assets.dev.bankfama.net/dev/UserDefault.png", "withdrawal": "https://assets.dev.bankfama.net/dev/withdrawal.png", "transferIn": "https://assets.dev.g-bank.app/txn-history/main_money_in.png", "transferOut": "https://assets.dev.g-bank.app/txn-history/main_money_out.png", "transferFee": "https://assets.dev.bankfama.net/dev/transfer_money.png", "interestPayout": "https://assets.dev.g-bank.app/txn-history/interest_earn.png", "taxOnInterest": "https://assets.dev.bankfama.net/dev/tax_on_interest.png", "adjustment": "https://assets.dev.g-bank.app/txn-history/gxb.png", "defaultTransaction": "https://assets.dev.bankfama.net/dev/UserDefault.png", "mastercard": "https://assets.dev.g-bank.app/txn-history/mastercard_icon.png", "merchant": "https://assets.dev.g-bank.app/txn-history/merchant_icon.png", "lendingDrawdown": "https://assets.dev.g-bank.app/lending/lending_drawndown.png", "lendingRepayment": "https://assets.dev.g-bank.app/lending/lending_withdraw.png"}, "workerConfig": {"updateInterestAggregateWorkerConf": {"lockDurationInMinutes": 200, "cronExpression": "00 17 * * *", "batchSizeInRow": 1000, "batchCOPInSecond": 1, "parentBatchSubPaginationInSeconds": 3600, "refreshAggregation": false, "updateBatchSizeInRow": 1000}}, "populateInterestAggregateOneTimer": {"startDate": "2022-04-26", "endDate": "2022-07-03", "runOneTimer": true, "batchSizeInDays": 10, "lockDurationInMinutes": 2000, "refreshAggregation": false, "updateBatchSizeInRow": 1000}, "snowflakeConfig": {"dsnOPSReader": ""}, "stm453TransactionInfoFeatureFlag": {"enabled": false}, "digicardFeatureFlag": {"enabled": true}, "featureFlags": {"insertTransactionsByBatch": {"enabled": true}, "replicaRead": {"enabled": true}, "checkStatementStatus": {"enabled": true}, "updatePostingInstructionBatchForLending": {"enabled": true}, "batchifyDailyInterestAgg": {"enabled": true}, "batchifyOneTimeInterestAgg": {"enabled": true}, "retryableDepositCoreStream": {"enabled": true}, "retryableDepositBalanceStream": {"enabled": true}, "depositCoreStream": {"enabled": true}, "depositBalanceStream": {"enabled": true}, "bizAuthorisation": {"enabled": true}, "enableTransactionsSearchDurationFilter": {"enabled": true}, "enableBizFlexiCredit": {"enabled": true}, "enableSearchTransactionLimit": {"enabled": true}, "enableExhaustiveCursorPagination": {"enabled": true}, "enableAuthzWithProfileID": {"enabled": true}, "isRetryableInterestAggStreamEnabled": {"enabled": true}}, "tenant": "MY", "locale": {"currency": "MYR", "language": "en"}, "pairingServiceConfig": {"serviceName": "pairing-service", "baseURL": "https://backend.dev.g-bank.app/pairing-service", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}}, "transactionsSearchFilterDurationInMonths": 6, "exhaustivePaginationConfig": {"maxCursorLimit": 10, "executionSleepDurationInMs": 500}, "cachingConfig": {"ttl": 30, "redisConfig": {"addr": "clustercfg.dev-cb-ec-transaction-history-01.qujkmo.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 60, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}}, "interestAggregationConfig": {"retentionDay": 5, "startMonth": "2024-06", "excludeAccounts": {"*********": true}}}
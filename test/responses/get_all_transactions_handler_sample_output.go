package responses

import (
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetAllTransactionsFirstPageResponse ...
// nolint: dupl
func GetAllTransactionsFirstPageResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "abc123efg",
			DisplayName:       "UserNo2",
			IconURL:           "",
			Amount:            -13,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category: &api.Category{
				Id:   "",
				Name: "Transfer Out",
			},
		}, {
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "efg123abd",
			DisplayName:       "UserNo2",
			IconURL:           "",
			Amount:            +513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category: &api.Category{
				Id:   "",
				Name: "Transfer Out",
			},
		}},
	}
}

// GetAllTransactionsFirstPageResponses ...
func GetAllTransactionsFirstPageResponses() *api.GetTransactionsHistoryResponse {
	response := GetAllTransactionsFirstPageResponse()
	response.Data[0].IconURL = "https://assets.dev.bankfama.net/dev/transfer_money.png"
	response.Data[1].IconURL = "https://assets.dev.bankfama.net/dev/transfer_money.png"
	return response
}

// GetAllTransactionsFirstPageResponseForListTransaction ...
// nolint: dupl
func GetAllTransactionsFirstPageResponseForListTransaction() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{{
			BatchID:           "abc123efg",
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			DisplayName:       "UserNo2",
			IconURL:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Amount:            -13,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category: &api.Category{
				Id:   "",
				Name: "Transfer Out",
			},
		}, {
			BatchID:           "efg123abd",
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			DisplayName:       "UserNo2",
			IconURL:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Amount:            +513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category: &api.Category{
				Id:   "",
				Name: "Transfer Out",
			},
		}},
	}
}

// GetAllTransactionsLastPageResponse ...
// nolint : dupl
func GetAllTransactionsLastPageResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "/v1/accounts/**********/transactions?pageSize=2&endingAfter=MjAyMS0wOC0yMFQwMjoxMDowMCwxLDMsODAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWY=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMDowMCwxLDMsODAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWY=",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "8007e9c10ea841c593bee2e65df599ef",
			BatchID:           "efg1111abd",
			DisplayName:       "UserNo2",
			IconURL:           "",
			Amount:            1512,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer In"},
		}},
	}
}

// GetAllTransactionsLastPageResponseForListResponse  ...
// nolint : dupl
func GetAllTransactionsLastPageResponseForListResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "/v1/accounts/**********/transactions?pageSize=2&endingAfter=MjAyMS0wOC0yMFQwMjoxMDowMCwxLDMsODAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWY=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMDowMCwxLDMsODAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWY=",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "8007e9c10ea841c593bee2e65df599ef",
			BatchID:           "efg1111abd",
			DisplayName:       "UserNo2",
			IconURL:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Amount:            1512,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer In"},
		}},
	}
}

// GetAllTransactionsOnlyPageResponse ...
// nolint:dupl
func GetAllTransactionsOnlyPageResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{{
			BatchID:           "abc123efg",
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			DisplayName:       "UserNo2",
			IconURL:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Amount:            -13,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer Out"},
		}, {
			BatchID:           "efg123abd",
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			DisplayName:       "UserNo2",
			IconURL:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Amount:            513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer Out"},
		}, {
			TransactionID:     "8007e9c10ea841c593bee2e65df599ef",
			BatchID:           "efg1111abd",
			DisplayName:       "UserNo2",
			IconURL:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Amount:            1512,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer In"},
		}},
	}
}

// GetAllTransactionsOnlyPageResponseForSearch ...
// nolint: dupl
func GetAllTransactionsOnlyPageResponseForSearch() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "abc123efg",
			DisplayName:       "UserNo2",
			IconURL:           "",
			Amount:            -13,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer Out"},
		}, {
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "efg123abd",
			DisplayName:       "UserNo2",
			IconURL:           "",
			Amount:            513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer Out"},
		}, {
			TransactionID:     "8007e9c10ea841c593bee2e65df599ef",
			BatchID:           "efg1111abd",
			DisplayName:       "UserNo2",
			IconURL:           "",
			Amount:            1512,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer In"},
		}},
	}
}

// GetAllTransactionsBackwardScrollingPrevPageResponse ...
// nolint: dupl
func GetAllTransactionsBackwardScrollingPrevPageResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "abc123efg",
			DisplayName:       "UserNo2",
			IconURL:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Amount:            -13,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer Out"},
		}, {
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "efg123abd",
			DisplayName:       "UserNo2",
			IconURL:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Amount:            513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer Out"},
		}},
	}
}

// GetAllTransactionsBackwardScrollingPrevPageResponseForSearch ...
// nolint: dupl
func GetAllTransactionsBackwardScrollingPrevPageResponseForSearch() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "abc123efg",
			DisplayName:       "UserNo2",
			IconURL:           "",
			Amount:            -13,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer Out"},
		}, {
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "efg123abd",
			DisplayName:       "UserNo2",
			IconURL:           "",
			Amount:            513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer Out"},
		}},
	}
}

// GetAllTransactionsPrevNextBothExistResponse ...
// nolint : dupl
func GetAllTransactionsPrevNextBothExistResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=1&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "/v1/accounts/**********/transactions?pageSize=1&endingAfter=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "efg123abd",
			DisplayName:       "UserNo2",
			IconURL:           "https://assets.sgbank.dev/dev/txHistory/images/UserDefault.png",
			Amount:            513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer Out"},
		}},
	}
}

// GetAllTransactionsWithAllCategoriesAndIconsResponse ...
// nolint : dupl
func GetAllTransactionsWithAllCategoriesAndIconsResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
				BatchID:           "abc123efg",
				DisplayName:       "UserNo2",
				IconURL:           constants.IconURLMap[constants.Withdrawal],
				Amount:            -13,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Name: "Withdrawal"},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599edd",
				BatchID:           "fasdfasdf",
				DisplayName:       "",
				IconURL:           constants.IconURLMap[constants.TransferIn],
				Amount:            13,
				Status:            "",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Name: "Transfer In"},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599edd",
				BatchID:           "abc12fasdf3efdg",
				DisplayName:       "",
				IconURL:           constants.IconURLMap[constants.TransferOut],
				Amount:            -13,
				Status:            "",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Name: "Transfer Out"},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599edd",
				BatchID:           "abc123efasdffdag",
				DisplayName:       "",
				IconURL:           constants.IconURLMap[constants.TransferIn],
				Amount:            13,
				Status:            "",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Name: "Transfer In"},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599edd",
				BatchID:           "abc123effasddag",
				DisplayName:       "Biaya Transaksi",
				IconURL:           constants.IconURLMap[constants.TransferFee],
				Amount:            -13,
				Status:            "",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Name: "Transfer Fee"},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599edx",
				BatchID:           "abc123effasddag",
				DisplayName:       "",
				IconURL:           constants.IconURLMap[constants.TransferOut],
				Amount:            -1000,
				Status:            "",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Name: "Transfer Out"},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599edd",
				BatchID:           "abc1sdf23efdg",
				DisplayName:       "",
				IconURL:           constants.IconURLMap[constants.TransferIn],
				Amount:            13,
				Status:            "",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Name: "Transfer In"},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599edd",
				BatchID:           "abc123efdsdg",
				DisplayName:       "Bunga Didapat",
				IconURL:           constants.IconURLMap[constants.InterestPayout],
				Amount:            13,
				Status:            "",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599eddfsd",
				BatchID:           "abc123efdgf",
				DisplayName:       "Pajak atas Bunga",
				IconURL:           constants.IconURLMap[constants.TaxOnInterest],
				Amount:            -13,
				Status:            "",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599edd",
				BatchID:           "abc123efdg",
				DisplayName:       "BANK",
				IconURL:           constants.IconURLMap[constants.Adjustment],
				Amount:            13,
				Status:            "",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Name: "Adjustment Transaction"},
			},
		},
	}
}

// GetAllTransactionsForPaymentFeeResponse ...
// nolint : dupl
func GetAllTransactionsForPaymentFeeResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "b195a28d62264cc7be07f2b93c5b701f",
				BatchID:           "aa44338fd3444a43ad45218bb54c62bc",
				DisplayName:       "",
				IconURL:           constants.IconURLMap[constants.TransferOut],
				Amount:            -2200000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Name: "Transfer Out"},
			},
			{
				TransactionID:     "feaeb973ce6d47e3ae91343e6dacf79f",
				BatchID:           "aa44338fd3444a43ad45218bb54c62bc",
				DisplayName:       "Biaya Transaksi",
				IconURL:           constants.IconURLMap[constants.TransferFee],
				Amount:            -650000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Name: "Transfer Fee"},
			},
		},
	}
}

// GetAllTransactionsForChildAccountTransfersResponse ...
// nolint : dupl
func GetAllTransactionsForChildAccountTransfersResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"prev":         "",
			"prevCursorID": "",
			"next":         "",
			"nextCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599es",
				BatchID:           "client-batch-id-withdrawal",
				DisplayName:       "source-name",
				IconURL:           "",
				Amount:            13,
				Currency:          locale.Currency,
				Status:            "",
				CreationTimestamp: time.Unix(**********, 0).UTC(),
				Category: &api.Category{
					Id:   "",
					Name: "Transfer In",
				},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599ei",
				BatchID:           "client-batch-id-funding",
				DisplayName:       "destination-name",
				IconURL:           "https://assets.dev.bankfama.net/dev/transfer_money.png",
				Amount:            -13,
				Currency:          locale.Currency,
				Status:            "",
				CreationTimestamp: time.Unix(**********, 0).UTC(),
				Category: &api.Category{
					Id:   "",
					Name: "Withdrawal",
				},
			},
		},
	}
}

// GetAllTransactionsForCardTxnResponse ...
// nolint : dupl
func GetAllTransactionsForCardTxnResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "b195a28d62264cc7be07f2b93c5b701f",
				BatchID:           "aa44338fd3444a43ad45218bb54c62bc",
				DisplayName:       "Bunga Didapat",
				IconURL:           constants.IconURLMap[constants.TransferIn],
				Amount:            2200000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(1629425600, 0).UTC(),
				Category:          &api.Category{},
			},
			{
				TransactionID:     "b195a28d62264cc7be07f2b93c5b701faq",
				BatchID:           "aa44338fd3444a43ad45218bb54c62bcdd",
				DisplayName:       "MerchantDescription",
				IconURL:           constants.IconURLMap[constants.DebitCardDomain],
				Amount:            -10000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(**********, 0).UTC(),
				Category:          &api.Category{Name: "Debit Card Payment"},
			},
		},
	}
}

// GetAllTransactionsForLoanTxnResponse ...
// nolint : dupl
func GetAllTransactionsForLoanTxnResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "feaeb973ce6d47e3ae91343e6dacf79f",
				BatchID:           "02046bef37a44a24a49c79437288cf88",
				DisplayName:       "FlexiCredit",
				IconURL:           constants.IconURLMap[constants.TransferIn],
				Amount:            100000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(1629425600, 0).UTC(),
				Category:          &api.Category{Name: "Drawdown"},
			},
			{
				TransactionID:     "feaeb973ce6d47e3ae91343e6dacf79f",
				BatchID:           "a1e9564232604bfead406d69cb985fd5",
				DisplayName:       "FlexiCredit",
				IconURL:           constants.IconURLMap[constants.TransferIn],
				Amount:            10000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(1629428400, 0).UTC(),
				Category:          &api.Category{Name: "Drawdown"},
			},
			{
				TransactionID:     "b195a28d62264cc7be07f2b93c5b701f",
				BatchID:           "aa44338fd3444a43ad45218bb54c62bc",
				DisplayName:       "Bunga Didapat",
				IconURL:           constants.IconURLMap[constants.TransferIn],
				Amount:            2200000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(1629425600, 0).UTC(),
				Category:          &api.Category{},
			},
			{
				TransactionID:     "b195a28d62264cc7be07f2b93c5b701faq",
				BatchID:           "87caacde9e3b41edaa004b430fefd5bc",
				DisplayName:       "FlexiCredit repayment",
				IconURL:           constants.IconURLMap[constants.TransferOut],
				Amount:            -5000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(**********, 0).UTC(),
				Category:          &api.Category{Name: "Repayment"},
			},
		},
	}
}

// GetAllTransactionsForBizLendingTxnResponse ...
// nolint : dupl
func GetAllTransactionsForBizLendingTxnResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "feaeb973ce6d47e3ae91343e6dacf79f",
				BatchID:           "02046bef37a44a24a49c79437288cf88",
				DisplayName:       "Biz FlexiLoan",
				IconURL:           constants.IconURLMap[constants.TransferIn],
				Amount:            100000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(1629425600, 0).UTC(),
				Category:          &api.Category{Name: "Drawdown"},
			},
			{
				TransactionID:     "feaeb973ce6d47e3ae91343e6dacf79f",
				BatchID:           "a1e9564232604bfead406d69cb985fd5",
				DisplayName:       "Biz FlexiLoan repayment",
				IconURL:           constants.IconURLMap[constants.TransferOut],
				Amount:            10000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(1629428400, 0).UTC(),
				Category:          &api.Category{Name: "Repayment"},
			},
			{
				TransactionID:     "b195a28d62264cc7be07f2b93c5b701f",
				BatchID:           "aa44338fd3444a43ad45218bb54c62bc",
				DisplayName:       "Biz FlexiLoan repayment",
				IconURL:           constants.IconURLMap[constants.TransferOut],
				Amount:            2200000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(1629425600, 0).UTC(),
				Category:          &api.Category{Name: "WriteOff"},
			},
			{
				TransactionID:     "b195a28d62264cc7be07f2b93c5b701faq",
				BatchID:           "87caacde9e3b41edaa004b430fefd5bc",
				DisplayName:       "Biz FlexiLoan repayment",
				IconURL:           constants.IconURLMap[constants.TransferOut],
				Amount:            -5000,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(**********, 0).UTC(),
				Category:          &api.Category{Name: "WriteOff"},
			},
		},
	}
}

// GetAllTransactionsForInsuranceTxnResponse ...
func GetAllTransactionsForInsuranceTxnResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599ef",
				BatchID:           "test-client-batch-id1",
				DisplayName:       "Cyber Fraud Protect payment",
				IconURL:           constants.IconURLMap[constants.InsuranceDomain],
				Amount:            -513,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(**********, 0).UTC(),
				Category: &api.Category{
					Name: "Insurance",
				},
			},
		},
	}
}

// GetAllTransactionsForBizTxnResponse ...
func GetAllTransactionsForBizTxnResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "a1943437135d4674bd1f39df6233c83c",
				BatchID:           "3e9c0187cf4941c0bf8f680fc813a656",
				DisplayName:       "",
				IconURL:           constants.IconURLMap[constants.TransferIn],
				Amount:            1520,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(**********, 0).UTC(),
				Category: &api.Category{
					Name: "Transfer In",
				},
			},
		},
	}
}

// GetTransactionsForInterestAndTax ...
// nolint : dupl
func GetTransactionsForInterestAndTax() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"prev":         "",
			"prevCursorID": "",
			"next":         "",
			"nextCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599eddfsd",
				BatchID:           "abc123efdgf",
				DisplayName:       "Pajak atas Bunga",
				IconURL:           constants.IconURLMap[constants.TaxOnInterest],
				Amount:            -13,
				Currency:          locale.Currency,
				Status:            "",
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Id: "", Name: ""},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599edd",
				BatchID:           "abc123efdsdg",
				DisplayName:       "Bunga Didapat",
				IconURL:           constants.IconURLMap[constants.InterestPayout],
				Amount:            13,
				Currency:          locale.Currency,
				Status:            "",
				CreationTimestamp: time.Time{},
				Category:          &api.Category{Id: "", Name: ""},
			},
		},
	}
}

// GetAllTransactionsPrevNextBothExistResponseForSearch ...
// nolint : dupl
func GetAllTransactionsPrevNextBothExistResponseForSearch() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=1&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "/v1/accounts/**********/transactions?pageSize=1&endingAfter=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "9007e9c10ea841c593bee2e65df599ed",
			BatchID:           "efg123abd",
			DisplayName:       "UserNo2",
			IconURL:           "",
			Amount:            513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
			Category:          &api.Category{Name: "Transfer Out"},
		}},
	}
}

// GetAllTransactionsPocketTxnsResponse ...
// nolint : dupl
func GetAllTransactionsPocketTxnsResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599ef",
				BatchID:           "test-client-batch-id1",
				DisplayName:       "MAIN_ACCOUNT",
				IconURL:           constants.IconURLMap["DefaultTransaction"],
				Amount:            -513,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(**********, 0).UTC(),
				Category: &api.Category{
					Id:   "",
					Name: "Withdrawal",
				},
			},
			{
				TransactionID:     "9007e9c10ea841c593bee2e65df599ef",
				BatchID:           "test-client-batch-id4",
				DisplayName:       "Paris1",
				IconURL:           constants.IconURLMap["DefaultTransaction"],
				Amount:            100,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(**********, 0).UTC(),
				Category: &api.Category{
					Id:   "",
					Name: "Transfer In",
				},
			},
		},
	}
}

// GetGrabTransactionsPocketTxnsResponse ...
func GetGrabTransactionsPocketTxnsResponse() *api.GetTransactionsHistoryResponse {
	locale := utils.GetLocale()
	return &api.GetTransactionsHistoryResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{
			{
				TransactionID:     "client-tid-6",
				BatchID:           "abcd12345",
				DisplayName:       "Grab Ride",
				IconURL:           constants.IconURLMap["DefaultTransaction"],
				Amount:            -13,
				Status:            "COMPLETED",
				Currency:          locale.Currency,
				CreationTimestamp: time.Unix(**********, 0),
				Category: &api.Category{
					Name: "Adjustment Transaction",
				},
			},
		},
	}
}

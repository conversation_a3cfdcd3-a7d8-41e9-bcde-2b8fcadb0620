package responses

import (
	"time"

	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetLendingTransactionDetailResponse  returns a sample outputs for GetTransactionDetail requests.
// nolint:dupl
func GetLendingTransactionDetailResponse() []*api.GetLendingTransactionDetailResponse {
	response1 := fetchDrawDownResponse()
	response2 := fetchRepaymentResponse()
	response3 := fetchOverpaidRepaymentResponse()
	response4 := fetchRepaymentResponseWithMainAccount()

	return []*api.GetLendingTransactionDetailResponse{response1, response2, response3, response4}
}

// nolint:dupl
func fetchDrawDownResponse() *api.GetLendingTransactionDetailResponse {
	return &api.GetLendingTransactionDetailResponse{
		Amount: &api.Money{
			CurrencyCode: "MYR",
			Val:          -1300,
		},
		TransactionDescription: "Loan for Europe Trip",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		TransactionTimestamp:   time.Unix(**********, 0).UTC(),
		CounterParty: &api.LendingCounterParty{
			DisplayName: "Main account",
			AccountID:   "54321",
			IconURL:     "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			ProxyDetail: &api.ProxyDetail{
				Channel: "RPP_NETWORK",
				Type:    "MSISDN",
				Value:   "+65****8923",
			},
		},
		DrawdownDetails: &api.DrawDownDetails{
			WithdrawalID: "12345",
			TransferID:   "abc123efg",
			LoanName:     "Europe Trip",
			RepaymentPeriod: &api.RepaymentPeriod{
				TimePeriodInMonths: 10,
				StartDate:          "2023-02-07",
				EndDate:            "2023-06-07",
				InstallmentCount:   10,
			},
			MonthlyRepayment: &api.MonthlyRepayment{
				Descriptions: nil,
			},
			TotalPayment: &api.TotalPayment{
				TotalAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          20435,
				},
				InterestAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          335,
				},
				InterestRate:          3.5,
				EffectiveInterestRate: 5.3520565,
				DisbursedAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          0,
				},
				ProcessingFeeAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          0,
				},
			},
		},
		RepaymentDetails: nil,
	}
}

// nolint:dupl
func fetchRepaymentResponse() *api.GetLendingTransactionDetailResponse {
	return &api.GetLendingTransactionDetailResponse{
		Amount: &api.Money{
			CurrencyCode: "MYR",
			Val:          13400,
		},
		TransactionDescription: "Payment for Europe Trip",
		Status:                 "COMPLETED",
		TransactionTimestamp:   time.Unix(**********, 0).UTC(),
		CounterParty: &api.LendingCounterParty{
			DisplayName: "Main account",
			AccountID:   "54321",
			IconURL:     "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			ProxyDetail: &api.ProxyDetail{
				Channel: "RPP_NETWORK",
				Type:    "MSISDN",
				Value:   "+65****8923",
			},
		},
		DrawdownDetails:  nil,
		RepaymentDetails: fetchRepaymentDetails(),
	}
}

// nolint:dupl
func fetchRepaymentResponseWithMainAccount() *api.GetLendingTransactionDetailResponse {
	return &api.GetLendingTransactionDetailResponse{
		Amount: &api.Money{
			CurrencyCode: "MYR",
			Val:          13400,
		},
		TransactionDescription: "Payment for Europe Trip",
		Status:                 "COMPLETED",
		TransactionTimestamp:   time.Unix(**********, 0).UTC(),
		CounterParty: &api.LendingCounterParty{
			DisplayName: "Main account",
			IconURL:     "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			AccountID:   "54321",
			ProxyDetail: &api.ProxyDetail{
				Channel: "RPP_NETWORK",
				Type:    "MSISDN",
				Value:   "+65****8923",
			},
		},
		DrawdownDetails:  nil,
		RepaymentDetails: fetchRepaymentDetails(),
	}
}

// nolint:dupl
func fetchOverpaidRepaymentResponse() *api.GetLendingTransactionDetailResponse {
	return &api.GetLendingTransactionDetailResponse{
		Amount: &api.Money{
			CurrencyCode: "MYR",
			Val:          13400,
		},
		TransactionDescription: "Payment for Europe trip",
		Status:                 "COMPLETED",
		TransactionTimestamp:   time.Unix(**********, 0).UTC(),
		CounterParty: &api.LendingCounterParty{
			DisplayName: "Main account",
			AccountID:   "54321",
			IconURL:     "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			ProxyDetail: &api.ProxyDetail{
				Channel: "RPP_NETWORK",
				Type:    "MSISDN",
				Value:   "+65****8923",
			},
		},
		DrawdownDetails:  nil,
		RepaymentDetails: fetchRepaymentDetailsForOverpaidRepayment(),
	}
}

// nolint:dupl
func fetchRepaymentDetails() *api.RepaymentDetails {
	return &api.RepaymentDetails{
		RepaymentID: "12345",
		TransferID:  "efg123abd",
		PaymentForInstalments: &api.PaymentForInstalments{
			TotalInterestSaved: &api.Money{
				CurrencyCode: "MYR",
				Val:          30000,
			},
			RepaymentSummary: []api.RepaymentSummary{{
				LoanName: "Europe Trip",
				PaidAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          300000,
				},
				RemainingPayableBeforePayment: &api.Money{
					CurrencyCode: "MYR",
					Val:          300000,
				},
				RemainingPayable: &api.Money{
					CurrencyCode: "MYR",
					Val:          200000,
				},
				InterestSavedAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          30000,
				},
			}},
		},
		LastRepaymentOverpaid: nil,
	}
}

// nolint:dupl
func fetchRepaymentDetailsForOverpaidRepayment() *api.RepaymentDetails {
	return &api.RepaymentDetails{
		RepaymentID: "12345",
		TransferID:  "efg1111abd",
		PaymentForOverdue: &api.PaymentForOverdue{
			TotalOverdueInterestPaid: &api.Money{
				CurrencyCode: "MYR",
				Val:          10000,
			},
			RepaymentSummary: []api.RepaymentSummary{{
				LoanName: "Europe trip",
				PaidAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          500000,
				},
				RemainingPayableBeforePayment: &api.Money{
					CurrencyCode: "MYR",
					Val:          300000,
				},
				RemainingPayable: &api.Money{
					CurrencyCode: "MYR",
					Val:          200000,
				},
				OverdueInterestPaidAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          10000,
				},
			}},
		},
		LastRepaymentOverpaid: &api.LastRepaymentOverpaid{
			Title:       "You paid extra with excess credit",
			Description: "You've paid all remaining balance with excess of RM200000. It will be stored safely in your GXS FlexiLoan account. You will be able to transfer it out during public launch very soon.",
		},
	}
}

// GetDrawdownTransactionDetailResponseForLending returns response with status
// nolint:dupl
func GetDrawdownTransactionDetailResponseForLending(status string) *api.GetLendingTransactionDetailResponse {
	return &api.GetLendingTransactionDetailResponse{
		Amount: &api.Money{
			CurrencyCode: "MYR",
			Val:          -1512,
		},
		TransactionDescription: "Loan for Europe Trip",
		Status:                 status,
		StatusDescription:      "",
		TransactionTimestamp:   time.Unix(**********, 0).UTC(),
		CounterParty: &api.LendingCounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
			ProxyDetail: &api.ProxyDetail{
				Channel: "RPP_NETWORK",
				Type:    "MSISDN",
				Value:   "+65****8923",
			},
		},
		DrawdownDetails: &api.DrawDownDetails{
			WithdrawalID: "12345",
			TransferID:   "abc123efg",
			LoanName:     "Europe Trip",
			RepaymentPeriod: &api.RepaymentPeriod{
				TimePeriodInMonths: 10,
				StartDate:          "2023-02-07",
				EndDate:            "2023-06-07",
				InstallmentCount:   10,
			},
			MonthlyRepayment: &api.MonthlyRepayment{
				Descriptions: nil,
			},
			TotalPayment: &api.TotalPayment{
				TotalAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          20435,
				},
				InterestAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          335,
				},
				InterestRate:          3.5,
				EffectiveInterestRate: 5.3520565,
				DisbursedAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          0,
				},
				ProcessingFeeAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          0,
				},
			},
		},
		RepaymentDetails: nil,
	}
}

// GetDrawdownTransactionDetailWithoutPaymentResponseForLending ...
// nolint:dupl
func GetDrawdownTransactionDetailWithoutPaymentResponseForLending(status string) *api.GetLendingTransactionDetailResponse {
	return &api.GetLendingTransactionDetailResponse{
		Amount: &api.Money{
			CurrencyCode: "MYR",
			Val:          -1512,
		},
		TransactionDescription: "Loan for Europe Trip",
		Status:                 status,
		StatusDescription:      "",
		TransactionTimestamp:   time.Unix(**********, 0).UTC(),
		CounterParty: &api.LendingCounterParty{
			IconURL: "https://assets.sgbank.dev/dev/txHistory/images/FundsOut.png",
		},
		DrawdownDetails: &api.DrawDownDetails{
			WithdrawalID: "12345",
			LoanName:     "Europe Trip",
			RepaymentPeriod: &api.RepaymentPeriod{
				TimePeriodInMonths: 10,
				StartDate:          "2023-02-07",
				EndDate:            "2023-06-07",
				InstallmentCount:   10,
			},
			MonthlyRepayment: &api.MonthlyRepayment{
				Descriptions: nil,
			},
			TotalPayment: &api.TotalPayment{
				TotalAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          20435,
				},
				InterestAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          335,
				},
				InterestRate:          3.5,
				EffectiveInterestRate: 5.3520565,
				DisbursedAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          0,
				},
				ProcessingFeeAmount: &api.Money{
					CurrencyCode: "MYR",
					Val:          0,
				},
			},
		},
		RepaymentDetails: nil,
	}
}

// GetRepaymentTransactionDetailResponseForLending ...
// nolint:dupl
func GetRepaymentTransactionDetailResponseForLending() *api.GetLendingTransactionDetailResponse {
	return &api.GetLendingTransactionDetailResponse{
		Amount: &api.Money{
			CurrencyCode: "MYR",
			Val:          1512,
		},
		TransactionDescription: "Payment for Europe Trip",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		TransactionTimestamp:   time.Unix(**********, 0).UTC(),
		CounterParty: &api.LendingCounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			ProxyDetail: &api.ProxyDetail{
				Channel: "RPP_NETWORK",
				Type:    "MSISDN",
				Value:   "+65****8923",
			},
		},
		RepaymentDetails: &api.RepaymentDetails{
			RepaymentID: "12345",
			TransferID:  "abc123efg",
			PaymentForInstalments: &api.PaymentForInstalments{
				TotalInterestSaved: &api.Money{
					CurrencyCode: "MYR",
					Val:          30000,
				},
				RepaymentSummary: []api.RepaymentSummary{{
					LoanName: "Europe Trip",
					PaidAmount: &api.Money{
						CurrencyCode: "MYR",
						Val:          300000,
					},
					RemainingPayableBeforePayment: &api.Money{
						CurrencyCode: "MYR",
						Val:          300000,
					},
					RemainingPayable: &api.Money{
						CurrencyCode: "MYR",
						Val:          200000,
					},
					InterestSavedAmount: &api.Money{
						CurrencyCode: "MYR",
						Val:          30000,
					},
				}},
			},
			PaymentForOverdue:     nil,
			LastRepaymentOverpaid: nil,
		},
	}
}

// GetFailedRepaymentTransactionDetailResponseForLending ...
func GetFailedRepaymentTransactionDetailResponseForLending() *api.GetLendingTransactionDetailResponse {
	return &api.GetLendingTransactionDetailResponse{
		Amount: &api.Money{
			CurrencyCode: "MYR",
			Val:          1512,
		},
		TransactionDescription: "Payment for your loans",
		Status:                 "FAILED",
		StatusDescription:      "",
		TransactionTimestamp:   time.Unix(**********, 0).UTC(),
		CounterParty: &api.LendingCounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			ProxyDetail: &api.ProxyDetail{
				Channel: "RPP_NETWORK",
				Type:    "MSISDN",
				Value:   "+65****8923",
			},
		},
		RepaymentDetails: &api.RepaymentDetails{
			RepaymentID:           "12345",
			TransferID:            "abc123efg",
			PaymentForInstalments: nil,
			PaymentForOverdue:     nil,
			LastRepaymentOverpaid: nil,
		},
	}
}

// GetProcessingRepaymentTransactionDetailResponseForLending returns response with status
// nolint:dupl
func GetProcessingRepaymentTransactionDetailResponseForLending() *api.GetLendingTransactionDetailResponse {
	return &api.GetLendingTransactionDetailResponse{
		Amount: &api.Money{
			CurrencyCode: "MYR",
			Val:          1512,
		},
		TransactionDescription: "Payment for your loans",
		Status:                 "PROCESSING",
		StatusDescription:      "",
		TransactionTimestamp:   time.Unix(**********, 0).UTC(),
		CounterParty: &api.LendingCounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.sgbank.dev/dev/txHistory/images/Repayment.png",
			ProxyDetail: &api.ProxyDetail{
				Channel: "RPP_NETWORK",
				Type:    "MSISDN",
				Value:   "+65****8923",
			},
		},
		RepaymentDetails: &api.RepaymentDetails{
			RepaymentID: "12345",
		},
	}
}

package responses

import (
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/constants"

	"gitlab.myteksi.net/bersama/core-banking/account-service/api"
)

// GetAccountDetailsByAccountIDResponse ...
func GetAccountDetailsByAccountIDResponse() *api.GetAccountResponse {
	return &api.GetAccountResponse{Account: &api.Account{
		Id:                        "test-id",
		Name:                      "child-account",
		ParentAccountID:           "test-parent-id",
		CifNumber:                 "test-cif",
		PermittedCurrencies:       nil,
		AvailableBalance:          nil,
		Status:                    "test-status",
		ProductID:                 "test",
		ProductVariantID:          constants.DepositsAccount,
		ProductSpecificParameters: nil,
		OpeningTimestamp:          time.Time{},
	}}
}

// GetBizAccountDetailsByAccountIDResponse ...
func GetBizAccountDetailsByAccountIDResponse() *api.GetAccountResponse {
	return &api.GetAccountResponse{Account: &api.Account{
		Id:                        "test-id",
		Name:                      "child-account",
		ParentAccountID:           "test-parent-id",
		CifNumber:                 "test-cif",
		PermittedCurrencies:       nil,
		AvailableBalance:          nil,
		Status:                    "test-status",
		ProductID:                 "test",
		ProductVariantID:          constants.BizDepositAccount,
		ProductSpecificParameters: nil,
		OpeningTimestamp:          time.Time{},
	}}
}

// ListCASAAccountsForCustomerDetailResponse ...
func ListCASAAccountsForCustomerDetailResponse() *api.ListCASAAccountsForCustomerDetailResponse {
	return &api.ListCASAAccountsForCustomerDetailResponse{
		Accounts: []api.CASAAccountDetail{
			{
				ParentAccountID: "**********1",
				Id:              "**********",
				AccountName:     "Paris",
			},
		},
		MaxChildAccountLimit: 8,
	}
}

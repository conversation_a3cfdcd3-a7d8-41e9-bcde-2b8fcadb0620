package responses

import (
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetPocketActivitiesFirstPageResponse ...
func GetPocketActivitiesFirstPageResponse() *api.GetPocketActivitiesResponse {
	locale := utils.GetLocale()
	return &api.GetPocketActivitiesResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/*************/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDEsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDEsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "abc123efg",
			DisplayName:       "Funds added",
			IconURL:           constants.IconURLMap[constants.PocketFundingTransactionSubType],
			Amount:            13,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
		}, {
			TransactionID:     "efg123abd",
			DisplayName:       "Funds added",
			IconURL:           constants.IconURLMap[constants.PocketFundingTransactionSubType],
			Amount:            513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
		}},
	}
}

// GetPocketActivitiesSinglePageResponse ...
// nolint:dupl
func GetPocketActivitiesSinglePageResponse() *api.GetPocketActivitiesResponse {
	locale := utils.GetLocale()
	return &api.GetPocketActivitiesResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "abc123efg",
			DisplayName:       "Funds added",
			IconURL:           constants.IconURLMap[constants.PocketFundingTransactionSubType],
			Amount:            13,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
		}, {
			TransactionID:     "efg123abd",
			DisplayName:       "Funds added",
			IconURL:           constants.IconURLMap[constants.PocketFundingTransactionSubType],
			Amount:            513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
		}, {
			TransactionID:     "efg1111abd",
			DisplayName:       "Funds withdrawn",
			IconURL:           constants.IconURLMap[constants.PocketWithdrawalTransactionSubType],
			Amount:            -312,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
		}},
	}
}

// GetPocketActivitiesLastPageResponse ...
func GetPocketActivitiesLastPageResponse() *api.GetPocketActivitiesResponse {
	locale := utils.GetLocale()
	return &api.GetPocketActivitiesResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "/v1/accounts/*************/transactions?pageSize=2&endingAfter=MjAyMS0wOC0yMFQwMjozMTo0MCwzLDEsODAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWY=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjozMTo0MCwzLDEsODAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWY=",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "efg1111abd",
			DisplayName:       "Funds withdrawn",
			IconURL:           constants.IconURLMap[constants.PocketWithdrawalTransactionSubType],
			Amount:            -312,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
		}},
	}
}

// GetPocketActivitiesBackwardScrollingPrevPageResponse ...
//
//nolint:dupl
func GetPocketActivitiesBackwardScrollingPrevPageResponse() *api.GetPocketActivitiesResponse {
	locale := utils.GetLocale()
	return &api.GetPocketActivitiesResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/*************/transactions?pageSize=3&startingBefore=MjAyMS0wOC0yMFQwMjozMTo0MCwzLDEsODAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWY=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjozMTo0MCwzLDEsODAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWY=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "abc123efg",
			DisplayName:       "Funds added",
			IconURL:           constants.IconURLMap[constants.PocketFundingTransactionSubType],
			Amount:            13,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
		}, {
			TransactionID:     "efg123abd",
			DisplayName:       "Funds added",
			IconURL:           constants.IconURLMap[constants.PocketFundingTransactionSubType],
			Amount:            513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
		}, {
			TransactionID:     "efg1111abd",
			DisplayName:       "Funds withdrawn",
			IconURL:           constants.IconURLMap[constants.PocketWithdrawalTransactionSubType],
			Amount:            -312,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
		},
		},
	}
}

// GetPocketActivitiesPrevNextCursorResponse ...
func GetPocketActivitiesPrevNextCursorResponse() *api.GetPocketActivitiesResponse {
	locale := utils.GetLocale()
	return &api.GetPocketActivitiesResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/*************/transactions?pageSize=1&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDEsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDEsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "/v1/accounts/*************/transactions?pageSize=1&endingAfter=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDEsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDEsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
		},
		Data: []api.TransactionHistoryResponse{{
			TransactionID:     "efg123abd",
			DisplayName:       "Funds added",
			IconURL:           constants.IconURLMap[constants.PocketFundingTransactionSubType],
			Amount:            513,
			Status:            "COMPLETED",
			Currency:          locale.Currency,
			CreationTimestamp: time.Unix(**********, 0).UTC(),
		}},
	}
}

// GetPocketActivitiesEmptyResponse returns an empty response
func GetPocketActivitiesEmptyResponse() *api.GetPocketActivitiesResponse {
	return &api.GetPocketActivitiesResponse{
		Links: map[string]string{"prev": "", "prevCursorID": "", "next": "", "nextCursorID": ""},
		Data:  []api.TransactionHistoryResponse{},
	}
}

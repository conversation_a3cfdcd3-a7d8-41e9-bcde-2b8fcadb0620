package responses

import (
	"gitlab.myteksi.net/dbmy/transaction-history/api"

	"gitlab.myteksi.net/dakota/transaction-history/test/resources"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
)

// GetInternalTransactionDetailResponse  returns a sample outputs for GetInternalTransactionDetailResponse requests.
// nolint: funlen
func GetInternalTransactionDetailResponse() []*api.GetInternalTransactionDetailResponse {
	locale := utils.GetLocale()
	response1 := &api.GetInternalTransactionDetailResponse{
		Amount:        13,
		Currency:      locale.Currency,
		TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		CreditOrDebit: "credit",
		Status:        "COMPLETED",
		CounterParty: &api.CounterParty{
			DisplayName: "Userno2",
			TransactionDetails: map[string]string{
				"bank_name":          "Affin Bank Berhad",
				"swift_code":         "PHBMMYKL",
				"txn_scenario":       "FUND_IN",
				"account_id":         "54321",
				"purposeCode":        "",
				"beneficiaryCountry": "",
				"residentStatus":     "",
				"relationshipCode":   "",
			},
		},
		TransactionTimestamp:   &t,
		TransactionType:        "FUND_IN",
		TransactionSubtype:     "RPP_NETWORK",
		ExternalTransactionID:  "12345",
		TransactionDescription: "Transfer from Userno2",
	}
	response2 := &api.GetInternalTransactionDetailResponse{
		Amount:        13,
		Currency:      locale.Currency,
		TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		CreditOrDebit: "credit",
		Status:        "COMPLETED",
		CounterParty: &api.CounterParty{
			DisplayName: "Userno2",
			TransactionDetails: map[string]string{
				"bank_name":          "Affin Bank Berhad",
				"swift_code":         "PHBMMYKL",
				"txn_scenario":       "FUND_IN",
				"account_id":         "54321",
				"purposeCode":        "",
				"beneficiaryCountry": "",
				"residentStatus":     "",
				"relationshipCode":   "",
			},
		},
		TransactionTimestamp:   &t,
		TransactionType:        "FUND_IN",
		TransactionSubtype:     "RPP_NETWORK",
		ExternalTransactionID:  "12345",
		TransactionDescription: "Transfer from Userno2",
	}
	response3 := &api.GetInternalTransactionDetailResponse{
		Amount:        13,
		Currency:      locale.Currency,
		TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		CreditOrDebit: "credit",
		Status:        "COMPLETED",
		CounterParty: &api.CounterParty{
			DisplayName: "Userno2",
			TransactionDetails: map[string]string{
				"swift_code":          "PHBMMYKL",
				"txn_scenario":        "RECEIVE_MONEY",
				"recipient_reference": "Dinner",
				"account_id":          "54321",
				"purposeCode":         "",
				"beneficiaryCountry":  "",
				"residentStatus":      "",
				"relationshipCode":    "",
			},
		},
		TransactionTimestamp:   &t,
		TransactionType:        "RECEIVE_MONEY",
		TransactionSubtype:     "RPP_NETWORK",
		ExternalTransactionID:  "12345",
		TransactionDescription: "Transfer from Userno2",
	}
	response4 := &api.GetInternalTransactionDetailResponse{
		Amount:        -13,
		Currency:      locale.Currency,
		TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		CreditOrDebit: "debit",
		Status:        "COMPLETED",
		CounterParty: &api.CounterParty{
			DisplayName: "Userno2",
			TransactionDetails: map[string]string{
				"txn_scenario":        "SEND_MONEY",
				"recipient_reference": "Dinner",
				"payment_description": "extra details",
				"type":                "Account number",
				"account_id":          "54321",
				"swift_code":          "PHBMMYKL",
				"service_type":        "",
				"purposeCode":         "",
				"beneficiaryCountry":  "",
				"residentStatus":      "",
				"relationshipCode":    "",
			},
		},
		TransactionTimestamp:   &t,
		TransactionType:        "SEND_MONEY",
		TransactionSubtype:     "RPP_NETWORK",
		ExternalTransactionID:  "12345",
		TransactionDescription: "Transfer to Userno2",
	}
	response5 := &api.GetInternalTransactionDetailResponse{
		Amount:        -13,
		Currency:      locale.Currency,
		TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		CreditOrDebit: "debit",
		Status:        "FAILED",
		CounterParty: &api.CounterParty{
			DisplayName: "Userno2",
			TransactionDetails: map[string]string{
				"swift_code":          "",
				"recipient_reference": "",
				"account_id":          "54321",
				"txn_scenario":        "TRANSFER_MONEY",
				"service_type":        "",
				"purposeCode":         "",
				"beneficiaryCountry":  "",
				"residentStatus":      "",
				"relationshipCode":    "",
			},
		},
		TransactionTimestamp:   &t,
		TransactionType:        "TRANSFER_MONEY",
		TransactionSubtype:     "INTRABANK",
		ExternalTransactionID:  "",
		TransactionDescription: "Transfer to Userno2",
	}
	response6 := &api.GetInternalTransactionDetailResponse{
		Amount:        13,
		Currency:      locale.Currency,
		TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		CreditOrDebit: "credit",
		Status:        "PROCESSING",
		CounterParty: &api.CounterParty{
			DisplayName: "Userno2",
			TransactionDetails: map[string]string{
				"bank_name":          "Affin Bank Berhad",
				"swift_code":         "PHBMMYKL",
				"txn_scenario":       "FUND_IN",
				"account_id":         "54321",
				"purposeCode":        "",
				"beneficiaryCountry": "",
				"residentStatus":     "",
				"relationshipCode":   "",
			},
		},
		TransactionTimestamp:   &t,
		TransactionType:        "FUND_IN",
		TransactionSubtype:     "RPP_NETWORK",
		ExternalTransactionID:  "",
		TransactionDescription: "Transfer from Userno2",
	}
	response7 := &api.GetInternalTransactionDetailResponse{
		Amount: 13,

		Currency:      locale.Currency,
		TransactionID: "9007e9c10ea841c593bee2e65df599ed",
		CreditOrDebit: "credit",
		Status:        "COMPLETED",
		CounterParty: &api.CounterParty{
			DisplayName: "Userno2",
			TransactionDetails: map[string]string{
				"bank_name":          "Affin Bank Berhad",
				"swift_code":         "XXX",
				"txn_scenario":       "FUND_IN",
				"account_id":         "54321",
				"pairing_id":         "000",
				"purposeCode":        "",
				"beneficiaryCountry": "",
				"residentStatus":     "",
				"relationshipCode":   "",
			},
		},
		TransactionTimestamp:   &t,
		TransactionType:        "FUND_IN",
		TransactionSubtype:     "RPP_NETWORK",
		ExternalTransactionID:  "54321",
		CashAccountCode:        "CASA",
		ServiceType:            "QR_TRANSFER",
		TransactionDescription: "Transfer from Userno2",
		Account: &api.Account{
			DisplayName: "account name",
		},
	}
	response8 := &api.GetInternalTransactionDetailResponse{
		Amount:                         -13,
		IsPartialSettlement:            false,
		CapturedAmountTillDate:         0,
		CapturedOriginalAmountTillDate: 0,
		Currency:                       locale.Currency,
		TransactionID:                  "9007e9c10ea841c593bee2e65df599ed",
		CreditOrDebit:                  "debit",
		Status:                         "PROCESSING",
		CounterParty: &api.CounterParty{
			DisplayName: "ATM withdrawal",
			TransactionDetails: map[string]string{
				"txn_scenario": "SPEND_CARD_ATM",
			},
		},
		TransactionTimestamp:   &resources.CardTransactionDetailSample().CreationTimestamp,
		TransactionType:        "SPEND_CARD_ATM",
		TransactionSubtype:     "MASTERCARD",
		TransactionDescription: "ATM withdrawal",
		FormattedLocalAmount:   "RM0.13",
		AccountID:              "12345",
		CardTransactionDetail: &api.CardTransactionDetail{
			CardID:                   resources.CardTransactionDetailSample().CardID,
			TailCardNumber:           "**9350",
			SettlementDate:           &resources.CardTransactionDetailSample().ValueTimestamp,
			ExchangeRate:             "",
			BankFee:                  "",
			Mcc:                      "",
			MaskedCardNumber:         "",
			TransactionCountry:       "MALAYSIA",
			TransactionCategory:      "",
			TransactionSubCategory:   "",
			NetworkID:                "",
			ThreeDsValidation:        "",
			PinValidation:            "",
			RetrievalReferenceNumber: "",
			CardProxyNumber:          "",
			MerchantName:             resources.CardTransactionDetailSample().MerchantDescription,
		},
	}

	response9 := &api.GetInternalTransactionDetailResponse{
		Amount:        600,
		Currency:      locale.Currency,
		TransactionID: "mabcd12345",
		CreditOrDebit: "credit",
		Status:        "COMPLETED",
		CounterParty: &api.CounterParty{
			DisplayName: "MooMoo",
			TransactionDetails: map[string]string{
				"txn_scenario":       "CASH_OUT",
				"purposeCode":        "",
				"beneficiaryCountry": "",
				"residentStatus":     "",
				"relationshipCode":   "",
			},
		},
		TransactionTimestamp:   &t,
		TransactionType:        "CASH_OUT",
		TransactionSubtype:     "MOOMOO",
		ExternalTransactionID:  "",
		TransactionDescription: "Withdrawal from MooMoo",
	}
	return []*api.GetInternalTransactionDetailResponse{response1, response2, response3, response4, response5, response6, response7, response8, response9}
}

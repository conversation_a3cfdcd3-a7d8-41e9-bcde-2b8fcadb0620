package responses

import (
	"encoding/json"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"

	accountServiceAPI "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetParentAccountDetailsByAccountIDResponse ...
func GetParentAccountDetailsByAccountIDResponse() *accountServiceAPI.GetAccountResponse {
	return &accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
		Id:                        "test-id",
		Name:                      "parent-account-id",
		ParentAccountID:           "",
		CifNumber:                 "test-cif",
		PermittedCurrencies:       nil,
		AvailableBalance:          nil,
		Status:                    "test-status",
		ProductID:                 "test",
		ProductVariantID:          "test",
		ProductSpecificParameters: nil,
		OpeningTimestamp:          time.Time{},
	}}
}

// GetOpsSearchFirstPageResponses ...
func GetOpsSearchFirstPageResponses() *api.GetOpsSearchResponse {
	response := GetOpsSearchFirstPageResponseForListTransaction()
	return response
}

// GetOpsSearchFirstPageResponseForListTransaction ...
// nolint: dupl,funlen
func GetOpsSearchFirstPageResponseForListTransaction() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "abc123efg",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "efg123abd",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -513,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"payment_description": "",
					"recipient_reference": "",
					"txn_scenario":        "SEND_MONEY",
					"type":                "Account number",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"payment_description": "",
						"recipient_reference": "",
						"txn_scenario":        "SEND_MONEY",
						"type":                "Account number",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "SEND_MONEY",
			TransactionSubtype:     "RPP_NETWORK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchLastPageResponseForListResponse  ...
// nolint : dupl
func GetOpsSearchLastPageResponseForListResponse() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "/v1/accounts/**********/transactions?pageSize=2&endingAfter=MjAyMS0wOC0yMFQwMjoxMDowMCwxLDMsODAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWY=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMDowMCwxLDMsODAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWY=",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "efg1111abd",
			TransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ExternalTransactionID: "xyz123",
			Amount:                1512,
			CreditOrDebit:         "credit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer from User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchOnlyPageResponse ...
// nolint:dupl, funlen
func GetOpsSearchOnlyPageResponse() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "abc123efg",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "efg123abd",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -513,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"payment_description": "",
					"recipient_reference": "",
					"txn_scenario":        "SEND_MONEY",
					"type":                "Account number",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"payment_description": "",
						"recipient_reference": "",
						"txn_scenario":        "SEND_MONEY",
						"type":                "Account number",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "SEND_MONEY",
			TransactionSubtype:     "RPP_NETWORK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "efg1111abd",
			TransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ExternalTransactionID: "xyz123",
			Amount:                1512,
			CreditOrDebit:         "credit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer from User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchCardsTxnOnlyPageResponse ...
// nolint:dupl, funlen
func GetOpsSearchCardsTxnOnlyPageResponse() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "abc123efg",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:                        "efg123abd",
			TransactionID:                  "9007e9c10ea841c593bee2e65df599ed",
			Amount:                         -513,
			IsPartialSettlement:            false,
			CapturedAmountTillDate:         0,
			CapturedOriginalAmountTillDate: 0,
			CreditOrDebit:                  "debit",
			Status:                         "COMPLETED",
			Currency:                       locale.Currency,
			CounterParty: &api.CounterParty{
				DisplayName: "MERCHANT2",
			},
			CounterParties: []api.CounterParty{
				{
					DisplayName: "MERCHANT2",
				},
			},
			TransactionType:        "SPEND_CARD_PRESENT",
			TransactionSubtype:     "PAYNET_MYDEBIT",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "GX Card payment to MERCHANT2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "efg1111abd",
			TransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ExternalTransactionID: "xyz123",
			Amount:                1512,
			CreditOrDebit:         "credit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer from User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchGrabTxnOnlyPageResponse ...
// nolint:dupl, funlen
func GetOpsSearchGrabTxnOnlyPageResponse() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "abc123efg",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "efg123abd",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -513,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				DisplayName:   "Grab Food",
				AccountNumber: "",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "SPEND_MONEY",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					DisplayName:   "Grab Food",
					AccountNumber: "",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "SPEND_MONEY",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "SPEND_MONEY",
			TransactionSubtype:     "GRAB",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Payment to Grab Food",
			TransactionRemarks:     "some remarks",
			AccountID:              "**********",
		}, {
			BatchID:               "efg1111abd",
			TransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ExternalTransactionID: "xyz123",
			Amount:                1512,
			CreditOrDebit:         "credit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer from User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchBackwardScrollingPrevPageResponse ...
// nolint: dupl,funlen
func GetOpsSearchBackwardScrollingPrevPageResponse() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "abc123efg",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "efg123abd",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -513,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"payment_description": "",
					"recipient_reference": "",
					"txn_scenario":        "SEND_MONEY",
					"type":                "Account number",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"payment_description": "",
						"recipient_reference": "",
						"txn_scenario":        "SEND_MONEY",
						"type":                "Account number",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "SEND_MONEY",
			TransactionSubtype:     "RPP_NETWORK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchBackwardScrollingPrevPageOneResponse ...
// nolint: dupl,funlen
func GetOpsSearchBackwardScrollingPrevPageOneResponse() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjozMTo0MCwzLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjozMTo0MCwzLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "abc123efg",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchPrevNextBothExistResponse ...
// nolint : dupl
func GetOpsSearchPrevNextBothExistResponse() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=1&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "/v1/accounts/**********/transactions?pageSize=1&endingAfter=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"prevCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "efg123abd",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -513,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"payment_description": "",
					"recipient_reference": "",
					"txn_scenario":        "SEND_MONEY",
					"type":                "Account number",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"payment_description": "",
						"recipient_reference": "",
						"txn_scenario":        "SEND_MONEY",
						"type":                "Account number",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "SEND_MONEY",
			TransactionSubtype:     "RPP_NETWORK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchByExternalIDForListTransaction ...
// nolint: dupl
func GetOpsSearchByExternalIDForListTransaction() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "efg1111abd",
			TransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ExternalTransactionID: "xyz123",
			Amount:                1512,
			CreditOrDebit:         "credit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "RECEIVE_MONEY",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "RECEIVE_MONEY",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "RECEIVE_MONEY",
			TransactionSubtype:     "RPP_NETWORK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer from User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchPocketTxnsResponse ...
// nolint : dupl
func GetOpsSearchPocketTxnsResponse() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{
			{
				TransactionID:         "9007e9c10ea841c593bee2e65df599ef",
				BatchID:               "test-client-batch-id1",
				ExternalTransactionID: "",
				Amount:                -513,
				CreditOrDebit:         "debit",
				Status:                "COMPLETED",
				Currency:              locale.Currency,
				CounterParty: &api.CounterParty{
					SwiftCode:     "",
					AccountNumber: "**********000",
					DisplayName:   "Paris1",
					TransactionDetails: map[string]string{
						"txn_scenario": "FROM_MAIN_TO_POCKET",
						"service_type": "",
					},
				},
				CounterParties: []api.CounterParty{
					{
						SwiftCode:     "",
						AccountNumber: "**********000",
						DisplayName:   "Paris1",
						TransactionDetails: map[string]string{
							"txn_scenario": "FROM_MAIN_TO_POCKET",
							"service_type": "",
						},
					},
				},
				TransactionType:        "TRANSFER_MONEY",
				TransactionSubtype:     constants.PocketFundingTransactionSubType,
				TransactionTimestamp:   time.Unix(**********, 0).UTC(),
				TransactionDescription: "Transfer to Paris1 Pocket",
				TransactionRemarks:     "",
				AccountID:              "**********",
			},
			{
				TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
				BatchID:               "test-client-batch-id4",
				ExternalTransactionID: "",
				Amount:                100,
				CreditOrDebit:         "credit",
				Status:                "COMPLETED",
				Currency:              locale.Currency,
				CounterParty: &api.CounterParty{
					SwiftCode:     "",
					AccountNumber: "**********000",
					DisplayName:   "Paris1",
					TransactionDetails: map[string]string{
						"txn_scenario": "FROM_POCKET_TO_MAIN",
						"service_type": "",
					},
				},
				CounterParties: []api.CounterParty{
					{
						SwiftCode:     "",
						AccountNumber: "**********000",
						DisplayName:   "Paris1",
						TransactionDetails: map[string]string{
							"txn_scenario": "FROM_POCKET_TO_MAIN",
							"service_type": "",
						},
					},
				},
				TransactionType:        "TRANSFER_MONEY",
				TransactionSubtype:     constants.PocketWithdrawalTransactionSubType,
				TransactionTimestamp:   time.Unix(**********, 0).UTC(),
				TransactionDescription: "Withdrawal from Paris1 Pocket",
				TransactionRemarks:     "",
				AccountID:              "**********",
			},
		},
	}
}

// FetchOpsSearchPaymentDetailHappyPath ...
// nolint: dupl
func FetchOpsSearchPaymentDetailHappyPath() map[storage.PaymentDataKey]*storage.PaymentDetail {
	locale := utils.GetLocale()
	account, _ := json.Marshal(dto.AccountDetail{Number: "**********", DisplayName: "User No1"})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "User No2"})
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123", "remarks": ""})
	return map[storage.PaymentDataKey]*storage.PaymentDetail{
		{PaymentBatchID: "abc123efg", AccountID: "**********"}: {
			ID:                    3,
			TransactionID:         "abc123efg",
			Amount:                -20,
			Currency:              locale.Currency,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
			Metadata:              metadata,
		},
		{PaymentBatchID: "efg123abd", AccountID: "**********"}: {
			ID:                    2,
			TransactionID:         "efg123abd",
			Currency:              locale.Currency,
			Amount:                -20,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
			Metadata:              metadata,
		},
		{PaymentBatchID: "efg1111abd", AccountID: "**********"}: {
			ID:                    1,
			TransactionID:         "efg1111abd",
			Currency:              locale.Currency,
			Amount:                20,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
			Metadata:              metadata,
		},
	}
}

// FetchOpsSearchGetCustomerTxnRowsForRPPTxn ...
func FetchOpsSearchGetCustomerTxnRowsForRPPTxn() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123effasddag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82dsd",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "RPP_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
	}
}

// FetchOpsSearchGetCustomerTxnRowsForIntrabankTxn ...
func FetchOpsSearchGetCustomerTxnRowsForIntrabankTxn() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          1,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
	}
}

// GetOpsSearchFirstPageResponseWithRewardsForListTransaction ...
// nolint: dupl,funlen
func GetOpsSearchFirstPageResponseWithRewardsForListTransaction() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31",
			"nextCursorID": "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "abc123efg",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:       "efg123abd",
			TransactionID: "9007e9c10ea841c593bee2e65df599ed",
			Amount:        490,
			CreditOrDebit: "credit",
			Status:        "COMPLETED",
			Currency:      locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "Grab Unlimited Cashback",
				TransactionDetails: map[string]string{
					"recipient_reference": "Grab Unlimited Rewards",
					"txn_scenario":        "BANK_INITIATED",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "Grab Unlimited Cashback",
					TransactionDetails: map[string]string{
						"recipient_reference": "Grab Unlimited Rewards",
						"txn_scenario":        "BANK_INITIATED",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "REWARDS_CASHBACK",
			TransactionSubtype:     "BANK_INITIATED",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Grab Unlimited Cashback",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchFirstPageResponseWithQrPaymentForListTransaction ...
// nolint: dupl, funlen
func GetOpsSearchFirstPageResponseWithQrPaymentForListTransaction() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "abc123efg",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "TRANSFER_MONEY",
			TransactionSubtype:     "INTRABANK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "qwer12345",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "abc123",
			Amount:                -490,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "SHOPEE MALAYSIA",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "PAYMENT",
					"service_type":        "QR_PAYMENT",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "SHOPEE MALAYSIA",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "PAYMENT",
						"service_type":        "QR_PAYMENT",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "PAYMENT",
			TransactionSubtype:     "RPP_NETWORK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "QR payment to SHOPEE MALAYSIA",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "qwer789",
			TransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ExternalTransactionID: "efg123",
			Amount:                -800,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"payment_description": "",
					"txn_scenario":        "SEND_MONEY",
					"service_type":        "QR_TRANSFER",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"payment_description": "",
						"txn_scenario":        "SEND_MONEY",
						"service_type":        "QR_TRANSFER",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "SEND_MONEY",
			TransactionSubtype:     "RPP_NETWORK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "QR transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchFirstPageResponseWithLendingForListTransaction ...
// nolint: dupl, funlen
func GetOpsSearchFirstPageResponseWithLendingForListTransaction() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "lending-txn-1",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "************",
				DisplayName:   "Loan 1",
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "************",
					DisplayName:   "Loan 1",
				},
				{
					SwiftCode:     "",
					AccountNumber: "************",
					DisplayName:   "Loan 2",
				},
			},
			TransactionType:        constants.RepaymentTransactionType,
			TransactionSubtype:     constants.IntrabankTransactionSubtype,
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "FlexiCredit repayment",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "qwer12345",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "abc123",
			Amount:                -490,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "SHOPEE MALAYSIA",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "PAYMENT",
					"service_type":        "QR_PAYMENT",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "SHOPEE MALAYSIA",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "PAYMENT",
						"service_type":        "QR_PAYMENT",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "PAYMENT",
			TransactionSubtype:     "RPP_NETWORK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "QR payment to SHOPEE MALAYSIA",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "qwer789",
			TransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ExternalTransactionID: "efg123",
			Amount:                -800,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"payment_description": "",
					"txn_scenario":        "SEND_MONEY",
					"service_type":        "QR_TRANSFER",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"payment_description": "",
						"txn_scenario":        "SEND_MONEY",
						"service_type":        "QR_TRANSFER",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "SEND_MONEY",
			TransactionSubtype:     "RPP_NETWORK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "QR transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchFirstPageResponseWithBizLendingForListTransaction ...
// nolint: dupl,funlen
func GetOpsSearchFirstPageResponseWithBizLendingForListTransaction() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "lending-txn-1",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "************",
				DisplayName:   "Loan 1",
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "************",
					DisplayName:   "Loan 1",
				},
				{
					SwiftCode:     "",
					AccountNumber: "************",
					DisplayName:   "Loan 2",
				},
			},
			TransactionType:        constants.RepaymentTransactionType,
			TransactionSubtype:     constants.IntrabankTransactionSubtype,
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Biz FlexiLoan repayment",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "qwer12345",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "abc123",
			Amount:                -490,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "SHOPEE MALAYSIA",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "PAYMENT",
					"service_type":        "QR_PAYMENT",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "SHOPEE MALAYSIA",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "PAYMENT",
						"service_type":        "QR_PAYMENT",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        "PAYMENT",
			TransactionSubtype:     "RPP_NETWORK",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "QR payment to SHOPEE MALAYSIA",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchFirstPageResponseWithBizForListTransaction ...
func GetOpsSearchFirstPageResponseWithBizForListTransaction() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "biz-txn-1",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "54321",
				DisplayName:   "User No2",
				TransactionDetails: map[string]string{
					"recipient_reference": "",
					"txn_scenario":        "TRANSFER_MONEY",
					"service_type":        "",
					"purposeCode":         "",
					"beneficiaryCountry":  "",
					"residentStatus":      "",
					"relationshipCode":    "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "54321",
					DisplayName:   "User No2",
					TransactionDetails: map[string]string{
						"recipient_reference": "",
						"txn_scenario":        "TRANSFER_MONEY",
						"service_type":        "",
						"purposeCode":         "",
						"beneficiaryCountry":  "",
						"residentStatus":      "",
						"relationshipCode":    "",
					},
				},
			},
			TransactionType:        constants.TransferMoneyTxType,
			TransactionSubtype:     constants.IntrabankTransactionSubtype,
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to User No2",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

// GetOpsSearchFirstPageResponseWithLendingForListByBatchIDTransaction ...
// nolint: dupl, funlen
func GetOpsSearchFirstPageResponseWithLendingForListByBatchIDTransaction() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "lending-txn-1",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "************",
				DisplayName:   "Loan 1",
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "************",
					DisplayName:   "Loan 1",
				},
				{
					SwiftCode:     "",
					AccountNumber: "************",
					DisplayName:   "Loan 2",
				},
			},
			TransactionType:        constants.RepaymentTransactionType,
			TransactionSubtype:     constants.IntrabankTransactionSubtype,
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "FlexiCredit repayment",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}, {
			BatchID:               "lending-txn-1",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "",
			Amount:                13,
			CreditOrDebit:         "credit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "************",
				DisplayName:   "Loan 1",
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "************",
					DisplayName:   "Loan 1",
				},
				{
					SwiftCode:     "",
					AccountNumber: "************",
					DisplayName:   "Loan 2",
				},
			},
			TransactionType:        constants.RepaymentTransactionType,
			TransactionSubtype:     constants.IntrabankTransactionSubtype,
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "FlexiCredit repayment",
			TransactionRemarks:     "",
			AccountID:              "************",
		}},
	}
}

// FetchOpsSearchGetCustomerTxnRowsForLendingTxn ...
// nolint: funlen
func FetchOpsSearchGetCustomerTxnRowsForLendingTxn() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          1,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdagffff",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           constants.LendingDomain,
			TransactionType:             constants.DrawdownTransactionType,
			TransactionSubtype:          constants.IntrabankTransactionSubtype,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           constants.LendingDomain,
			TransactionType:             constants.RepaymentTransactionType,
			TransactionSubtype:          constants.IntrabankTransactionSubtype,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdagffff",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           constants.LendingDomain,
			TransactionType:             constants.DrawdownTransactionType,
			TransactionSubtype:          constants.IntrabankTransactionSubtype,
			AccountID:                   "************",
			AccountAddress:              "DISBURSEMENT_CONTRA",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          4,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82dsd",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           constants.LendingDomain,
			TransactionType:             constants.RepaymentTransactionType,
			TransactionSubtype:          constants.IntrabankTransactionSubtype,
			AccountID:                   "************",
			AccountAddress:              "REPAYMENT_MADE",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
	}
}

// GetOpsSearchResponseForListMooMooTransaction ...
// nolint: dupl,funlen
func GetOpsSearchResponseForListMooMooTransaction() *api.GetOpsSearchResponse {
	locale := utils.GetLocale()
	return &api.GetOpsSearchResponse{
		Links: map[string]string{
			"next":         "",
			"nextCursorID": "",
			"prev":         "",
			"prevCursorID": "",
		},
		Data: []api.OpsSearchResponse{{
			BatchID:               "abc123efg",
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ExternalTransactionID: "xyz123",
			Amount:                -13,
			CreditOrDebit:         "debit",
			Status:                "COMPLETED",
			Currency:              locale.Currency,
			CounterParty: &api.CounterParty{
				SwiftCode:     "",
				AccountNumber: "",
				DisplayName:   "Moomoo",
				TransactionDetails: map[string]string{
					"txn_scenario":       "SPEND_MONEY",
					"purposeCode":        "",
					"beneficiaryCountry": "",
					"residentStatus":     "",
					"relationshipCode":   "",
				},
			},
			CounterParties: []api.CounterParty{
				{
					SwiftCode:     "",
					AccountNumber: "",
					DisplayName:   "Moomoo",
					TransactionDetails: map[string]string{
						"txn_scenario":       "SPEND_MONEY",
						"purposeCode":        "",
						"beneficiaryCountry": "",
						"residentStatus":     "",
						"relationshipCode":   "",
					},
				},
			},
			TransactionType:        "SPEND_MONEY",
			TransactionSubtype:     "MOOMOO",
			TransactionTimestamp:   time.Unix(**********, 0).UTC(),
			TransactionDescription: "Transfer to Moomoo",
			TransactionRemarks:     "",
			AccountID:              "**********",
		}},
	}
}

package responses

import (
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetCASAInterestEarnedValidResponse ...
func GetCASAInterestEarnedValidResponse() *api.GetCASAInterestEarnedResponse {
	locale := utils.GetLocale()
	return &api.GetCASAInterestEarnedResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          10,
		},
	}
}

// GetCASAInterestEarnedEmptyResponse ...
func GetCASAInterestEarnedEmptyResponse() *api.GetCASAInterestEarnedResponse {
	locale := utils.GetLocale()
	return &api.GetCASAInterestEarnedResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          0,
		},
	}
}

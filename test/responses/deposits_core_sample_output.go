package responses

import (
	"encoding/json"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// CreateBatchLevelEntryHappyPathResponse ...
func CreateBatchLevelEntryHappyPathResponse() (*storage.TransactionsData, error) {
	batchDetail, _ := json.Marshal(nil)
	return &storage.TransactionsData{
		TmPostingInstructionBatchID: "23aa2045-4ea7-422f-b72f-6e8b44931009",
		ClientBatchID:               "8720867d-59e4-4050-b08f-b23326632da9",
		BatchDetails:                batchDetail,
		BatchValueTimestamp:         time.Unix(1629426700, 0).UTC(),
		BatchInsertionTimestamp:     time.Unix(1629426700, 0).UTC(),
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchErrorType:              "",
		BatchErrorMessage:           "",
		BatchRemarks:                "Happy Payment Remarks",
	}, nil
}

// CreateTransactionLevelEntryHappyPathResponse ...
func CreateTransactionLevelEntryHappyPathResponse() (*storage.TransactionsData, error) {
	transactionData := getTestTransactionsData()
	transactionData.TransactionDomain = "DEPOSITS"
	return transactionData, nil
}

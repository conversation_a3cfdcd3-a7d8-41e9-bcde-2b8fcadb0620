package responses

import (
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetPocketInterestEarnedValidResponse ...
func GetPocketInterestEarnedValidResponse() *api.GetPocketInterestEarnedResponse {
	locale := utils.GetLocale()
	return &api.GetPocketInterestEarnedResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          5,
		},
	}
}

// GetPocketInterestEarnedEmptyResponse ...
func GetPocketInterestEarnedEmptyResponse() *api.GetPocketInterestEarnedResponse {
	locale := utils.GetLocale()
	return &api.GetPocketInterestEarnedResponse{
		TotalInterestEarned: &api.Money{
			CurrencyCode: locale.Currency,
			Val:          0,
		},
	}
}

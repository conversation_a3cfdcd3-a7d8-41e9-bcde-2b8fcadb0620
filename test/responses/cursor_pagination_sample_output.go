package responses

import (
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/dto"
)

// EncodeCursorHappyPathResponse ...
func EncodeCursorHappyPathResponse() string {
	return "MjAyMS0wOC0yMFQwMjoxMDowMFosMSwzLGE0MWQ4YjViY2ExMTRlMTNiZWNlNjAxY2Y1MjlkYzhm"
}

// DecodeCursorHappyPathResponse ...
func DecodeCursorHappyPathResponse() dto.PaginationCursor {
	return dto.PaginationCursor{
		Date:               time.Unix(**********, 0).UTC().Format("2006-01-02T15:04:05Z07:00"),
		ID:                 1,
		FirstTransactionID: 3,
		TransactionID:      "a41d8b5bca114e13bece601cf529dc8f",
	}
}

// NextPageExistResponse ...
func NextPageExistResponse() (string, string) {
	nextLink := "/v1/accounts/**********/transactions?pageSize=2&startingBefore=MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31"
	nextCursorID := "MjAyMS0wOC0yMFQwMjoxMTo0MCwyLDMsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ="
	return nextLink, nextCursorID
}

// PrevPageExistResponse ...
func PrevPageExistResponse() (string, string) {
	prevLink := "/v1/accounts/**********/transactions?pageSize=2&endingAfter=MjAyMS0wOC0yMFQwMjozMTo0MCwzLDIsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ=&startDate=2021-08-01&endDate=2021-08-31"
	prevCursorID := "MjAyMS0wOC0yMFQwMjozMTo0MCwzLDIsOTAwN2U5YzEwZWE4NDFjNTkzYmVlMmU2NWRmNTk5ZWQ="
	return prevLink, prevCursorID
}

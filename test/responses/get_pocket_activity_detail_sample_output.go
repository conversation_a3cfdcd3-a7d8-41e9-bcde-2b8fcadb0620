package responses

import (
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

// GetPocketActivityDetailResponse  returns a sample outputs for GetPocketActivityDetail requests.
func GetPocketActivityDetailResponse() []*api.GetPocketActivityDetailResponse {
	locale := utils.GetLocale()
	response1 := &api.GetPocketActivityDetailResponse{
		Amount:                 513,
		Currency:               locale.Currency,
		TransactionDescription: "Fund Moved from MAIN_ACCOUNT",
		ClientTransactionID:    "test-client-batch-id1",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		CounterParty: &api.CounterParty{
			DisplayName: "MAIN_ACCOUNT",
			IconURL:     constants.IconURLMap[constants.PocketFundingTransactionSubType],
		},
		TransactionTimestamp: &t,
	}
	response2 := &api.GetPocketActivityDetailResponse{
		Amount:                 -100,
		Currency:               locale.Currency,
		TransactionDescription: "Withdrawal to Paris1",
		ClientTransactionID:    "test-client-batch-id4",
		Status:                 "COMPLETED",
		CounterParty: &api.CounterParty{
			DisplayName: "Paris1",
			IconURL:     constants.IconURLMap[constants.PocketWithdrawalTransactionSubType],
		},
		TransactionTimestamp: &t,
	}
	response3 := &api.GetPocketActivityDetailResponse{
		Amount:                 10,
		Currency:               locale.Currency,
		TransactionDescription: "Interest Earned",
		ClientTransactionID:    "test-client-batch-id-interest-payout",
		Status:                 "COMPLETED",
		CounterParty: &api.CounterParty{
			DisplayName: "Interest Earned",
			IconURL:     constants.IconURLMap[constants.InterestPayout],
		},
		TransactionTimestamp: &t,
	}
	return []*api.GetPocketActivityDetailResponse{response1, response2, response3}
}

// GetPocketActivityDetailResponseBahasa  returns a sample outputs for GetTransactionDetail requests.
func GetPocketActivityDetailResponseBahasa() []*api.GetPocketActivityDetailResponse {
	response1 := GetPocketActivityDetailResponse()[0]
	response1.TransactionDescription = "Pemindahan Dana dari MAIN_ACCOUNT"
	response2 := GetPocketActivityDetailResponse()[1]
	response2.TransactionDescription = "Penarikan ke Paris1"
	return []*api.GetPocketActivityDetailResponse{response1, response2}
}

// GetPocketActivityDetailResponseForBahasa ...
func GetPocketActivityDetailResponseForBahasa() *api.GetPocketActivityDetailResponse {
	locale := utils.GetLocale()
	return &api.GetPocketActivityDetailResponse{
		Amount:                 10,
		Currency:               locale.Currency,
		TransactionDescription: "Bunga Didapat",
		ClientTransactionID:    "test-client-batch-id-interest-payout",
		Status:                 "COMPLETED",
		CounterParty: &api.CounterParty{
			DisplayName: "Bunga Didapat",
			IconURL:     constants.IconURLMap[constants.InterestPayout],
		},
		TransactionTimestamp: &t,
	}
}

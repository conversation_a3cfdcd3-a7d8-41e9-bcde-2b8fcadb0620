package responses

import (
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
	"gitlab.myteksi.net/dbmy/transaction-history/api"
)

var t = time.Unix(**********, 0).UTC()

// GetTransactionDetailResponse  returns a sample outputs for GetTransactionDetail requests.
// nolint:funlen
func GetTransactionDetailResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	recipientReference := "Dinner"
	paymentDescription := "extra details"
	response1 := &api.GetTransactionDetailResponse{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "Bank Transfer to UserNo2",
		TransactionRemarks:     "Thanks for the dinner",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           constants.MainAccountSource,
		Category:               &api.Category{Id: "", Name: "Transfer Out"},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
		},
		TransactionTimestamp: &t,
	}
	response2 := &api.GetTransactionDetailResponse{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "FAILED",
		StatusDescription:      "Rejected due to account is closed",
		SourceOfFund:           "child-account",
		Category: &api.Category{
			Id:   "",
			Name: "Transfer Out",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
		},
		TransactionTimestamp: &t,
	}
	response3 := &api.GetTransactionDetailResponse{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "Bank Transfer to UserNo2",
		TransactionRemarks:     "Thanks for the dinner",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "child-account",
		Category:               &api.Category{Id: "", Name: "Transfer Out"},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
		},
		TransactionTimestamp: &t,
	}
	response4 := &api.GetTransactionDetailResponse{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "Bank Transfer to UserNo2",
		TransactionRemarks:     "Thanks for the dinner",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "child-account",
		Category:               &api.Category{Id: "", Name: "Transfer Out"},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
		},
		TransactionTimestamp: &t,
	}
	response5 := &api.GetTransactionDetailResponse{
		Amount:                 13,
		Currency:               locale.Currency,
		TransactionDescription: "Transfer from UserNo2",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "",
		Category:               &api.Category{Id: "", Name: "Transfer In"},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
			TransactionDetails: map[string]string{
				"txn_scenario": "FUND_IN",
				"bank_name":    "Affin Bank Berhad",
			},
		},
		HasReceipt:           false,
		TransactionTimestamp: &t,
	}
	response6 := &api.GetTransactionDetailResponse{
		Amount:                 13,
		Currency:               locale.Currency,
		TransactionDescription: "Transfer from UserNo2",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "",
		Category:               &api.Category{Id: "", Name: "Transfer In"},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
			TransactionDetails: map[string]string{
				"txn_scenario":        "RECEIVE_MONEY",
				"recipient_reference": recipientReference,
			},
		},
		HasReceipt:           false,
		TransactionTimestamp: &t,
	}
	response7 := &api.GetTransactionDetailResponse{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "Transfer to UserNo2",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "Main Account",
		Category:               &api.Category{Id: "", Name: "Transfer Out"},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
			TransactionDetails: map[string]string{
				"txn_scenario":        "SEND_MONEY",
				"recipient_reference": recipientReference,
				"payment_description": paymentDescription,
				"type":                "Account number",
				"service_type":        "",
			},
		},
		HasReceipt:           true,
		TransactionTimestamp: &t,
	}
	response8 := &api.GetTransactionDetailResponse{
		Amount:                 13,
		Currency:               locale.Currency,
		TransactionDescription: "Reversal from UserNo2",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "",
		Category:               &api.Category{Id: "", Name: "Transfer In"},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
			TransactionDetails: map[string]string{
				"txn_scenario":        "SEND_MONEY_REVERSAL",
				"recipient_reference": "56789",
			},
		},
		HasReceipt:           false,
		TransactionTimestamp: &t,
	}

	response9 := &api.GetTransactionDetailResponse{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "Reversal to UserNo2",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "Main Account",
		Category:               &api.Category{Id: "", Name: "Transfer Out"},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
			TransactionDetails: map[string]string{
				"txn_scenario":        "RECEIVE_MONEY_REVERSAL",
				"recipient_reference": "56789",
			},
		},
		HasReceipt:           false,
		TransactionTimestamp: &t,
	}

	response10 := &api.GetTransactionDetailResponse{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "Reversal to UserNo2",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "Main Account",
		Category:               &api.Category{Id: "", Name: "Transfer Out"},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
			TransactionDetails: map[string]string{
				"txn_scenario":        "FUND_IN_REVERSAL",
				"recipient_reference": "56789",
			},
		},
		HasReceipt:           false,
		TransactionTimestamp: &t,
	}
	return []*api.GetTransactionDetailResponse{response1, response2, response3, response4, response5, response6, response7, response8, response9, response10}
}

// GetTransactionDetailResponseForIntrabankInBahasa  returns a sample outputs for GetTransactionDetail requests.
func GetTransactionDetailResponseForIntrabankInBahasa() []*api.GetTransactionDetailResponse {
	response1 := GetTransactionDetailResponse()[0]
	response1.TransactionDescription = "Transfer Bank ke UserNo2"

	return []*api.GetTransactionDetailResponse{response1}
}

// GetTransactionDetailResponseForInterbankInBahasa  returns a sample outputs for GetTransactionDetail requests.
func GetTransactionDetailResponseForInterbankInBahasa() []*api.GetTransactionDetailResponse {
	response := GetTransactionDetailResponse()[3]
	response.TransactionDescription = "Transfer Bank ke UserNo2"

	return []*api.GetTransactionDetailResponse{response}
}

// GetInterestEarnedTransactionResponse  returns a sample outputs for InterestEarned transaction detail requests.
func GetInterestEarnedTransactionResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 13,
		Currency:               locale.Currency,
		TransactionDescription: "Interest Earned",
		TransactionRemarks:     "",
		TransactionID:          "8007e9c10ea841c593bee2e65df599ed",
		BatchID:                "sfg-132gsb34-88800",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		Category: &api.Category{
			Id:   "",
			Name: "",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "Interest Earned",
			IconURL:     constants.IconURLMap[constants.InterestPayout],
		},
		TransactionTimestamp: &t,
	},
	}
}

// GetEarmarkTransactionResponse  returns a sample outputs for Earmark transaction detail requests.
func GetEarmarkTransactionResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 -100000,
		Currency:               locale.Currency,
		TransactionDescription: "On hold amount",
		TransactionRemarks:     "",
		TransactionID:          "8007e9c10ea841c593bee2e65df599ed",
		BatchID:                "sfg-132gsb34-88800",
		Status:                 "CANCELED",
		StatusDescription:      "",
		Category: &api.Category{
			Id:   "",
			Name: "On hold amount",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "On hold amount",
			IconURL:     constants.IconURLMap[constants.TransferOut],
			TransactionDetails: map[string]string{
				"txn_scenario":        constants.ReleaseEarmarkTransactionType,
				"recipient_reference": "",
			},
		},
		TransactionTimestamp: &t,
	}, {
		Amount:                 -100000,
		Currency:               locale.Currency,
		TransactionDescription: "On hold amount",
		TransactionRemarks:     "",
		TransactionID:          "8007e9c10ea841c593bee2e65df599ed",
		BatchID:                "sfg-132gsb34-88800",
		Status:                 "PROCESSING",
		StatusDescription:      "",
		Category: &api.Category{
			Id:   "",
			Name: "On hold amount",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "On hold amount",
			IconURL:     constants.IconURLMap[constants.TransferOut],
			TransactionDetails: map[string]string{
				"txn_scenario":        constants.ApplyEarmarkTransactionType,
				"recipient_reference": "",
			},
		},
		TransactionTimestamp: &t,
	}}
}

// GetLoanTransactionResponse  returns a sample outputs for Loan transaction detail requests.
func GetLoanTransactionResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 -100000,
		Currency:               locale.Currency,
		TransactionDescription: "FlexiCredit repayment",
		TransactionID:          "8007e9c10ea841c593bee2e65df599ed",
		BatchID:                "sfg-132gsb34-88800",
		Status:                 "COMPLETED",
		Category: &api.Category{
			Name: "Repayment",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "FlexiCredit repayment",
			IconURL:     constants.IconURLMap[constants.TransferOut],
			TransactionDetails: map[string]string{
				"txn_scenario": constants.RepaymentTransactionType,
				"service_type": "",
			},
		},
		TransactionTimestamp: &t,
	}, {
		Amount:                 100000,
		Currency:               locale.Currency,
		TransactionDescription: "FlexiCredit",
		TransactionID:          "8007e9c10ea841c593bee2e65df599ff",
		BatchID:                "sfg-132gsb34-88811",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		Category: &api.Category{
			Name: "Drawdown",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "FlexiCredit",
			IconURL:     constants.IconURLMap[constants.TransferIn],
			TransactionDetails: map[string]string{
				"txn_scenario": constants.DrawdownTransactionType,
				"service_type": "",
			},
		},
		TransactionTimestamp: &t,
	}}
}

// GetInterestEarnedTransactionResponseForBahasa  returns a sample outputs for InterestEarned transaction detail requests.
func GetInterestEarnedTransactionResponseForBahasa() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 13,
		Currency:               locale.Currency,
		TransactionDescription: "Bunga Didapat",
		TransactionRemarks:     "",
		TransactionID:          "8007e9c10ea841c593bee2e65df599ed",
		BatchID:                "sfg-132gsb34-88800",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		Category: &api.Category{
			Id:   "",
			Name: "",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "Bunga Didapat",
			IconURL:     constants.IconURLMap[constants.InterestPayout],
		},
		TransactionTimestamp: &t,
	},
	}
}

// GetTaxDeductedTransactionResponse  returns a sample outputs for Tax deducted transaction detail requests.
func GetTaxDeductedTransactionResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 13,
		Currency:               locale.Currency,
		TransactionDescription: "Tax on Interest",
		TransactionRemarks:     "",
		TransactionID:          "8007e9c10ea841c593bee2e65df599ed",
		BatchID:                "sfg-132gsb34-88800",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		Category:               &api.Category{Id: "", Name: ""},
		CounterParty: &api.CounterParty{
			DisplayName: "Tax on Interest",
			IconURL:     constants.IconURLMap[constants.TaxOnInterest],
		},
		TransactionTimestamp: &t,
	},
	}
}

// GetTaxDeductedTransactionResponseForBahasa  returns a sample outputs for Tax deducted transaction detail requests.
func GetTaxDeductedTransactionResponseForBahasa() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 13,
		Currency:               locale.Currency,
		TransactionDescription: "Pajak atas Bunga",
		TransactionRemarks:     "",
		TransactionID:          "8007e9c10ea841c593bee2e65df599ed",
		BatchID:                "sfg-132gsb34-88800",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		Category:               &api.Category{Id: "", Name: ""},
		CounterParty: &api.CounterParty{
			DisplayName: "Pajak atas Bunga",
			IconURL:     constants.IconURLMap[constants.TaxOnInterest],
		},
		TransactionTimestamp: &t,
	},
	}
}

// GetOPSTransactionResponse  returns a sample outputs for ops transaction detail requests.
func GetOPSTransactionResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "Transaction Adjustment",
		TransactionRemarks:     "",
		TransactionID:          "8007e9c10ea841c593bee2e65df599ed",
		BatchID:                "sfg-132gsb34-88800",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		Category: &api.Category{
			Id:   "",
			Name: "Adjustment Transaction",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "BANK",
		},
		TransactionTimestamp: &t,
	},
	}
}

// GetPocketTransactionResponses  returns a sample outputs for pocket funding transaction detail requests.
// nolint: dupl
func GetPocketTransactionResponses() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 -513,
		Currency:               locale.Currency,
		TransactionDescription: "Fund Moved to MAIN_ACCOUNT",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ef",
		BatchID:                "test-client-batch-id1",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "Main Account",
		Category: &api.Category{
			Id:   "",
			Name: "Withdrawal",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "MAIN_ACCOUNT",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
		},
		TransactionTimestamp: &t,
	},
		{
			Amount:                 100,
			Currency:               locale.Currency,
			TransactionDescription: "Withdrawal from Paris1",
			TransactionRemarks:     "",
			TransactionID:          "9007e9c10ea841c593bee2e65df599ef",
			BatchID:                "test-client-batch-id4",
			Status:                 "COMPLETED",
			StatusDescription:      "",
			SourceOfFund:           "",
			Category: &api.Category{
				Id:   "",
				Name: "Transfer In",
			},
			CounterParty: &api.CounterParty{
				DisplayName: "Paris1",
				IconURL:     "",
			},
			TransactionTimestamp: &t,
		},
	}
}

// GetPocketTransactionResponsesForAPITest  returns a sample outputs for pocket funding transaction detail requests.
// nolint: dupl
func GetPocketTransactionResponsesForAPITest() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 -513,
		Currency:               locale.Currency,
		TransactionDescription: "Fund Moved to MAIN_ACCOUNT",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ef",
		BatchID:                "test-client-batch-id1",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           constants.MainAccountSource,
		Category:               &api.Category{Id: "", Name: "Withdrawal"},
		CounterParty: &api.CounterParty{
			DisplayName: "MAIN_ACCOUNT",
			IconURL:     "https://assets.dev.bankfama.net/dev/transfer_money.png",
		},
		TransactionTimestamp: &t,
	},
		{
			Amount:                 100,
			Currency:               locale.Currency,
			TransactionID:          "9007e9c10ea841c593bee2e65df599ef",
			BatchID:                "test-client-batch-id4",
			TransactionDescription: "Withdrawal from Paris1",
			TransactionRemarks:     "",
			Status:                 "COMPLETED",
			StatusDescription:      "",
			Category:               &api.Category{Id: "", Name: "Transfer In"},
			CounterParty: &api.CounterParty{
				DisplayName: "Paris1",
				IconURL:     "",
			},
			TransactionTimestamp: &t,
		},
	}
}

// GetPocketTransactionResponsesForAPITestBahasa  returns a sample outputs for pocket funding transaction detail requests.
// nolint: dupl
func GetPocketTransactionResponsesForAPITestBahasa() []*api.GetTransactionDetailResponse {
	response1 := GetPocketTransactionResponsesForAPITest()[0]
	response1.TransactionDescription = "Pemindahan Dana ke MAIN_ACCOUNT"
	response2 := GetPocketTransactionResponsesForAPITest()[1]
	response2.TransactionDescription = "Penarikan dari Paris1"
	return []*api.GetTransactionDetailResponse{response1, response2}
}

// GetTransactionDetailForGRABResponse  returns a sample outputs for GetTransactionDetail grab txn requests.
func GetTransactionDetailForGRABResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	response1 := &api.GetTransactionDetailResponse{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abcd12345",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "child-account",
		Category: &api.Category{
			Id:   "",
			Name: "",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "Grab Ride",
			IconURL:     constants.IconURLMap["DefaultTransaction"],
		},
		TransactionTimestamp: &t,
	}
	return []*api.GetTransactionDetailResponse{response1}
}

// GetTransactionDetailForMooMooResponse ...
// // nolint:funlen
func GetTransactionDetailForMooMooResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	response1 := &api.GetTransactionDetailResponse{
		Amount:                 -100,
		Currency:               locale.Currency,
		TransactionDescription: "Transfer to Moomoo",
		TransactionRemarks:     "",
		TransactionID:          "mabcd12345",
		BatchID:                "abcd12345",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "child-account",
		Category: &api.Category{
			Name: "Transfer Out",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "Moomoo",
			IconURL:     "https:/assets.bank/image.png",
			TransactionDetails: map[string]string{
				"txn_scenario": "SPEND_MONEY",
			},
		},
		TransactionTimestamp: &t,
	}
	response2 := &api.GetTransactionDetailResponse{
		Amount:                 600,
		Currency:               locale.Currency,
		TransactionDescription: "Withdrawal from Moomoo",
		TransactionRemarks:     "",
		TransactionID:          "mabcd12345",
		BatchID:                "abcd1234567",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "child-account",
		Category: &api.Category{
			Name: "Transfer In",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "Moomoo",
			IconURL:     "https:/assets.bank/image.png",
			TransactionDetails: map[string]string{
				"txn_scenario": "CASH_OUT",
			},
		},
		TransactionTimestamp: &t,
	}
	response3 := &api.GetTransactionDetailResponse{
		Amount:                 500,
		Currency:               locale.Currency,
		TransactionDescription: "Refund from Moomoo",
		TransactionRemarks:     "",
		TransactionID:          "mabcd12345",
		BatchID:                "abcd123456",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "child-account",
		Category: &api.Category{
			Name: "Transfer In",
		},
		CounterParty: &api.CounterParty{
			DisplayName: "Moomoo",
			IconURL:     "https:/assets.bank/image.png",
			TransactionDetails: map[string]string{
				"txn_scenario": "SPEND_MONEY_REVERSAL",
			},
		},
		TransactionTimestamp: &t,
	}

	return []*api.GetTransactionDetailResponse{response1, response2, response3}
}

// DBMYGetTransactionDetailForGRABResponse  returns a sample outputs for GetTransactionDetail grab txn requests for DBMY.
func DBMYGetTransactionDetailForGRABResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	response1 := &api.GetTransactionDetailResponse{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "Payment to Grab Ride",
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abcd12345",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "Main Account",
		Category:               &api.Category{Id: "", Name: "Transfer Out"},
		CounterParty: &api.CounterParty{
			DisplayName: "Grab Ride",
			IconURL:     "https://assets.dev.g-bank.app/txn-history/grab.png",
			TransactionDetails: map[string]string{
				"txn_scenario":        "SPEND_MONEY",
				"recipient_reference": "",
			},
		},
		HasReceipt:           false,
		TransactionTimestamp: &t,
	}
	return []*api.GetTransactionDetailResponse{response1}
}

// DBMYGetTransactionDetailForInsuranceResponse  returns a sample outputs for GetTransactionDetail insurance txn requests for DBMY.
func DBMYGetTransactionDetailForInsuranceResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	response1 := &api.GetTransactionDetailResponse{
		Amount:                 -13,
		Currency:               locale.Currency,
		TransactionDescription: "Cyber Fraud Protect payment",
		TransactionRemarks:     "Cyber Fraud Protect payment",
		TransactionID:          "abcd12345",
		BatchID:                "batch-abcd12345",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "Main Account",
		Category:               &api.Category{Id: "", Name: "Insurance"},
		CounterParty: &api.CounterParty{
			DisplayName: "Cyber Fraud Protect payment",
			TransactionDetails: map[string]string{
				"txn_scenario":        constants.InsurPremiumTxType,
				"recipient_reference": "Cyber Fraud Protect payment",
			},
		},
		HasReceipt:           false,
		TransactionTimestamp: &t,
	}
	return []*api.GetTransactionDetailResponse{response1}
}

// GetBizTransactionDetailResponse  returns a sample outputs for GetTransactionDetail requests for BIZ.
func GetBizTransactionDetailResponse() []*api.GetTransactionDetailResponse {
	response1 := &api.GetTransactionDetailResponse{
		Amount:                 -24,
		Currency:               "MYR",
		TransactionDescription: "Transfer to UserNo2",
		TransactionRemarks:     "Thanks for the dinner",
		TransactionID:          "a1943437135d4674bd1f39df6233c83c",
		BatchID:                "test-biz",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		SourceOfFund:           "child-account",
		Category:               &api.Category{Id: "", Name: "Transfer Out"},
		CounterParty: &api.CounterParty{
			DisplayName: "UserNo2",
			TransactionDetails: map[string]string{
				"txn_scenario":        "TRANSFER_MONEY",
				"recipient_reference": "Thanks for the dinner",
				"service_type":        "",
			},
		},
		TransactionTimestamp: &t,
		HasReceipt:           true,
	}
	return []*api.GetTransactionDetailResponse{response1}
}

func GetCardNewIssuanceFeeTransactionDetailResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 -400,
		TransactionDescription: "New card issuance fee",
		Currency:               locale.Currency,
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		CounterParty: &api.CounterParty{
			DisplayName: "New card issuance fee",
			IconURL:     constants.IconURLMap[constants.DebitCardDomain],
			TransactionDetails: map[string]string{
				"txn_scenario": "BANK_INITIATED",
			},
		},
		TransactionTimestamp: &t,
		CardTransactionDetail: &api.CardTransactionDetail{
			CardID:         "card-id",
			TailCardNumber: "**1234",
		},
	},
	}
}

func GetCardNewIssuanceFeeCancelledTransactionDetailResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 400,
		TransactionDescription: "New card issuance fee",
		Currency:               locale.Currency,
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "CANCELLED",
		StatusDescription:      "",
		CounterParty: &api.CounterParty{
			DisplayName: "New card issuance fee",
			IconURL:     constants.IconURLMap[constants.DebitCardDomain],
			TransactionDetails: map[string]string{
				"txn_scenario": "BANK_INITIATED",
			},
		},
		TransactionTimestamp: &t,
		CardTransactionDetail: &api.CardTransactionDetail{
			CardID:         "card-id",
			TailCardNumber: "**1234",
		},
	},
	}
}

func GetCardNewIssuanceFeeWaivedTransactionDetailResponse() []*api.GetTransactionDetailResponse {
	locale := utils.GetLocale()
	return []*api.GetTransactionDetailResponse{{
		Amount:                 200,
		TransactionDescription: "New card issuance fee waived",
		Currency:               locale.Currency,
		TransactionRemarks:     "",
		TransactionID:          "9007e9c10ea841c593bee2e65df599ed",
		BatchID:                "abc123efg",
		Status:                 "COMPLETED",
		StatusDescription:      "",
		CounterParty: &api.CounterParty{
			DisplayName: "New card issuance fee waived",
			IconURL:     constants.IconURLMap[constants.DebitCardDomain],
			TransactionDetails: map[string]string{
				"txn_scenario": "BANK_INITIATED",
			},
		},
		TransactionTimestamp: &t,
		CardTransactionDetail: &api.CardTransactionDetail{
			CardID:         "card-id",
			TailCardNumber: "**1234",
		},
	},
	}
}

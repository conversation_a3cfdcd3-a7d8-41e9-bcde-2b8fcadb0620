// Package resources ...
package resources

import (
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
)

// InterestPayoutTransactionData ...
// nolint
func InterestPayoutTransactionData() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{
		ID:                  1,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "ACCRUED_DEPOSIT_INTEREST",
		DebitOrCredit:       "debit",
		TransactionAmount:   "5.13",
		TransactionCurrency: locale.Currency,
	}, {
		ID:                  2,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "DEFAULT",
		DebitOrCredit:       "credit",
		TransactionAmount:   "5.13",
		TransactionCurrency: locale.Currency,
	}, {
		ID:                  3,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "ACCRUED_DEPOSIT_INTEREST_*************",
		DebitOrCredit:       "debit",
		TransactionAmount:   "5.50",
		TransactionCurrency: locale.Currency,
	}, {
		ID:                  4,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "*************",
		DebitOrCredit:       "credit",
		TransactionAmount:   "5.50",
		TransactionCurrency: locale.Currency,
	}, {
		ID:                  5,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "ACCRUED_DEPOSIT_INTEREST_*************",
		DebitOrCredit:       "debit",
		TransactionAmount:   "5.50",
		TransactionCurrency: locale.Currency,
	}, {
		ID:                  6,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "*************",
		DebitOrCredit:       "credit",
		TransactionAmount:   "5.50",
		TransactionCurrency: locale.Currency,
	}}
}

// InterestPayoutReversalTransactionData ...
// nolint
func InterestPayoutReversalTransactionData() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{
		ID:                  1,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT_REVERSAL",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "ACCRUED_DEPOSIT_INTEREST",
		DebitOrCredit:       "credit",
		TransactionAmount:   "5.13",
		TransactionCurrency: locale.Currency,
	}, {
		ID:                  2,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "DEFAULT",
		DebitOrCredit:       "debit",
		TransactionAmount:   "5.13",
		TransactionCurrency: locale.Currency,
	}, {
		ID:                  3,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT_REVERSAL",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "ACCRUED_DEPOSIT_INTEREST_*************",
		DebitOrCredit:       "credit",
		TransactionAmount:   "5.50",
		TransactionCurrency: locale.Currency,
	}, {
		ID:                  4,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "*************",
		DebitOrCredit:       "debit",
		TransactionAmount:   "5.50",
		TransactionCurrency: locale.Currency,
	}, {
		ID:                  5,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT_REVERSAL",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "ACCRUED_DEPOSIT_INTEREST_*************",
		DebitOrCredit:       "credit",
		TransactionAmount:   "5.50",
		TransactionCurrency: locale.Currency,
	}, {
		ID:                  6,
		BatchStatus:         "ACCEPTED",
		TransactionDomain:   "DEPOSITS",
		TransactionType:     "INTEREST_PAYOUT",
		TransactionSubtype:  "SAVINGS",
		AccountID:           "**********",
		AccountAddress:      "*************",
		DebitOrCredit:       "debit",
		TransactionAmount:   "5.50",
		TransactionCurrency: locale.Currency,
	}}
}

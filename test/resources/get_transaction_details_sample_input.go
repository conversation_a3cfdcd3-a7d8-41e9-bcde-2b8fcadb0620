package resources

import (
	"encoding/json"
	"time"

	"gitlab.myteksi.net/bersama/core-banking/account-service/api"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
)

// TransactionsMockDBRows ...
// nolint:dupl,funlen
func TransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.<PERSON>(map[string]string{"displayName": "ManualUser"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ssf12332",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	}, {ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ssf12332",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "RTOL_ALTO",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	}, {ID: 4,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ssf12332",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "FUND_IN",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
		BatchDetails:                batchDetails,
	}, {ID: 5,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ssf12332",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		Metadata:                    []byte(`{"tm_balance_id": "", "recipient_reference": "Dinner", "payment_description": "extra details"}`),
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	}, {ID: 6,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ssf12332",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		Metadata:                    []byte(`{"tm_balance_id": "", "recipient_reference": "Dinner", "payment_description": "extra details"}`),
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	}, {ID: 7,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		Metadata:                    []byte(`{"tm_balance_id": "", "recipient_reference": "Dinner"}`),
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 8,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		Metadata:                    []byte(`{"tm_balance_id": "", "recipient_reference": "Dinner"}`),
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 9,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "ADJUSTMENT",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		Metadata:                    []byte(`{"tm_balance_id": ""}`),
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 10,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEBIT_CARD",
		TransactionType:             "SPEND_CARD_ATM",
		TransactionSubtype:          "MASTERCARD",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		Metadata:                    []byte(`{"tm_balance_id": ""}`),
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	}, {ID: 11,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY_REVERSAL",
		TransactionSubtype:          "RPP_PROPERTIES",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		Metadata:                    []byte(`{"tm_balance_id": ""}`),
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 12,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY_REVERSAL",
		TransactionSubtype:          "RPP_PROPERTIES",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		Metadata:                    []byte(`{"tm_balance_id": ""}`),
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 13,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "FUND_IN_REVERSAL",
		TransactionSubtype:          "RPP_PROPERTIES",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		Metadata:                    []byte(`{"tm_balance_id": ""}`),
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 14,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEBIT_CARD",
		TransactionType:             "DISPUTE_BANK_REFUND",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		Metadata:                    []byte(`{"tm_balance_id": ""}`),
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 15,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ssf12332",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "BIZ_DEPOSITS",
		TransactionType:             "FUND_IN",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
		BatchDetails:                batchDetails,
	}}
}

// InterestPayoutTransactionsMockDBRows ...
// nolint:dupl
func InterestPayoutTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"displayName": "ManualUser"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "sfg-132gsb34-88800",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "INTEREST_PAYOUT",
		TransactionSubtype:          "SAVINGS",
		AccountID:                   "**********",
		AccountAddress:              "ACCRUED_DEPOSIT_INTEREST",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	}, {ID: 2,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "sfg-132gsb34-88800",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "INTEREST_PAYOUT",
		TransactionSubtype:          "SAVINGS",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	}}
}

// TaxPayoutTransactionsMockDBRows ...
//
//nolint:dupl
func TaxPayoutTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"displayName": "ManualUser"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "sfg-132gsb34-88800",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TAX_PAYOUT",
		TransactionSubtype:          "SAVINGS",
		AccountID:                   "**********",
		AccountAddress:              "ACCRUED_TAX",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	}, {ID: 2,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "sfg-132gsb34-88800",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TAX_PAYOUT",
		TransactionSubtype:          "SAVINGS",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	}}
}

// InterestPayoutReversalTransactionsMockDBRows ...
//
//nolint:dupl
func InterestPayoutReversalTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"displayName": "ManualUser"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "sfg-132gsb34-88800",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		BatchStatus:                 "REJECTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "INTEREST_PAYOUT_REVERSAL",
		TransactionSubtype:          "SAVINGS",
		AccountID:                   "**********",
		AccountAddress:              "ACCRUED_DEPOSIT_INTEREST",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchErrorMessage:           "Rejected due to account is closed",
		BatchDetails:                batchDetails,
	}, {ID: 2,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "sfg-132gsb34-88800",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		BatchStatus:                 "REJECTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "INTEREST_PAYOUT_REVERSAL",
		TransactionSubtype:          "SAVINGS",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
		BatchDetails:                batchDetails,
	}}
}

// CommittedSettlementTransactions ...
//
//nolint:dupl
func CommittedSettlementTransactions() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 1202,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "0f0ffb23-eb3d-4226-b370-6cf6bca8bfc7",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1203,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "0f0ffb23-eb3d-4226-b370-6cf6bca8bfc7",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1204,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "0f0ffb23-eb3d-4226-b370-6cf6bca8bfc7",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	},
	}
}

// PendingOutboundAuthTransactions ...
//
//nolint:dupl
func PendingOutboundAuthTransactions() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "0f0ffb23-eb3d-4226-b370-6cf6bca8bfc7",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	},
	}
}

// ReleaseTransactions ...
//
//nolint:dupl
func ReleaseTransactions() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"displayName": "DummyUser"})
	return []*storage.TransactionsData{{
		ID:                          10702,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "0f0ffb23-eb3d-4226-b370-6cf6bca8bfc7",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY_REVERSAL",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
		BatchDetails:                batchDetails,
	},
	}
}

// PendingIncomingTransactions ...
//
//nolint:dupl
func PendingIncomingTransactions() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_INCOMING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_INCOMING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	},
	}
}

// PaymentDataMockDBRows ...
//
//nolint:dupl, funlen
func PaymentDataMockDBRows() []*storage.PaymentDetail {
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2"})
	accountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", FullName: "DummyUser", DisplayName: "account name", Proxy: dto.ProxyObject{Channel: constants.RppChannelType}})
	metadata := []byte(`{"remarks": "Thanks for the dinner", "transferType": "INTRABANK"}`)
	statusDetails, _ := json.Marshal(dto.StatusDetails{Reason: "ACCOUNT_CLOSED", Description: "Rejected due to account is closed"})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    1,
			TransactionID:         "abc123efg",
			TransactionType:       "TRANSFER_MONEY",
			TransactionSubType:    "INTRABANK",
			Amount:                -20,
			Currency:              utils.GetLocale().Currency,
			AccountID:             "12345",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			Metadata:              metadata,
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
		{
			ID:                    2,
			TransactionID:         "efg123abd",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "FAST_NETWORK",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    3,
			TransactionID:         "efg1111abd",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "FAST_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "FAILED",
			StatusDetails:         statusDetails,
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    4,
			TransactionID:         "efg1sda22d",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "FAILED",
			StatusDetails:         statusDetails,
		},
	}
}

// PaymentRppDataMockDBRows ...
// nolint: funlen
func PaymentRppDataMockDBRows() []*storage.PaymentDetail {
	accountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", FullName: "DummyUser", DisplayName: "account name", Proxy: dto.ProxyObject{Channel: constants.RppChannelType}})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2", SwiftCode: "PHBMMYKL", Proxy: dto.ProxyObject{}})
	counterPartyAccountDetail1, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2", SwiftCode: "XXX", Proxy: dto.ProxyObject{Channel: constants.RppChannelType, Type: constants.MobileNumberRail}})
	counterPartyAccountDetail2, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2", SwiftCode: "XXX", Proxy: dto.ProxyObject{Channel: constants.RppChannelType, Type: constants.NRICRail}})
	counterPartyAccountDetail3, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2", SwiftCode: "XXX", Proxy: dto.ProxyObject{Channel: constants.RppChannelType, Type: constants.ArmyIDRail}})
	counterPartyAccountDetail4, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2", SwiftCode: "XXX", Proxy: dto.ProxyObject{Channel: constants.RppChannelType, Type: constants.PassportNumberRail}})
	counterPartyAccountDetail5, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2", SwiftCode: "XXX", Proxy: dto.ProxyObject{Channel: constants.RppChannelType, Type: constants.BusinessRegistrationNumberRail}})
	counterPartyAccountDetail6, _ := json.Marshal(dto.AccountDetail{Number: "54321", PairingID: "000", DisplayName: "UserNo2", SwiftCode: "XXX", Proxy: dto.ProxyObject{}})

	accountDetailRpp, _ := json.Marshal(dto.AccountDetail{Number: "54321", FullName: "DummyUser", DisplayName: "account name", SwiftCode: "XXX", Proxy: dto.ProxyObject{Channel: constants.RppChannelType, Type: constants.MobileNumberRail}})

	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    1,
			TransactionID:         "abc123efg",
			TransactionType:       "FUND_IN",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "transferType": "", "recipient_reference": "Dinner", "external_id": "12345", "bank_name": "Affin Bank Berhad"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    2,
			TransactionID:         "abc123efg",
			TransactionType:       "RECEIVE_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetailRpp,
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "transferType": "", "recipient_reference": "Dinner", "external_id": "12345" }`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    3,
			TransactionID:         "abc123efg",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "recipient_reference": "Dinner", "payment_description": "extra details", "external_id": "12345", "bank_name": "Affin Bank Berhad"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    4,
			TransactionID:         "abc123efg",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail1,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "recipient_reference": "Dinner", "payment_description": "extra details", "external_id": "12345"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    5,
			TransactionID:         "abc123efg",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail2,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "recipient_reference": "Dinner", "payment_description": "extra details", "external_id": "12345"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    6,
			TransactionID:         "abc123efg",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail3,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "recipient_reference": "Dinner", "payment_description": "extra details", "external_id": "12345"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    7,
			TransactionID:         "abc123efg",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail4,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "recipient_reference": "Dinner", "payment_description": "extra details", "external_id": "12345"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    8,
			TransactionID:         "abc123efg",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail5,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "recipient_reference": "Dinner", "payment_description": "extra details", "external_id": "12345"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    9,
			TransactionID:         "abc123efg",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail5,
			Status:                "COMPLETED",
			Metadata:              []byte(`{somerandom`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    10,
			TransactionID:         "abc123efg",
			TransactionType:       "FUND_IN",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "PROCESSING",
			Metadata:              []byte(`{"remarks": "", "transferType": "", "recipient_reference": "Dinner", "external_id": "12345", "bank_name": "Affin Bank Berhad"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    11,
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			TransactionType:       "FUND_IN",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail6,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "transferType": "", "recipient_reference": "Dinner", "external_id": "54321", "serviceType": "QR_TRANSFER", "cash_account_code": "CASA", "bank_name": "Affin Bank Berhad"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    12,
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			TransactionType:       "SEND_MONEY_REVERSAL",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail6,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "transferType": "", "recipient_reference": "Dinner", "external_id": "54321", "bank_name": "Affin Bank Berhad", "original_transaction_id":"56789"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    13,
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			TransactionType:       "RECEIVE_MONEY_REVERSAL",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail6,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "transferType": "", "recipient_reference": "Dinner", "external_id": "54321", "bank_name": "Affin Bank Berhad", "original_transaction_id":"56789"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    14,
			TransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			TransactionType:       "FUND_IN_REVERSAL",
			TransactionSubType:    "RPP_NETWORK",
			Currency:              utils.GetLocale().Currency,
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail6,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "transferType": "", "recipient_reference": "Dinner", "external_id": "54321", "bank_name": "Affin Bank Berhad", "original_transaction_id":"56789"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// PaymentGrabDataMockDBRows ...
// nolint: funlen
func PaymentGrabDataMockDBRows() []*storage.PaymentDetail {
	accountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", FullName: "DummyUser", DisplayName: "account name", Proxy: dto.ProxyObject{Channel: constants.RppChannelType}})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2", SwiftCode: "PHBMMYKL", Proxy: dto.ProxyObject{}})

	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    1,
			TransactionID:         "abc123efg",
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "GRAB",
			Currency:              utils.GetLocale().Currency,
			Amount:                -13,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "transferType": "", "grab_activity_type": "TRANSPORT"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// PaymentMooMooDataMockDBRows ...
// nolint: funlen
func PaymentMooMooDataMockDBRows() []*storage.PaymentDetail {
	accountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", FullName: "DummyUser", DisplayName: "account name", Proxy: dto.ProxyObject{Channel: constants.RppChannelType}})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "", DisplayName: "", SwiftCode: "", Proxy: dto.ProxyObject{}})

	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    1,
			TransactionID:         "abc123efg",
			TransactionType:       "CASH_OUT",
			TransactionSubType:    "MOOMOO",
			Currency:              utils.GetLocale().Currency,
			Amount:                600,
			AccountID:             "**********",
			CounterPartyAccountID: "",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "transferType": ""`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// PaymentInsuranceDataMockDBRows ...
// nolint: funlen
func PaymentInsuranceDataMockDBRows() []*storage.PaymentDetail {
	accountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", FullName: "DummyUser", DisplayName: "account name", Proxy: dto.ProxyObject{Channel: constants.RppChannelType}})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2", SwiftCode: "PHBMMYKL", Proxy: dto.ProxyObject{}})

	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    1,
			TransactionID:         "abc123efg",
			TransactionType:       constants.InsurPremiumTxType,
			TransactionSubType:    constants.CollectCustSubType,
			Currency:              utils.GetLocale().Currency,
			Amount:                -13,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetail,
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			Metadata:              []byte(`{"transferType": "", "activity_type": "INSURANCE", "remarks": "Cyber Fraud Protect payment", "counterPartyDisplayName":"Cyber Fraud Protect payment"}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// FailedStatusPaymentTransactionsDBRows ...
func FailedStatusPaymentTransactionsDBRows() []*storage.PaymentDetail {
	locale := utils.GetLocale()
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2"})
	statusDetails1, _ := json.Marshal(dto.StatusDetails{Reason: "CONTRACT_VIOLATION", Description: "CONTRACT_VIOLATION_BREACH_TERMS_AND_CONDITIONS"})
	statusDetails2, _ := json.Marshal(dto.StatusDetails{Reason: "REJECTED_BY_PARTNER", Description: "DDA Terminated. code from adapter: 1041"})
	statusDetails3, _ := json.Marshal(dto.StatusDetails{Reason: "PAYMENT_TRANSFER_LIMIT", Description: "Maximum amount exceeded for PAYMENT_TRANSFER_LIMIT.ACCOUNT_ID.DAILY_CALENDAR. Amount has to be between 1 and 111"})
	statusDetails4, _ := json.Marshal(dto.StatusDetails{Reason: "ACCOUNT_VIOLATION_ACCOUNT_NOT_PRESENT", Description: ""})
	return []*storage.PaymentDetail{{ID: 1,
		TransactionID:         "abc123efg",
		TransactionType:       "TRANSFER_MONEY",
		TransactionSubType:    "INTRABANK",
		Amount:                -20,
		Currency:              locale.Currency,
		AccountID:             "12345",
		CounterPartyAccountID: "54321",
		CounterPartyAccount:   counterPartyAccountDetail,
		Status:                "FAILED",
		StatusDetails:         statusDetails1,
		CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
	}, {ID: 2,
		TransactionID:         "efg123abd",
		Currency:              locale.Currency,
		Amount:                -20,
		AccountID:             "**********",
		TransactionType:       "SEND_MONEY",
		TransactionSubType:    "FAST_NETWORK",
		CounterPartyAccountID: "54321",
		CounterPartyAccount:   counterPartyAccountDetail,
		Status:                "FAILED",
		StatusDetails:         statusDetails2,
		CreationTimestamp:     time.Unix(**********, 0).UTC(),
	}, {ID: 3,
		TransactionID:         "efg1111abd",
		TransactionType:       "SEND_MONEY",
		TransactionSubType:    "FAST_NETWORK",
		Currency:              locale.Currency,
		Amount:                20,
		AccountID:             "**********",
		CounterPartyAccountID: "54321",
		CounterPartyAccount:   counterPartyAccountDetail,
		Status:                "FAILED",
		StatusDetails:         statusDetails3,
		CreationTimestamp:     time.Unix(**********, 0).UTC(),
	}, {ID: 4,
		TransactionID:         "efg1sda22d",
		TransactionType:       "SEND_MONEY",
		TransactionSubType:    "FAST_NETWORK",
		Currency:              locale.Currency,
		Amount:                20,
		AccountID:             "**********",
		CounterPartyAccountID: "54321",
		CounterPartyAccount:   counterPartyAccountDetail,
		Status:                "FAILED",
		StatusDetails:         statusDetails4,
		CreationTimestamp:     time.Unix(**********, 0).UTC(),
	},
	}
}

// ManualPostings ...
//
//nolint:dupl
func ManualPostings() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"displayName": "MANUAL_POSTING", "batch_remarks": ""})
	return []*storage.TransactionsData{{
		ID:                          10702,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "e3589ea7-436f-4588-946c-a1b06a633598",
		TmPostingInstructionBatchID: "aldfnskv",
		TmTransactionID:             "0f0ffb23-eb3d-4226-b370-6cf6bca8bfc7",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "payments",
		TransactionType:             "paynow",
		TransactionSubtype:          "funds_in",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.05",
		TransactionCurrency:         locale.Currency,
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
		BatchDetails:                batchDetails,
	}, {ID: 10703,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "e3589ea7-436f-4588-946c-a1b06a633598",
		TmPostingInstructionBatchID: "aldfnskv",
		TmTransactionID:             "0f0ffb23-eb3d-4226-b370-6cf6bca8bfc7",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "payments",
		TransactionType:             "paynow",
		TransactionSubtype:          "funds_in",
		AccountID:                   "50001",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.05",
		TransactionCurrency:         locale.Currency,
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
		BatchDetails:                batchDetails,
	},
	}
}

// GrabTransactionsMockDBRows ...
func GrabTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abcd12345",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SPEND_MONEY",
		TransactionSubtype:          "GRAB",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	},
	}
}

// MooMooTransactionsMockDBRows ...
// nolint:funlen
func MooMooTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "mabcd12345",
		ClientBatchID:               "abcd12345",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SPEND_MONEY",
		TransactionSubtype:          "MOOMOO",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "1",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	},
		{ID: 2,
			ClientTransactionID:         "mabcd12345",
			ClientBatchID:               "abcd1234567",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			BatchRemarks:                "TEST REMARKS",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "CASH_OUT",
			TransactionSubtype:          "MOOMOO",
			AccountID:                   "12345",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "6",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
		},
		{ID: 2,
			ClientTransactionID:         "mabcd12345",
			ClientBatchID:               "abcd123456",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			BatchRemarks:                "TEST REMARKS",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SPEND_MONEY_REVERSAL",
			TransactionSubtype:          "MOOMOO",
			AccountID:                   "12345",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "5",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
		},
	}
}

// DBMYGrabTransactionsMockDBRows ...
// nolint: dupl
func DBMYGrabTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abcd12345",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SPEND_MONEY",
		TransactionSubtype:          "GRAB",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	},
	}
}

// DBMYInsuranceTransactionsMockDBRows ...
// nolint: dupl
func DBMYInsuranceTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "abcd12345",
		ClientBatchID:               "batch-abcd12345",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           constants.InsuranceDomain,
		TransactionType:             constants.InsurPremiumTxType,
		TransactionSubtype:          constants.CollectCustSubType,
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	},
	}
}

// OPSTransactionsMockDBRows ...
//
//nolint:dupl
func OPSTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"displayName": "ManualUser"})
	return []*storage.TransactionsData{
		{
			ID:                          1,
			ClientBatchID:               "sfg-132gsb34-88800",
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "FINANCE",
			TransactionType:             "ADJUSTMENT",
			TransactionSubtype:          "BANK_INITIATED",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
			BatchDetails:                batchDetails,
		},
		{
			ID:                          2,
			ClientBatchID:               "sfg-132gsb34-88800",
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "FINANCE",
			TransactionType:             "ADJUSTMENT",
			TransactionSubtype:          "BANK_INITIATED",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
			BatchDetails:                batchDetails,
		},
	}
}

// RewardsTransactionsMockDBRows ...
//
//nolint:dupl
func RewardsTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          1,
			ClientBatchID:               "sfg-132gsb34-88800",
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "REWARDS_CASHBACK",
			TransactionSubtype:          "BANK_INITIATED",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "4.90",
			TransactionCurrency:         locale.Currency,
			TransactionDetails:          json.RawMessage("{\"campaignName\": \"Grab Unlimited Reward\", \"campaignDescription\": \"Grab Unlimited Cashback\"}"),
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          2,
			ClientBatchID:               "sfg-132gsb34-88800",
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEBIT_CARD",
			TransactionType:             constants.RewardsCashback,
			TransactionSubtype:          "BANK_INITIATED",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "8.00",
			TransactionCurrency:         locale.Currency,
			TransactionDetails:          json.RawMessage("{\"campaignName\": \"Cashback Earned\"}"),
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
		},
	}
}

// EarmarkTransactionsMockDBRows ...
// nolint:dupl
func EarmarkTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"displayName": "ManualUser"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "sfg-132gsb34-88800",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RELEASE_EARMARK",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "1000",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "0",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
		BatchDetails:                batchDetails,
	}, {ID: 2,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "sfg-132gsb34-88800",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "APPLY_EARMARK",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "1000",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "-1000",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
		BatchDetails:                batchDetails,
	}}
}

// LoanTransactionsMockDBRows ...
// nolint:dupl
func LoanTransactionsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"displayName": "ManualUser"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "sfg-132gsb34-88800",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "REPAYMENT",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "1000",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "0",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
		BatchDetails:                batchDetails,
	}, {ID: 2,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ff",
		ClientBatchID:               "sfg-132gsb34-88811",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36sdgs22",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "LENDING",
		TransactionType:             "DRAWDOWN",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "1000",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "-1000",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
		BatchDetails:                batchDetails,
	}}
}

// PaymentLoanDataMockDBRows ...
// nolint: funlen
func PaymentLoanDataMockDBRows() []*storage.PaymentDetail {
	return []*storage.PaymentDetail{
		{
			ID:                    1,
			TransactionID:         "sfg-132gsb34-88800",
			TransactionType:       constants.RepaymentTransactionType,
			TransactionSubType:    constants.IntrabankTransactionSubtype,
			Currency:              utils.GetLocale().Currency,
			Amount:                -100000,
			AccountID:             "**********",
			CounterPartyAccountID: "**********",
			Account:               []byte(`{"number": "**********"}`),
			CounterPartyAccount:   []byte(`{"number": "**********"}`),
			Status:                "COMPLETED",
			Metadata:              []byte(`{"remarks": "", "transferType": ""}`),
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// LoanTransactionDetailMockDBRow ...
// nolint:dupl
func LoanTransactionDetailMockDBRow() []*storage.LoanDetail {
	t := time.Unix(**********, 0).UTC()
	return []*storage.LoanDetail{{
		ID:                   1,
		LoanTransactionID:    "loan-txn-id-1",
		Amount:               100000,
		Currency:             "MYR",
		AccountID:            "**********",
		AccountDetail:        nil,
		PaymentTransactionID: "sfg-132gsb34-88800",
		TransactionDomain:    "LENDING",
		TransactionType:      "REPAYMENT",
		TransactionSubType:   "INTRABANK",
		Status:               "COMPLETED",
		StatusDetail:         nil,
		DisbursementDetail:   nil,
		RepaymentDetail:      nil,
		CreatedAt:            t,
		UpdatedAt:            t,
	}, {
		ID:                   2,
		LoanTransactionID:    "loan-txn-id-2",
		Amount:               100000,
		Currency:             "MYR",
		AccountID:            "**********",
		AccountDetail:        nil,
		PaymentTransactionID: "sfg-132gsb34-88811",
		TransactionDomain:    "LENDING",
		TransactionType:      "DRAWDOWN",
		TransactionSubType:   "INTRABANK",
		Status:               "COMPLETED",
		StatusDetail:         nil,
		DisbursementDetail:   nil,
		RepaymentDetail:      nil,
		CreatedAt:            t,
		UpdatedAt:            t,
	}}
}

// GetAccountDetailsByAccountIDResponseParent ...
func GetAccountDetailsByAccountIDResponseParent() *api.GetAccountResponse {
	return &api.GetAccountResponse{Account: &api.Account{
		Id:                        "test-id",
		Name:                      "test",
		ParentAccountID:           "",
		CifNumber:                 "test-cif",
		PermittedCurrencies:       nil,
		AvailableBalance:          nil,
		Status:                    "test-status",
		ProductID:                 "test",
		ProductVariantID:          "test",
		ProductSpecificParameters: nil,
		OpeningTimestamp:          time.Time{},
	}}
}

// GetAccountDetailsByAccountIDResponseChild ...
func GetAccountDetailsByAccountIDResponseChild() *api.GetAccountResponse {
	return &api.GetAccountResponse{Account: &api.Account{
		Id:                        "test-id",
		Name:                      "test",
		ParentAccountID:           "test-parent-account",
		CifNumber:                 "test-cif",
		PermittedCurrencies:       nil,
		AvailableBalance:          nil,
		Status:                    "test-status",
		ProductID:                 "test",
		ProductVariantID:          "test",
		ProductSpecificParameters: nil,
		OpeningTimestamp:          time.Time{},
	}}
}

// PaymentDataMockDBRowsForLendingTransaction ...
// nolint:dupl
func PaymentDataMockDBRowsForLendingTransaction(status string) []*storage.PaymentDetail {
	accountDetails, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2", PairingID: "123456"})
	counterPartyDetails, _ := json.Marshal(dto.AccountDetail{Number: "54321", FullName: "DummyUser", Proxy: dto.ProxyObject{Channel: constants.RppChannelType}, DisplayName: "UserNo2", PairingID: "123456"})
	statusDetails, _ := json.Marshal(dto.StatusDetails{Reason: "ACCOUNT_CLOSED", Description: "Rejected due to account is closed"})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    1,
			TransactionID:         "efg1111abd",
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "FAST_NETWORK",
			Currency:              "SGD",
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			Account:               accountDetails,
			CounterPartyAccount:   counterPartyDetails,
			Status:                status,
			StatusDetails:         statusDetails,
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// TransactionsDataMockDBRowsForFailed ...
func TransactionsDataMockDBRowsForFailed() []*storage.TransactionsData {
	transactionDetailsRepayment, _ := json.Marshal(map[string]string{"loanTransactionID": "12345"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "REJECTED",
		BatchErrorType:              "CONTRACT_VIOLATION",
		BatchErrorMessage:           "CONTRACT_VIOLATION_INSUFFICIENT_FUNDS",
		TransactionDomain:           "LENDING",
		TransactionType:             "REPAYMENT",
		TransactionSubtype:          "FAST_NETWORK",
		TransactionDetails:          transactionDetailsRepayment,
		AccountID:                   "**********",
		AccountAddress:              "REPAYMENT_MADE",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "SGD",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

// TransactionsDataMockDBRowsForBiz ...
func TransactionsDataMockDBRowsForBiz() []*storage.TransactionsData {
	return []*storage.TransactionsData{{
		ID:                          1,
		ClientTransactionID:         "a1943437135d4674bd1f39df6233c83c",
		ClientBatchID:               "test-biz",
		TmPostingInstructionBatchID: "ecdb7ab2-5c74-433f-b855-bb84a76ae2bf",
		TmTransactionID:             "88b29426-601b-4dd2-9575-4756f4ec8acd",
		BatchStatus:                 "ACCEPTED",
		BatchRemarks:                "TEST REMARKS",
		TransactionDomain:           "BIZ_DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "12345",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.24",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "312",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

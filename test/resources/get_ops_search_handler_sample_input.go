package resources //nolint:dupl

import (
	"encoding/json"
	"time"

	"gitlab.myteksi.net/dakota/transaction-history/constants"

	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/storage"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
)

// GetOpsSearchFirstPageMockDBResponse ...
func GetOpsSearchFirstPageMockDBResponse() []*storage.TransactionsData {
	rows := OpsSearchTransactionsDataMockDBRows()
	return rows
}

// GetOpsSearchLastPageMockDBResponse ...
func GetOpsSearchLastPageMockDBResponse() []*storage.TransactionsData {
	rows := OpsSearchTransactionsDataMockDBRows()
	return rows[2:]
}

// OpsSearchTransactionsDataMockDBRows ...
// nolint:dupl, funlen
func OpsSearchTransactionsDataMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchTransactionsCardsDataMockDBRows ...
// nolint:dupl, funlen
func OpsSearchTransactionsCardsDataMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           constants.DebitCardDomain,
		TransactionType:             "SPEND_CARD_PRESENT",
		TransactionSubtype:          "PAYNET_MYDEBIT",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchTransactionsGrabDataMockDBRows ...
// nolint:dupl, funlen
func OpsSearchTransactionsGrabDataMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SPEND_MONEY",
		TransactionSubtype:          "GRAB",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchTransactionsMooMooDataMockDBRows ...
// nolint:dupl, funlen
func OpsSearchTransactionsMooMooDataMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SPEND_MONEY",
		TransactionSubtype:          "MOOMOO",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchTransactionsDataByExternalIDMockDBRows ...
// nolint:dupl
func OpsSearchTransactionsDataByExternalIDMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchTransactionsDataMockDBRowsForAllCategories ...
// nolint
func OpsSearchTransactionsDataMockDBRowsForAllCategories() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "abc123efg",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "FROM_MAIN_TO_POCKET",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          4,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "fasdfasdf",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "FROM_POCKET_TO_MAIN",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          5,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc12fasdf3efdg",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          5,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          6,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123effasddag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82dsd",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY_FEE",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          7,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edx",
			ClientBatchID:               "abc123effasddag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "10",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          8,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc1sdf23efdg",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "RECEIVE_MONEY",
			TransactionSubtype:          "RTOL_ALTO",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          9,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efdsdg",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "INTEREST_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "TRANSFER",
		},
		{
			ID:                          9,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599eddfsd",
			ClientBatchID:               "abc123efdgf",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7csd0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44sde6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TAX_PAYOUT",
			TransactionSubtype:          "SAVINGS",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "TRANSFER",
		},
		{
			ID:                          10,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efdg",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "ADJUSTMENT",
			TransactionSubtype:          "BANK_INITIATED",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
	}
}

// OpsSearchPaymentDetailMockDBRows ...
// nolint
func OpsSearchPaymentDetailMockDBRows() []*storage.PaymentDetail {
	locale := utils.GetLocale()
	account, _ := json.Marshal(dto.AccountDetail{Number: "**********", DisplayName: "User No1", SwiftCode: ""})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "User No2", SwiftCode: ""})
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123", "remarks": ""})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    3,
			TransactionID:         "abc123efg",
			Amount:                -20,
			Currency:              locale.Currency,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
		{
			ID:                    2,
			TransactionID:         "efg123abd",
			Currency:              locale.Currency,
			Amount:                -20,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    1,
			TransactionID:         "efg1111abd",
			Currency:              locale.Currency,
			Amount:                20,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    6,
			TransactionID:         "abcd12345",
			Currency:              locale.Currency,
			Amount:                -13,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
}

// OpsSearchMooMooPaymentDetailMockDBRows ...
// nolint
func OpsSearchMooMooPaymentDetailMockDBRows() []*storage.PaymentDetail {
	locale := utils.GetLocale()
	account, _ := json.Marshal(dto.AccountDetail{Number: "**********", DisplayName: "User No1", SwiftCode: ""})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "", SwiftCode: ""})
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123", "remarks": ""})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    3,
			TransactionID:         "abc123efg",
			Amount:                -13,
			Currency:              locale.Currency,
			AccountID:             "**********",
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "MOOMOO",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
	}
}

// OpsSearchGetAllTransactionsBackwardScrollingMockDBResponse ...
//
//nolint:dupl
func OpsSearchGetAllTransactionsBackwardScrollingMockDBResponse() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchGetAllTransactionsBackwardScrollingMockDBOneResponse ...
// nolint: dupl,funlen
func OpsSearchGetAllTransactionsBackwardScrollingMockDBOneResponse() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchGetAllTransactionsPrevNextExistMockDBResponse ...
func OpsSearchGetAllTransactionsPrevNextExistMockDBResponse() []*storage.TransactionsData {
	rows := OpsSearchTransactionsDataMockDBRows()
	return rows[1:]
}

// OpsSearchPocketTransactions ...
// nolint: dupl, funlen
func OpsSearchPocketTransactions() []*storage.TransactionsData {
	locale := utils.GetLocale()
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "MAIN_ACCOUNT", "source_account_id": "**********", "destination_display_name": "Paris1", "destination_account_id": "**********000"})
	batchDetails2, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "Paris1", "source_account_id": "**********000", "destination_display_name": "MAIN_ACCOUNT", "destination_account_id": "**********"})
	return []*storage.TransactionsData{
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "215.2",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		}, {
			ID:                          5,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "test-client-batch-id4",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "1",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails2,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// OpsSearchCounterPartyTransactionEntryForPocketTransactions will be just the counterparty transaction PocketTransactions method
func OpsSearchCounterPartyTransactionEntryForPocketTransactions() []*storage.TransactionsData {
	pocketTransfer := OpsSearchPocketTransactions()
	return pocketTransfer // []*storage.TransactionsData{pocketTransfer[0], pocketTransfer[1]}
}

// OpsSearchPaymentDetailData ...
// nolint: funlen
func OpsSearchPaymentDetailData() map[storage.PaymentDataKey]*storage.PaymentDetail {
	locale := utils.GetLocale()
	account, _ := json.Marshal(dto.AccountDetail{Number: "**********", DisplayName: "User No1", SwiftCode: ""})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "User No2", SwiftCode: ""})
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123", "remarks": ""})
	paymentData := map[storage.PaymentDataKey]*storage.PaymentDetail{
		{PaymentBatchID: "abc123efg", AccountID: "**********"}: {
			ID:                    3,
			TransactionID:         "abc123efg",
			Amount:                -20,
			Currency:              locale.Currency,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
		{PaymentBatchID: "efg123abd", AccountID: "**********"}: {
			ID:                    2,
			TransactionID:         "efg123abd",
			Currency:              locale.Currency,
			Amount:                -20,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{PaymentBatchID: "efg1111abd", AccountID: "**********"}: {
			ID:                    1,
			TransactionID:         "efg1111abd",
			Currency:              locale.Currency,
			Amount:                20,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{PaymentBatchID: "abcd12345", AccountID: "**********"}: {
			ID:                    6,
			TransactionID:         "abcd12345",
			Currency:              locale.Currency,
			Amount:                -13,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{PaymentBatchID: "qwer12345", AccountID: "**********"}: {
			ID:                    7,
			TransactionID:         "qwer12345",
			Currency:              locale.Currency,
			Amount:                -490,
			TransactionType:       "PAYMENT",
			TransactionSubType:    "RPP_NETWORK",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   json.RawMessage("{\"number\": \"54321\", \"displayName\": \"SHOPEE MALAYSIA\"}"),
			Metadata:              json.RawMessage("{\"external_id\": \"abc123\", \"serviceType\": \"QR_PAYMENT\"}"),
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{PaymentBatchID: "qwer789", AccountID: "**********"}: {
			ID:                    8,
			TransactionID:         "qwer789",
			Currency:              locale.Currency,
			Amount:                -800,
			TransactionType:       "SEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              json.RawMessage("{\"external_id\": \"efg123\", \"serviceType\": \"QR_TRANSFER\"}"),
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{PaymentBatchID: "lending-txn-1", AccountID: "**********"}: {
			ID:                    9,
			TransactionID:         "lending-txn-1",
			Currency:              locale.Currency,
			Amount:                -13,
			TransactionType:       constants.RepaymentTransactionType,
			TransactionSubType:    constants.IntrabankTransactionSubtype,
			AccountID:             "**********",
			CounterPartyAccountID: "************",
			CounterPartyAccount:   []byte(`{"Number"": "************""}`),
			Metadata:              json.RawMessage("{\"external_id\": \"\", \"serviceType\": \"\"}"),
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{PaymentBatchID: "lending-txn-1", AccountID: "************"}: {
			ID:                    10,
			TransactionID:         "lending-txn-1",
			Currency:              locale.Currency,
			Amount:                13,
			TransactionType:       constants.RepaymentTransactionType,
			TransactionSubType:    constants.IntrabankTransactionSubtype,
			AccountID:             "************",
			CounterPartyAccountID: "**********",
			CounterPartyAccount:   []byte(`{"Number"": "**********""}`),
			Metadata:              json.RawMessage("{\"external_id\": \"\", \"serviceType\": \"\"}"),
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{PaymentBatchID: "biz-txn-1", AccountID: "**********"}: {
			ID:                    3,
			TransactionID:         "biz-txn-1",
			Amount:                -20,
			Currency:              locale.Currency,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
	return paymentData
}

// OpsSearchPaymentDetailGrabData ...
// nolint: funlen
func OpsSearchPaymentDetailGrabData() map[storage.PaymentDataKey]*storage.PaymentDetail {
	locale := utils.GetLocale()
	account, _ := json.Marshal(dto.AccountDetail{Number: "**********", DisplayName: "User No1", SwiftCode: ""})
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "User No2", SwiftCode: ""})
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123", "remarks": ""})
	metadataGrab, _ := json.Marshal(map[string]string{"external_id": "xyz123", "grab_activity_type": "FOOD", "remarks": "some remarks"})
	paymentData := map[storage.PaymentDataKey]*storage.PaymentDetail{
		{PaymentBatchID: "abc123efg", AccountID: "**********"}: {
			ID:                    3,
			TransactionID:         "abc123efg",
			Amount:                -20,
			Currency:              locale.Currency,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
		{PaymentBatchID: "efg123abd", AccountID: "**********"}: {
			ID:                    2,
			TransactionID:         "efg123abd",
			Currency:              locale.Currency,
			Amount:                -20,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "GRAB",
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadataGrab,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{PaymentBatchID: "efg1111abd", AccountID: "**********"}: {
			ID:                    1,
			TransactionID:         "efg1111abd",
			Currency:              locale.Currency,
			Amount:                20,
			AccountID:             "**********",
			Account:               account,
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{PaymentBatchID: "abcd12345", AccountID: "**********"}: {
			ID:                    6,
			TransactionID:         "abcd12345",
			Currency:              locale.Currency,
			Amount:                -13,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "RPP_NETWORK",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
	}
	return paymentData
}

// OpsSearchCardDetailData ...
func OpsSearchCardDetailData() map[storage.CardDataKey]*storage.CardTransactionDetail {
	locale := utils.GetLocale()
	account, _ := json.Marshal(dto.AccountDetail{Number: "**********"})
	metadata, _ := json.Marshal(map[string]string{"networkID": "MCN"})
	cardData := map[storage.CardDataKey]*storage.CardTransactionDetail{
		{CardTxnID: "abc123efg", AccountID: "**********"}: {
			ID:                  3,
			CardTransactionID:   "abc123efg",
			Amount:              20,
			Currency:            locale.Currency,
			AccountID:           "**********",
			Account:             account,
			MerchantDescription: "MERCHANT1",
			Metadata:            metadata,
			Status:              "COMPLETED",
			CreationTimestamp:   time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
		{CardTxnID: "efg123abd", AccountID: "**********"}: {
			ID:                  2,
			CardTransactionID:   "efg123abd",
			TransactionType:     constants.SpendCardPresentTransactionType,
			TransactionSubType:  constants.PaynetMydebitTransactionSubtype,
			TransferType:        constants.TransferTypeCharge,
			Currency:            locale.Currency,
			Amount:              513,
			CaptureAmount:       513,
			AccountID:           "**********",
			Account:             account,
			MerchantDescription: "MERCHANT2",
			Metadata:            metadata,
			Status:              "COMPLETED",
			CreationTimestamp:   time.Unix(**********, 0).UTC(),
		},
		{CardTxnID: "efg1111abd", AccountID: "**********"}: {
			ID:                  1,
			CardTransactionID:   "efg1111abd",
			Currency:            locale.Currency,
			Amount:              20,
			AccountID:           "**********",
			Account:             account,
			MerchantDescription: "MERCHANT3",
			Metadata:            metadata,
			Status:              "COMPLETED",
			CreationTimestamp:   time.Unix(**********, 0).UTC(),
		},
		{CardTxnID: "abcd12345", AccountID: "**********"}: {
			ID:                  6,
			CardTransactionID:   "abcd12345",
			Currency:            locale.Currency,
			Amount:              -13,
			TransactionType:     "SPEND_MONEY",
			TransactionSubType:  "RPP_NETWORK",
			AccountID:           "**********",
			MerchantDescription: "MERCHANT4",
			Metadata:            metadata,
			Status:              "COMPLETED",
			CreationTimestamp:   time.Unix(**********, 0).UTC(),
		},
	}
	return cardData
}

// OpsSearchLoanDetailData ...
func OpsSearchLoanDetailData() map[storage.LoanDataKey]*storage.LoanDetail {
	locale := utils.GetLocale()
	loanData := map[storage.LoanDataKey]*storage.LoanDetail{
		{LoanPaymentTrxID: "lending-txn-1"}: {
			ID:                   1,
			LoanTransactionID:    "loan-1",
			Amount:               1300,
			Currency:             locale.Currency,
			AccountID:            "************",
			AccountDetail:        nil,
			PaymentTransactionID: "lending-txn-1",
			TransactionDomain:    constants.LendingDomain,
			TransactionType:      constants.RepaymentTransactionType,
			TransactionSubType:   constants.IntrabankTransactionSubtype,
			Status:               "COMPLETED",
			StatusDetail:         nil,
			DisbursementDetail:   nil,
			RepaymentDetail:      []byte(`{"Currency": "MYR", "LoanRepaymentDetail": [{"LoanName": "Loan 1", "AccountID": "************"}, {"LoanName": "Loan 2", "AccountID": "************"}]}`),
			CreatedAt:            time.Time{},
			UpdatedAt:            time.Time{},
		},
	}
	return loanData
}

// OpsSearchTransactionsDataWithRewardsMockDBRows ...
// nolint:dupl, funlen
func OpsSearchTransactionsDataWithRewardsMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "REWARDS_CASHBACK",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "4.90",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          json.RawMessage("{\"campaignName\": \"Grab Unlimited Cashback\", \"campaignDescription\":\"Grab Unlimited Rewards\"}"),
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEBIT_CARD",
		TransactionType:             "REWARDS_CASHBACK",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "8.00",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          json.RawMessage("{\"campaignName\": \"Grab Unlimited Cashback\", \"campaignDescription\":\"Grab Unlimited Rewards\"}"),
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchTransactionsDataWithQrPaymentMockDBRows ...
// nolint:dupl, funlen
func OpsSearchTransactionsDataWithQrPaymentMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "qwer12345",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "PAYMENT",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "4.90",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          json.RawMessage("{\"external_id\": \"abc123\"}"),
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "qwer789",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "8.00",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          json.RawMessage("{\"external_id\": \"efg123\"}"),
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchTransactionsDataWithLendingMockDBRows ...
// nolint:dupl, funlen
func OpsSearchTransactionsDataWithLendingMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "lending-txn-1",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           constants.LendingDomain,
		TransactionType:             constants.RepaymentTransactionType,
		TransactionSubtype:          constants.IntrabankTransactionSubtype,
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "qwer12345",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "PAYMENT",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "4.90",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          json.RawMessage("{\"external_id\": \"abc123\"}"),
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "qwer789",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "8.00",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          json.RawMessage("{\"external_id\": \"efg123\"}"),
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchTransactionsDataWithBizLendingMockDBRows ...
// nolint:dupl
func OpsSearchTransactionsDataWithBizLendingMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "lending-txn-1",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           constants.BizLendingDomain,
		TransactionType:             constants.RepaymentTransactionType,
		TransactionSubtype:          constants.IntrabankTransactionSubtype,
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "qwer12345",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "PAYMENT",
		TransactionSubtype:          "RPP_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "4.90",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          json.RawMessage("{\"external_id\": \"abc123\"}"),
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchTransactionsDataWithBizMockDBRows ...
func OpsSearchTransactionsDataWithBizMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "biz-txn-1",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           constants.BizDepositsDomain,
		TransactionType:             constants.TransferMoneyTxType,
		TransactionSubtype:          constants.IntrabankTransactionSubtype,
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// OpsSearchTransactionsDataWithLendingByBatchIDMockDBRows ...
// nolint:dupl, funlen
func OpsSearchTransactionsDataWithLendingByBatchIDMockDBRows() []*storage.TransactionsData {
	locale := utils.GetLocale()
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "lending-txn-1",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           constants.LendingDomain,
		TransactionType:             constants.RepaymentTransactionType,
		TransactionSubtype:          constants.IntrabankTransactionSubtype,
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {ID: 1,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "lending-txn-1",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           constants.LendingDomain,
		TransactionType:             constants.RepaymentTransactionType,
		TransactionSubtype:          constants.IntrabankTransactionSubtype,
		AccountID:                   "************",
		AccountAddress:              "REPAYMENT_MADE",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         locale.Currency,
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}}
}

// GetCustomerTxnRowsTxnDataForLending ...
// nolint
func GetCustomerTxnRowsTxnDataForLending() []*storage.TransactionsData {
	locale := utils.GetLocale()
	return []*storage.TransactionsData{
		{
			ID:                          1,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdagffff",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           constants.LendingDomain,
			TransactionType:             constants.DrawdownTransactionType,
			TransactionSubtype:          constants.IntrabankTransactionSubtype,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           constants.LendingDomain,
			TransactionType:             constants.RepaymentTransactionType,
			TransactionSubtype:          constants.IntrabankTransactionSubtype,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdagffff",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           constants.LendingDomain,
			TransactionType:             constants.DrawdownTransactionType,
			TransactionSubtype:          constants.IntrabankTransactionSubtype,
			AccountID:                   "************",
			AccountAddress:              "DISBURSEMENT_CONTRA",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          4,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edd",
			ClientBatchID:               "abc123efasdffdag",
			TmPostingInstructionBatchID: "40cb2d25-aecdd-4741-b7c0-b5f105dcf",
			TmTransactionID:             "58e3ac0a-f8d88-44e6-a1e1-ed36532bdb82dsd",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           constants.LendingDomain,
			TransactionType:             constants.RepaymentTransactionType,
			TransactionSubtype:          constants.IntrabankTransactionSubtype,
			AccountID:                   "************",
			AccountAddress:              "REPAYMENT_MADE",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         locale.Currency,
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Time{},
			BatchValueTimestamp:         time.Time{},
			TmTransactionType:           "SETTLEMENT",
		},
	}
}

package resources //nolint:dupl

import (
	"encoding/json"
	"time"

	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"

	"gitlab.myteksi.net/dakota/transaction-history/dto"

	"gitlab.myteksi.net/dakota/transaction-history/constants"

	"gitlab.myteksi.net/dakota/transaction-history/storage"
)

// UnknownActivityType ...
const UnknownActivityType = "UNKNOWN"

// GetAllTransactionsForCxFirstPageMockDBResponse ...
func GetAllTransactionsForCxFirstPageMockDBResponse() []*storage.TransactionsData {
	rows := CxTransactionsDataMockDBRows()
	return rows
}

// GetAllTransactionsForCxLastPageMockDBResponse ...
func GetAllTransactionsForCxLastPageMockDBResponse() []*storage.TransactionsData {
	rows := CxTransactionsDataMockDBRows()
	return rows[2:]
}

// CxTransactionsDataMockDBRows ...
// nolint:dupl
func CxTransactionsDataMockDBRows() []*storage.TransactionsData {
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

// CxMooMooTransactionsDataMockDBRows ...
// nolint:dupl
func CxMooMooTransactionsDataMockDBRows() []*storage.TransactionsData {
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "CASH_OUT",
		TransactionSubtype:          "MOOMOO",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

// LendingTransactionsDataMockDBRows
// nolint : dupl
func LendingTransactionsDataMockDBRows() []*storage.TransactionsData {
	return []*storage.TransactionsData{
		{ID: 3,
			ClientTransactionID:         "e576bdd97c664832ad318992c8016f78",
			ClientBatchID:               "d98982e4164643709d7415cf9a97b61f",
			TmPostingInstructionBatchID: "c162f7db-e940-4673-84ca-134f8ba3b878",
			TmTransactionID:             "362b541b-c6bf-495b-b0b5-adaaca21da72",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "LENDING",
			TransactionType:             "DRAWDOWN",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "1000",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "1000.21",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "TRANSFER",
		}, {ID: 2,
			ClientTransactionID:         "e0524f07c8494a0ab2caf39c97e6af4d",
			ClientBatchID:               "73db0b995d254e02b86821db360d32bf",
			TmPostingInstructionBatchID: "c4f8fdc1-8532-4999-932e-3d28cdac33ff",
			TmTransactionID:             "ef050f1d-b54e-4b0a-b79d-008ecdeb4922",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "LENDING",
			TransactionType:             "REPAYMENT",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "500",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "750.21",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "TRANSFER",
		}, {ID: 1,
			ClientTransactionID:         "4da71218-383d-44c1-83df-c799e1d1fd4d",
			ClientBatchID:               "a984a3e7-ae4b-48bc-82a7-a296c66ae604",
			TmPostingInstructionBatchID: "a196de66-1ba0-4cf4-906c-9a8fb5d10dcd",
			TmTransactionID:             "a2874194-085a-4be1-9c95-75230375983d",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "LENDING",
			TransactionType:             "INTEREST_APPLICATION",
			TransactionSubtype:          "PENAL_INTEREST",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.03",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "750.18",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "TRANSFER",
		},
	}
}

// PaymentDetailForCxMockDBRows ...
// nolint: funlen
func PaymentDetailForCxMockDBRows() []*storage.PaymentDetail {
	counterPartyAccountDetail, _ := json.Marshal(dto.AccountDetail{Number: "54321", DisplayName: "UserNo2", Proxy: dto.ProxyObject{IsQR: true}})
	metadata, _ := json.Marshal(map[string]interface{}{"grab_activity_type": "TRANSPORT", "is_ops_transaction": false})
	metadataUnkownActivityType, _ := json.Marshal(map[string]interface{}{"grab_activity_type": UnknownActivityType, "is_ops_transaction": false})
	metadataRefund, _ := json.Marshal(map[string]interface{}{"transferType": "REFUND", "grab_activity_id": "123", "grab_activity_type": "TRANSPORT", "is_ops_transaction": false})
	dashMetadata, _ := json.Marshal(map[string]interface{}{"dash_activity_type": "TOPUP", "is_ops_transaction": false})
	dashMetadata1, _ := json.Marshal(map[string]interface{}{"dash_activity_type": "QR", "is_ops_transaction": false})
	dashMetadata2, _ := json.Marshal(map[string]interface{}{"dash_activity_type": "DEFAULT", "is_ops_transaction": false})
	dashMetadata3, _ := json.Marshal(map[string]interface{}{"dash_activity_type": UnknownActivityType, "is_ops_transaction": false})
	return []*storage.PaymentDetail{ // only fields required in processing are mentioned
		{
			ID:                    3,
			TransactionID:         "abc123efg",
			Amount:                -20,
			Currency:              "SGD",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(), // to ensure it belong to request interval
		},
		{
			ID:                    2,
			TransactionID:         "efg123abd",
			Currency:              "SGD",
			Amount:                -20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    1,
			TransactionID:         "efg1111abd",
			Currency:              "SGD",
			Amount:                20,
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    6,
			TransactionID:         "abcd12345",
			Currency:              "SGD",
			Amount:                -13,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "GRAB",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
			ValueTimestamp:        time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    7,
			TransactionID:         "abcd23456",
			Currency:              "SGD",
			Amount:                -13,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "DASH",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              dashMetadata1,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
			ValueTimestamp:        time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    8,
			TransactionID:         "abcd34567",
			Currency:              "SGD",
			Amount:                13,
			TransactionType:       "RECEIVE_MONEY",
			TransactionSubType:    "DASH",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              dashMetadata,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0),
		},
		{
			ID:                    9,
			TransactionID:         "pqr123",
			Currency:              "SGD",
			Amount:                -1,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "GRAB",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadata,
			Status:                "CANCELED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
			ValueTimestamp:        time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    10,
			TransactionID:         "cbid1",
			Currency:              "SGD",
			Amount:                1,
			TransactionType:       "SPEND_MONEY_REVERSAL",
			TransactionSubType:    "GRAB",
			AccountID:             "**********",
			CounterPartyAccountID: "*********",
			Metadata:              metadataRefund,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
			ValueTimestamp:        time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    11,
			TransactionID:         "pqrst123",
			Currency:              "SGD",
			Amount:                1,
			TransactionType:       "RECEIVE_MONEY",
			TransactionSubType:    "DASH",
			AccountID:             "**********",
			CounterPartyAccountID: "*********",
			Metadata:              dashMetadata2,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
			ValueTimestamp:        time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    12,
			TransactionID:         "cbid1",
			Currency:              "SGD",
			Amount:                10,
			TransactionType:       "DRAWDOWN",
			TransactionSubType:    "FAST_NETWORK",
			AccountID:             "**********",
			CounterPartyAccountID: "flexi_loan_account",
			Metadata:              nil,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    13,
			TransactionID:         "cbid2",
			Currency:              "SGD",
			Amount:                -10,
			TransactionType:       "REPAYMENT",
			TransactionSubType:    "FAST_NETWORK",
			AccountID:             "**********",
			CounterPartyAccountID: "flexi_loan_account",
			Metadata:              nil,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    14,
			TransactionID:         "abcd54321",
			Currency:              "SGD",
			Amount:                -13,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "GRAB",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              metadataUnkownActivityType,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
			ValueTimestamp:        time.Unix(**********, 0).UTC(),
		},
		{
			ID:                    15,
			TransactionID:         "abcd23457",
			Currency:              "SGD",
			Amount:                -13,
			TransactionType:       "SPEND_MONEY",
			TransactionSubType:    "DASH",
			AccountID:             "**********",
			CounterPartyAccountID: "54321",
			CounterPartyAccount:   counterPartyAccountDetail,
			Metadata:              dashMetadata3,
			Status:                "COMPLETED",
			CreationTimestamp:     time.Unix(**********, 0).UTC(),
			ValueTimestamp:        time.Unix(**********, 0).UTC(),
		},
	}
}

// GetAllTransactionsBackwardScrollingForCxMockDBResponse ...
//
//nolint:dupl
func GetAllTransactionsBackwardScrollingForCxMockDBResponse() []*storage.TransactionsData {
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

// GetAllTransactionsPrevNextExistForCxMockDBResponse ...
func GetAllTransactionsPrevNextExistForCxMockDBResponse() []*storage.TransactionsData {
	rows := CxTransactionsDataMockDBRows()
	return rows[1:]
}

// PocketTransactionsForCx ...
// nolint: funlen, dupl
func PocketTransactionsForCx() []*storage.TransactionsData {
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "MAIN_ACCOUNT", "destination_display_name": "Paris1"})
	batchDetails2, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "Paris1", "destination_display_name": "MAIN_ACCOUNT"})
	return []*storage.TransactionsData{
		{
			ID:                          5,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id4",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "1",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails2,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          4,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "test-client-batch-id4",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "**********000",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "1",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "215.2",
			BatchDetails:                batchDetails2,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "test-client-batch-id1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "**********000",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "215.2",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// AllCommittedPostingForCx ...
// nolint:dupl
func AllCommittedPostingForCx() []*storage.TransactionsData {
	return []*storage.TransactionsData{
		{
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "abc123efg",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          "INTRABANK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.13",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "210",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "efg123abd",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "215.2",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		}, {ID: 1,
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "efg1111abd",
			TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
			TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "RECEIVE_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "15.12",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "250.21",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		}}
}

// AllReleasePostingForCx ...
// nolint:dupl
func AllReleasePostingForCx() []*storage.TransactionsData {
	return []*storage.TransactionsData{{
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY_REVERSAL",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "SGD",
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
	}, {
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "abc123efg",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY_REVERSAL",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_INGOING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "SGD",
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "SEND_MONEY_REVERSAL",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "SGD",
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
	}}
}

// AuthAndSettlementTxns ...
// nolint:dupl, funlen
func AuthAndSettlementTxns() []*storage.TransactionsData {
	return []*storage.TransactionsData{
		{
			ID:                          6,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "CB1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "4",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "215.2",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          5,
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "CB2",
			TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
			TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "15.12",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "250.21",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          4,
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "CB3",
			TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
			TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "15.12",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "250.21",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "SETTLEMENT",
		},
		{
			ID:                          3,
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "CB3",
			TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
			TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "debit",
			TransactionAmount:           "15.12",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "250.21",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "OUTBOUND_AUTHORISATION",
		},
		{
			ID:                          2,
			ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "CB2",
			TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
			TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "debit",
			TransactionAmount:           "15.12",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "250.21",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "OUTBOUND_AUTHORISATION",
		},
		{
			ID:                          1,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "CB1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SEND_MONEY",
			TransactionSubtype:          "FAST_NETWORK",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
			DebitOrCredit:               "debit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "215.2",
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
			TmTransactionType:           "OUTBOUND_AUTHORISATION",
		},
	}
}

// IncomingSettlementTxns ...
// nolint:dupl, funlen
func IncomingSettlementTxns() []*storage.TransactionsData {
	return []*storage.TransactionsData{{
		ID:                          3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "CB1",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "SGD",
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {
		ID:                          2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "CB1",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_INCOMING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "4",
		TransactionCurrency:         "SGD",
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "SETTLEMENT",
	}, {
		ID:                          1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "CB2",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RECEIVE_MONEY",
		TransactionSubtype:          "FAST_NETWORK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_INCOMING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "SGD",
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "INBOUND_AUTHORISATION",
	},
	}
}

// GetAccountImageDetailsSampleResponseV2  returns sample for accountImageDetail
func GetAccountImageDetailsSampleResponseV2() *accountService.GetAccountDetailsResponse {
	return &accountService.GetAccountDetailsResponse{
		AccountImageDetails: map[string]accountService.Image{
			"**********000": {ID: "001", URL: "https://assets.sgbank.dev/**********000"},
			"**********001": {ID: "002", URL: "https://assets.sgbank.dev/**********001"},
		},
		AccountNameDetails: map[string]string{
			"**********000": "Paris",
			"**********001": "Iphone",
		},
	}
}

// MultiplePocketTransactions ...
// nolint: funlen, dupl
func MultiplePocketTransactions() []*storage.TransactionsData {
	batchDetails, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "MAIN_ACCOUNT", "destination_display_name": "Paris"})
	batchDetails1, _ := json.Marshal(map[string]string{"service_id": "deposits-exp", "batch_remarks": "", "source_display_name": "Iphone", "destination_display_name": "MAIN_ACCOUNT"})
	return []*storage.TransactionsData{
		{
			ID:                          7,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id6",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "5",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          6,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "test-client-batch-id6",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "**********000",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "5",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "215.2",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          5,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id4",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "1",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails1,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          4,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "test-client-batch-id4",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketWithdrawalTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "**********001",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "1",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "215.2",
			BatchDetails:                batchDetails1,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          3,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ef",
			ClientBatchID:               "test-client-batch-id1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "210",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
		{
			ID:                          2,
			ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
			ClientBatchID:               "test-client-batch-id1",
			TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
			TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
			BatchStatus:                 constants.BatchStatusAccepted,
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "TRANSFER_MONEY",
			TransactionSubtype:          constants.PocketFundingTransactionSubType,
			AccountID:                   "**********",
			AccountAddress:              "**********000",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "5.13",
			TransactionCurrency:         "SGD",
			BalanceAfterTransaction:     "215.2",
			BatchDetails:                batchDetails,
			BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
			BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		},
	}
}

// GrabTransactionData ...
func GrabTransactionData() []*storage.TransactionsData {
	return []*storage.TransactionsData{
		{ID: 1,
			ClientTransactionID:         "ctid1",
			ClientBatchID:               "cbid1",
			TmPostingInstructionBatchID: "0acb2d25-aecd-4741-b7c0-b5f138105dee",
			TmTransactionID:             "85e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SPEND_MONEY_REVERSAL",
			TransactionSubtype:          "GRAB",
			AccountID:                   "*********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "debit",
			TransactionAmount:           "0.1",
			TransactionCurrency:         "SGD",
			BatchInsertionTimestamp:     time.Unix(**********, 0),
			BatchValueTimestamp:         time.Unix(**********, 0),
			TmTransactionType:           "TRANSFER",
		},
		{ID: 1,
			ClientTransactionID:         "ctid1",
			ClientBatchID:               "cbid1",
			TmPostingInstructionBatchID: "0acb2d25-aecd-4741-b7c0-b5f138105dee",
			TmTransactionID:             "85e3ac0a-f888-44e6-a1e1-ed36532bdb82",
			BatchStatus:                 "ACCEPTED",
			TransactionDomain:           "DEPOSITS",
			TransactionType:             "SPEND_MONEY_REVERSAL",
			TransactionSubtype:          "GRAB",
			AccountID:                   "**********",
			AccountAddress:              "DEFAULT",
			AccountPhase:                "POSTING_PHASE_COMMITTED",
			DebitOrCredit:               "credit",
			TransactionAmount:           "0.1",
			TransactionCurrency:         "SGD",
			BatchInsertionTimestamp:     time.Unix(**********, 0),
			BatchValueTimestamp:         time.Unix(**********, 0),
			TmTransactionType:           "TRANSFER",
		},
	}
}

// CxSearchTransactionsCardsDataMockDBRows ...
// nolint:dupl, funlen
func CxSearchTransactionsCardsDataMockDBRows() []*storage.TransactionsData {
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           constants.DebitCardDomain,
		TransactionType:             "SPEND_CARD_PRESENT",
		TransactionSubtype:          "PAYNET_MYDEBIT",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

// CxCardTransactionDetailSample ...
// nolint: dupl
func CxCardTransactionDetailSample() *storage.CardTransactionDetail {
	return &storage.CardTransactionDetail{
		ID:                            0,
		CardTransactionID:             "efg123abd",
		CardID:                        "d35a0a5c-6426-43c2-9a7e-e5e299f0d3d7",
		TransactionDomain:             "TransactionDomain",
		TransactionType:               "SPEND_CARD_ATM",
		TransactionSubType:            "MASTERCARD",
		Amount:                        513,
		Currency:                      "MYR",
		OriginalAmount:                100,
		CaptureOriginalAmountTillDate: 100,
		OriginalCurrency:              "MYR",
		CaptureAmount:                 0,
		CaptureAmountTillDate:         0,
		AccountID:                     "**********",
		MerchantDescription:           "MerchantDescription",
		Status:                        "COMPLETED",
		StatusDetails:                 nil,
		Metadata:                      nil,
		TailCardNumber:                "9350",
		CreationTimestamp:             time.Unix(**********, 0).UTC(),
		ValueTimestamp:                time.Unix(**********, 0).UTC(),
		CreatedAt:                     time.Unix(**********, 0).UTC(),
		UpdatedAt:                     time.Unix(**********, 0).UTC(),
	}
}

// CxRewardsTransactionsMockDBRows ...
// nolint:dupl, funlen
func CxRewardsTransactionsMockDBRows() []*storage.TransactionsData {
	metadata, _ := json.Marshal(map[string]string{"external_id": "xyz123"})
	return []*storage.TransactionsData{{ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg123abdee",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           constants.DebitCardDomain,
		TransactionType:             "REWARDS_CASHBACK",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "MYR",
		TransactionDetails:          json.RawMessage("{\"campaignName\": \"Grab Unlimited Cashback\", \"campaignDescription\":\"Grab Unlimited Rewards\"}"),
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 2,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "debit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "MYR",
		TransactionDetails:          metadata,
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 1,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg1234abdee",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "REWARDS_CASHBACK",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "4.90",
		TransactionCurrency:         "MYR",
		TransactionDetails:          json.RawMessage("{\"campaignName\": \"Grab Unlimited Cashback\", \"campaignDescription\":\"Grab Unlimited Rewards\"}"),
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}}
}

// CxEarmarkTransactionsMockDBRows ...
// nolint:dupl, funlen
func CxEarmarkTransactionsMockDBRows() []*storage.TransactionsData {
	return []*storage.TransactionsData{{ID: 4,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edf",
		ClientBatchID:               "efg123abde",
		TmPostingInstructionBatchID: "10cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "38e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "RELEASE_EARMARK",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "credit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "RELEASE",
	}, {ID: 3,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599edf",
		ClientBatchID:               "efg123abde",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "58e3ac0a-f888-44e6-a1e1-ed36532bdb82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "APPLY_EARMARK",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "0.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "210",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	}, {ID: 2,
		ClientTransactionID:         "9007e9c10ea841c593bee2e65df599ed",
		ClientBatchID:               "efg1111abd",
		TmPostingInstructionBatchID: "40cb2d25-aecd-4741-b7c0-b5f138105dcf",
		TmTransactionID:             "48e3ac0a-f888-44e6-a1e1-ed36532bdb12",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "TRANSFER_MONEY",
		TransactionSubtype:          "INTRABANK",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_COMMITTED",
		DebitOrCredit:               "credit",
		TransactionAmount:           "5.13",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "215.2",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "TRANSFER",
	}, {ID: 1,
		ClientTransactionID:         "8007e9c10ea841c593bee2e65df599ef",
		ClientBatchID:               "efg1234abde",
		TmPostingInstructionBatchID: "20cb2d25-aecd-4741-b7c0-b5f138105dca",
		TmTransactionID:             "28e3ac0a-f888-44e6-a1e1-ed36532bab82",
		BatchStatus:                 "POSTING_INSTRUCTION_BATCH_STATUS_ACCEPTED",
		TransactionDomain:           "DEPOSITS",
		TransactionType:             "APPLY_EARMARK",
		TransactionSubtype:          "BANK_INITIATED",
		AccountID:                   "**********",
		AccountAddress:              "DEFAULT",
		AccountPhase:                "POSTING_PHASE_PENDING_OUTGOING",
		DebitOrCredit:               "debit",
		TransactionAmount:           "15.12",
		TransactionCurrency:         "MYR",
		BalanceAfterTransaction:     "250.21",
		BatchInsertionTimestamp:     time.Unix(**********, 0).UTC(),
		BatchValueTimestamp:         time.Unix(**********, 0).UTC(),
		TmTransactionType:           "OUTBOUND_AUTHORISATION",
	}}
}

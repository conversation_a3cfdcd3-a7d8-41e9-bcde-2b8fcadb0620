package storage

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/test/utils"
)

// Test case for CheckAndUpdateCalendarActivity method
func TestCheckAndUpdateCalendarActivity(t *testing.T) {
	t.Run("happy-path-row-not-exist", func(t *testing.T) {
		transaction := &TransactionsData{
			AccountID:           "12345",
			BatchValueTimestamp: time.Now(),
		}
		var mockDBResponse []*AccountCalendarActivity

		mockStorageDAO := &MockIAccountCalendarActivityDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		AccountCalendarActivityD = mockStorageDAO

		_, err := CheckAndUpdateCalendarActivity(context.Background(), transaction)
		assert.NoError(t, err)
	})

	t.Run("happy-path-row-exist", func(t *testing.T) {
		transaction := &TransactionsData{
			AccountID:           "12345",
			BatchValueTimestamp: time.Unix(**********, 0),
		}
		mockDBResponse := []*AccountCalendarActivity{{
			ID: 1, Year: 2021, AccountID: "12345", Months: "01",
		}}

		mockStorageDAO := &MockIAccountCalendarActivityDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		AccountCalendarActivityD = mockStorageDAO

		_, err := CheckAndUpdateCalendarActivity(context.Background(), transaction)
		assert.NoError(t, err)
	})

	t.Run("happy-path-row-exist", func(t *testing.T) {
		transaction := &TransactionsData{
			AccountID:           "12345",
			BatchValueTimestamp: time.Date(2024, time.April, 30, 16, 0, 0, 0, time.UTC), // 2024-04-30 16:00:00 +0000 UTC
		}
		var mockDBResponse []*AccountCalendarActivity

		mockStorageDAO := &MockIAccountCalendarActivityDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		AccountCalendarActivityD = mockStorageDAO

		activity, err := CheckAndUpdateCalendarActivity(context.Background(), transaction)
		assert.NoError(t, err)
		assert.Equal(t, "05", activity.Months)
	})

	t.Run("error-path", func(t *testing.T) {
		transaction := &TransactionsData{
			AccountID:           "12345",
			BatchValueTimestamp: time.Now(),
		}
		var mockDBResponse []*AccountCalendarActivity
		expectedError := errors.New("random error string")

		mockStorageDAO := &MockIAccountCalendarActivityDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(expectedError)
		AccountCalendarActivityD = mockStorageDAO

		_, err := CheckAndUpdateCalendarActivity(context.Background(), transaction)
		assert.Error(t, err, expectedError)
	})
}

func TestBulkUpsertTx(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		var mockDBResponse []*PaymentDetail

		mockStorageDAO := &MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		PaymentDetailD = mockStorageDAO

		transaction := &PaymentDetail{
			AccountID:     "12345",
			TransactionID: uuid.NewString(),
		}
		flag, bulkUpsertErr := BulkUpsertTx(context.Background(), []*PaymentDetail{transaction})
		assert.NoError(t, bulkUpsertErr)
		assert.Equal(t, true, flag)
	})

	t.Run("happy-path-row-exist", func(t *testing.T) {
		txID := uuid.NewString()
		mockDBResponse := []*PaymentDetail{
			{ID: 5, UUID: uuid.NewString(), AccountID: "12345", TransactionID: txID, CreatedAt: time.Now()},
		}

		mockStorageDAO := &MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		PaymentDetailD = mockStorageDAO

		transaction := &PaymentDetail{AccountID: "12345", TransactionID: txID}
		flag, bulkUpsertErr := BulkUpsertTx(context.Background(), []*PaymentDetail{transaction})
		assert.NoError(t, bulkUpsertErr)
		assert.Equal(t, true, flag)
	})

	t.Run("upsert-operation-error-path", func(t *testing.T) {
		var mockDBResponse []*PaymentDetail
		expectedError := errors.New("random error string")

		mockStorageDAO := &MockIPaymentDetailDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(expectedError)
		PaymentDetailD = mockStorageDAO

		transaction := &PaymentDetail{
			AccountID:     "12345",
			TransactionID: uuid.NewString(),
		}
		flag, bulkUpsertErr := BulkUpsertTx(context.Background(), []*PaymentDetail{transaction})
		assert.EqualError(t, bulkUpsertErr, "random error string")
		assert.Equal(t, false, flag)
	})
}

func TestUpdateInterestEarned(t *testing.T) {
	locale := utils.GetLocale()
	accountID := "12345"
	accountAddress := "DEFAULT"
	money := dto.Money{Amount: 200, Currency: locale.Currency}

	t.Run("happy-path-row-exist", func(t *testing.T) {
		mockDBResponse := []*InterestAggregate{{
			AccountID: accountID, AccountAddress: accountAddress, TotalInterestEarned: 20, Currency: locale.Currency,
		}}

		mockStorageDAO := &MockIInterestAggregateDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		InterestAggregateD = mockStorageDAO

		err := UpdateInterestEarned(context.Background(), accountID, accountAddress, money)
		assert.NoError(t, err)
	})

	t.Run("happy-path", func(t *testing.T) {
		var mockDBResponse []*InterestAggregate

		mockStorageDAO := &MockIInterestAggregateDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(nil)
		InterestAggregateD = mockStorageDAO

		err := UpdateInterestEarned(context.Background(), accountID, accountAddress, money)
		assert.NoError(t, err)
	})

	t.Run("error-path", func(t *testing.T) {
		expectedError := errors.New("random error string")
		var mockDBResponse []*InterestAggregate

		mockStorageDAO := &MockIInterestAggregateDAO{}
		mockStorageDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(mockDBResponse, nil)
		mockStorageDAO.On("Upsert", mock.Anything, mock.Anything).Return(expectedError)
		InterestAggregateD = mockStorageDAO

		err := UpdateInterestEarned(context.Background(), accountID, accountAddress, money)
		assert.EqualError(t, err, expectedError.Error())
	})
}

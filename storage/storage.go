package storage

import (
	"context"
	"database/sql"
	"fmt"

	godata "gitlab.myteksi.net/gophers/go/commons/data"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	servusStatsD "gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/server/config"
)

var (
	// PaymentDetailD ...
	PaymentDetailD IPaymentDetailDAO = &PaymentDetailDAO{}
	// AccountCalendarActivityD ...
	AccountCalendarActivityD IAccountCalendarActivityDAO = &AccountCalendarActivityDAO{}
	//TransactionsDataD ...
	TransactionsDataD ITransactionsDataDAO = &TransactionsDataDAO{}
	//InterestAggregateD ...
	InterestAggregateD IInterestAggregateDAO = &InterestAggregateDAO{}
	// InterestAggregateV2D ...
	InterestAggregateV2D IInterestAggregateV2DAO = &InterestAggregateV2DAO{}
	// CardTransactionDetailD ...
	CardTransactionDetailD ICardTransactionDetailDAO = &CardTransactionDetailDAO{}
	//LoanDetailD ...
	LoanDetailD ILoanDetailDAO = &LoanDetailDAO{}
	// StatementsDOPSReader ...
	StatementsDOPSReader *sql.DB
	// DB is meant for checking out connection to handle complex sql statement
	DB *sql.DB
)

// Init ...
func Init(conf *config.AppConfig, app *servus.Application, client servusStatsD.Client) {
	PaymentDetailD = NewPaymentDetailDAO(conf.Data, client)
	AccountCalendarActivityD = NewAccountCalendarActivityDAO(conf.Data, client)
	TransactionsDataD = NewTransactionsDataDAO(conf.Data, client)
	InterestAggregateD = NewInterestAggregateDAO(conf.Data, client)
	InterestAggregateV2D = NewInterestAggregateV2DAO(conf.Data, client)
	CardTransactionDetailD = NewCardTransactionDetailDAO(conf.Data, client)
	LoanDetailD = NewLoanDetailDAO(conf.Data, client)

	registerDatabase(app, conf, client)
	dep, err := app.Dependency("dBStore")
	// Initialise db connection pool
	if err != nil {
		app.GetLogger().Error(constants.InitLogTag, "failed to get [dBStore] dependency", slog.Error(err))
		panic(fmt.Errorf("failed to get [dBStore] dependency: %w", err))
	}
	dbStore, ok := dep.(*DBStore)
	if !ok {
		app.GetLogger().Error(constants.InitLogTag, "failed to cast [dBStore] dependency")
		panic(fmt.Errorf("failed to cast [dBStore] dependency"))
	}
	DB, err = dbStore.GetDatabaseHandle(context.Background(), conf.Data.MySQL.Master)
	if err != nil {
		app.GetLogger().Error(constants.InitLogTag, "failed to get database handler", slog.Error(err))
		panic(fmt.Errorf("failed to get database handler: %w", err))
	}
	snowflakeInit(conf)
}

func registerDatabase(app *servus.Application, appCfg *config.AppConfig, client servusStatsD.Client) {
	godata.SetDatetimePrecision(appCfg.GetMySQLDatetimePrecision())
	app.MustRegister("servus.DataConfig", appCfg.Data)
	app.MustRegister("schedulerLockDAO", NewSchedulerLockDAO(appCfg.Data, client))
	app.MustRegister("dBStore", &DBStore{})
}

// snowflakeInit ...
func snowflakeInit(conf *config.AppConfig) {
	if !conf.STM453TransactionInfoFeatureFlag.Enabled {
		return
	}
	db, err := sql.Open("snowflake", conf.SnowflakeConfig.DSNOPSReader)
	if err != nil {
		slog.FromContext(context.Background()).Fatal(constants.SnowflakeTransactionHistoryLogTag, fmt.Sprintf("%v", err))
	} else {
		StatementsDOPSReader = db
		slog.FromContext(context.Background()).Info(constants.SnowflakeTransactionHistoryLogTag, "successful")
	}
}

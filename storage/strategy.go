package storage

import (
	"context"
	"errors"
	"fmt"
	"reflect"

	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/featureflag"
)

type replicaStrategyOpt struct {
	byCallerIdentity string
}

type strategy string

const (
	findStrategy        strategy = "Find"
	findOnSlaveStrategy strategy = "FindOnSlave"
)

type replicaStrategyOptFunc func(opt replicaStrategyOpt) replicaStrategyOpt

// ReplicaByCallerIdentity is an option to be used with FindReplicaConditionally
// nolint: golint
func ReplicaByCallerIdentity(clientID string) replicaStrategyOptFunc {
	return func(opt replicaStrategyOpt) replicaStrategyOpt {
		opt.byCallerIdentity = clientID
		return opt
	}
}

// ShouldReadFromReplica returns true if the feature flag is enabled and the caller identity meets the requirement
func ShouldReadFromReplica(ctx context.Context, opts ...replicaStrategyOptFunc) bool {
	featFlag := featureflag.FeatureFlagsFromContext(ctx)
	ret := true
	ret = ret && featFlag != nil && featFlag.IsReplicaReadEnabled()
	opt := replicaStrategyOpt{}
	for _, optFunc := range opts {
		opt = optFunc(opt)
	}
	// TODO: Can we make this configurable?
	if opt.byCallerIdentity != "" {
		ret = ret && opt.byCallerIdentity == string(servicename.SentryT6)
	}

	return ret
}

// FindReplicaConditionally calls FindOnSlave if the feature flag is enabled and the caller identity meets the requirement
func FindReplicaConditionally(ctx context.Context, dao any, filters []data.Condition, opts ...replicaStrategyOptFunc) (any, error) {
	strat := string(findStrategy)
	useReplica := ShouldReadFromReplica(ctx, opts...)
	if useReplica {
		strat = string(findOnSlaveStrategy)
	}
	find, daoType, err := lookupMethodByName(dao, strat)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info("FindReplicaConditionally", fmt.Sprintf("Using %s query strategy: %s", daoType.Name(), strat))

	funcVals := make([]reflect.Value, 0, len(filters)+1)
	funcVals = append(funcVals, reflect.ValueOf(ctx))
	for i := range filters {
		funcVals = append(funcVals, reflect.ValueOf(filters[i]))
	}
	result := find.Call(funcVals)
	if !result[1].IsNil() {
		return nil, result[1].Interface().(error)
	}
	return result[0].Interface(), nil
}

func lookupMethodByName(dao any, name string) (method reflect.Value, daoType reflect.Type, err error) {
	val := reflect.ValueOf(dao)
	kind := val.Kind()
	if kind != reflect.Pointer {
		return reflect.Value{}, nil, errors.New("DAO is not a pointer")
	}
	typ := val.Type()
	for i := 0; i < val.NumMethod(); i++ {
		method := val.Method(i)
		if typ.Method(i).Name == name {
			if !method.IsValid() {
				return reflect.Value{}, nil, fmt.Errorf("method %s not valid", name)
			}
			return method, typ.Elem(), nil
		}
	}
	return reflect.Value{}, nil, fmt.Errorf("method %s not found on %s", name, typ.Name())
}

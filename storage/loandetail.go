package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
)

// LoanDetail ...
type LoanDetail struct {
	ID                   uint64          `sql-col:"id" sql-key:"id" sql-insert:"false"`
	LoanTransactionID    string          `sql-col:"loan_transaction_id"`
	Amount               int64           `sql-col:"amount"`
	Currency             string          `sql-col:"currency"`
	AccountID            string          `sql-col:"account_id"`
	AccountDetail        json.RawMessage `sql-col:"account_detail" data-type:"json"`
	PaymentTransactionID string          `sql-col:"payment_transaction_id"`
	TransactionDomain    string          `sql-col:"transaction_domain"`
	TransactionType      string          `sql-col:"transaction_type"`
	TransactionSubType   string          `sql-col:"transaction_subtype"`
	Status               string          `sql-col:"status"`
	StatusDetail         json.RawMessage `sql-col:"status_detail" data-type:"json"`
	DisbursementDetail   json.RawMessage `sql-col:"disbursement_detail" data-type:"json"`
	RepaymentDetail      json.RawMessage `sql-col:"repayment_detail" data-type:"json"`
	CreatedAt            time.Time       `sql-col:"created_at"`
	UpdatedAt            time.Time       `sql-col:"updated_at"`
}

// LoanDataKey ...
type LoanDataKey struct {
	LoanPaymentTrxID string
}

// BuildLoanDataKey ...
func BuildLoanDataKey(txnID string) LoanDataKey {
	return LoanDataKey{
		LoanPaymentTrxID: txnID,
	}
}

// GetDisbursementDetail ...
func (d *LoanDetail) GetDisbursementDetail(ctx context.Context) *dto.DrawdownDetailsDTO {
	var disbursementDetail dto.DrawdownDetailsDTO
	err := json.Unmarshal(d.DisbursementDetail, &disbursementDetail)
	if err != nil {
		slog.FromContext(ctx).Warn("loanDetail.GetDisbursementDetail", fmt.Sprintf("Error parsing disbursementDetail, err: %s", err.Error()))
	}
	return &disbursementDetail
}

// GetRepaymentDetail ...
func (d *LoanDetail) GetRepaymentDetail(ctx context.Context) *dto.RepaymentDetailsDTO {
	var repaymentDetail dto.RepaymentDetailsDTO
	err := json.Unmarshal(d.RepaymentDetail, &repaymentDetail)
	if err != nil {
		slog.FromContext(ctx).Warn("loanDetail.GetRepaymentDetail", fmt.Sprintf("Error parsing repaymentDetail, err: %s", err.Error()))
	}
	return &repaymentDetail
}

// GetLoanName return loan name from disbursement/repayment detail
func (d *LoanDetail) GetLoanName(ctx context.Context) string {
	var loanName string
	switch d.TransactionType {
	case constants.DrawdownTransactionType:
		disbursementDetail := d.GetDisbursementDetail(ctx)
		if disbursementDetail != nil {
			loanName = disbursementDetail.LoanName
		}
	case constants.RepaymentTransactionType:
		repaymentDetail := d.GetRepaymentDetail(ctx)
		if repaymentDetail != nil && len(repaymentDetail.LoanRepaymentDetail) > 0 {
			loanName = repaymentDetail.LoanRepaymentDetail[0].LoanName
		}
	}
	return loanName
}

// Package storage ...
package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"sync/atomic"

	"gitlab.myteksi.net/dakota/transaction-history/db/mysql/queries"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/gophers/go/commons/data"
)

// DatabaseStore ...
type DatabaseStore interface {
	GetDatabaseHandle(ctx context.Context, config *data.MysqlConfig) (*sql.DB, error)
	GetTxnDataByExternalIDFromDB(ctx context.Context, externalID string, dbClient *sql.DB) ([]*TransactionsData, error)
}

type (
	// DBStore ...
	DBStore struct {
		SchedulerLockDAO ISchedulerLockDAO `inject:"schedulerLockDAO"`
		Statsd           statsd.Client     `inject:"statsD"`
	}
)

// GetDatabaseHandle ...
func (db *DBStore) GetDatabaseHandle(ctx context.Context, config *data.MysqlConfig) (*sql.DB, error) {
	atomic.AddInt64(&config.PendingCalls, 1)
	defer atomic.AddInt64(&config.PendingCalls, -1)
	dbs, err := getDatabase(ctx, config)
	if err != nil {
		return nil, err
	}
	return dbs, nil
}

// GetTxnDataByExternalIDFromDB ...
// nolint: funlen
func (db *DBStore) GetTxnDataByExternalIDFromDB(ctx context.Context, externalID string, dbClient *sql.DB) ([]*TransactionsData, error) {
	rows, err := dbClient.Query(queries.SelectTxnDataByExternalID, externalID)

	if err != nil {
		if err != sql.ErrNoRows {
			slog.FromContext(ctx).Error(
				constants.GetOpsSearchTransactionDataFromDBLogTag, fmt.Sprintf("Failed to get transactions data from db: %s", err.Error()))
			return nil, err
		}
		return nil, nil
	}
	defer func() {
		err2 := rows.Close()
		if err2 != nil {
			slog.FromContext(ctx).Error(
				constants.GetOpsSearchTransactionDataFromDBLogTag, fmt.Sprintf("Failed to close rows: %s", err.Error()))
		}
	}()
	var txnData []*TransactionsData

	for rows.Next() {
		var rowData TransactionsData
		if err := rows.Scan(
			&rowData.ID,
			&rowData.ClientTransactionID,
			&rowData.ClientBatchID,
			&rowData.TmPostingInstructionBatchID,
			&rowData.TmTransactionID,
			&rowData.BatchRemarks,
			&rowData.BatchStatus,
			&rowData.BatchErrorType,
			&rowData.BatchDetails,
			&rowData.BatchErrorMessage,
			&rowData.TransactionDomain,
			&rowData.TransactionType,
			&rowData.TmTransactionType,
			&rowData.TransactionSubtype,
			&rowData.TransactionDetails,
			&rowData.TransactionViolations,
			&rowData.AccountID,
			&rowData.AccountAddress,
			&rowData.AccountAsset,
			&rowData.AccountPhase,
			&rowData.DebitOrCredit,
			&rowData.TransactionAmount,
			&rowData.TransactionCurrency,
			&rowData.BalanceAfterTransaction,
			&rowData.Metadata,
			&rowData.BatchInsertionTimestamp,
			&rowData.BatchValueTimestamp,
			&rowData.CreatedAt,
			&rowData.UpdatedAt,
		); err != nil {
			slog.FromContext(ctx).Error(
				constants.GetOpsSearchTransactionDataFromDBLogTag, fmt.Sprintf("Failed to get transactions data from db: %s", err.Error()))
			return txnData, err
		}
		txnData = append(txnData, &rowData)
	}

	// Rows.Err will report the last error encountered by Rows.Scan.
	if err := rows.Err(); err != nil {
		slog.FromContext(ctx).Error(
			constants.GetOpsSearchTransactionDataFromDBLogTag, fmt.Sprintf("Failed to  get transactions data from db: %s", err.Error()))
		return nil, err
	}
	return txnData, nil
}

// getDatabase returns a connection the database (either real or mocked) depending on the ENV('Mode') variable.
var getDatabase = func(ctx context.Context, config *data.MysqlConfig) (db *sql.DB, err error) {
	defer panicRecovery(ctx, "commons.data.getDatabase")
	connect := func() {
		db, err = createDB(ctx, config)
	}
	config.ConnectOnce.Do(connect)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.TransactionConnectionLogTag, fmt.Sprintf("CreateDB error: %s", err.Error()))
		return nil, err
	}
	if db != nil {
		return db, nil
	}

	// check for existing connection
	db = getDB(config)
	if db == nil {
		slog.FromContext(ctx).Warn(constants.TransactionConnectionLogTag, fmt.Sprintf("GetDB error: %s", err.Error()))
		return nil, errors.New("failed to connect to DB")
	}
	return db, nil
}

var createDB = func(ctx context.Context, config *data.MysqlConfig) (db *sql.DB, err error) {
	db, err = sql.Open("mysql", config.Dsn)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.TransactionConnectionLogTag, fmt.Sprintf("Failed to open database connection: %s", err.Error()))
		return
	}
	db.SetMaxIdleConns(config.MaxIdle)
	db.SetMaxOpenConns(config.MaxOpen)
	if (config.ConnMaxLifetime.Duration) > 0 {
		db.SetConnMaxLifetime(config.ConnMaxLifetime.Duration)
	}

	// store connection for later reuse
	setDB(config, db)
	return
}

func getDB(config *data.MysqlConfig) *sql.DB {
	return config.DBCache.DB
}

func setDB(config *data.MysqlConfig, db *sql.DB) {
	config.DBCache.DB = db
}

// panicRecovery recovers from a panic happened during specified function
func panicRecovery(ctx context.Context, tag string) {
	if r := recover(); r != nil {
		slog.FromContext(ctx).Info(constants.TransactionConnectionLogTag, fmt.Sprintf("[%s] Recovered from panic. Error: %s", tag, r))
	}
}

package storage

import (
	"context"
	"testing"

	"gitlab.myteksi.net/dakota/transaction-history/dto"

	"gitlab.myteksi.net/dakota/transaction-history/constants"

	"github.com/stretchr/testify/assert"
)

func TestPaymentDetail_GetRemarksFromMetadata(t *testing.T) {
	scenarios := []struct {
		name            string
		paymentDetail   *PaymentDetail
		expectedRemarks string
	}{
		{
			name: "happy path",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"remarks\":      \"ops testing\"}"),
			},
			expectedRemarks: "ops testing",
		},
		{
			name: "empty remarks",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{}"),
			},
			expectedRemarks: "",
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			remarks := scenario.paymentDetail.GetRemarksFromMetadata(context.Background())
			assert.Equal(t, scenario.expectedRemarks, remarks)
		})
	}
}

func TestPaymentDetail_GetOriginalTransactionIDFromMetadata(t *testing.T) {
	scenarios := []struct {
		name                  string
		paymentDetail         *PaymentDetail
		expectedOriginalTxnID string
	}{
		{
			name: "happy path",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"original_transaction_id\":      \"abc123\"}"),
			},
			expectedOriginalTxnID: "abc123",
		},
		{
			name: "empty metadata",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{}"),
			},
			expectedOriginalTxnID: "",
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			originalTxnID := scenario.paymentDetail.GetOriginalTransactionIDFromMetadata(context.Background())
			assert.Equal(t, scenario.expectedOriginalTxnID, originalTxnID)
		})
	}
}

func TestPaymentDetail_GetGrabRecipientReferenceFromActivityType(t *testing.T) {
	scenarios := []struct {
		name                       string
		paymentDetail              *PaymentDetail
		expectedRecipientReference string
	}{
		{
			name: "grab-DEFAULT",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"grab_activity_type\":      \"DEFAULT\"}"),
			},
			expectedRecipientReference: "",
		},
		{
			name: "grab-CANCELLATIONFEE",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"grab_activity_type\":      \"CANCELLATIONFEE\"}"),
			},
			expectedRecipientReference: "Cancellation fee",
		},
		{
			name: "grab-OUTSTANDINGFEE",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"grab_activity_type\":      \"OUTSTANDINGFEE\"}"),
			},
			expectedRecipientReference: "Outstanding fee",
		},
		{
			name: "grab-NOSHOWFEE",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"grab_activity_type\":      \"NOSHOWFEE\"}"),
			},
			expectedRecipientReference: "No-show fee",
		},
		{
			name: "grab-TRANSPORT",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"grab_activity_type\":      \"TRANSPORT\"}"),
			},
			expectedRecipientReference: "",
		},
		{
			name: "grab-EXPRESS",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"grab_activity_type\":      \"EXPRESS\"}"),
			},
			expectedRecipientReference: "",
		},
		{
			name: "grab-FOOD",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"grab_activity_type\":      \"FOOD\"}"),
			},
			expectedRecipientReference: "",
		},
		{
			name: "grab-TOPUP",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"grab_activity_type\":      \"TOPUP\"}"),
			},
			expectedRecipientReference: "",
		},
		{
			name: "grab-TIPPING",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"grab_activity_type\":      \"TIPPING\"}"),
			},
			expectedRecipientReference: "",
		},
		{
			name: "empty metadata",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{}"),
			},
			expectedRecipientReference: "",
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			recipientReference := scenario.paymentDetail.GetGrabRecipientReferenceFromActivityType(context.Background())
			assert.Equal(t, scenario.expectedRecipientReference, recipientReference)
		})
	}
}

func TestPaymentDetail_GetServiceTypeFromMetadata(t *testing.T) {
	scenarios := []struct {
		name                string
		paymentDetail       *PaymentDetail
		expectedServiceType string
	}{
		{
			name: "without service type",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{}"),
			},
			expectedServiceType: "",
		},
		{
			name: "with service type",
			paymentDetail: &PaymentDetail{
				Metadata: []byte("{\"serviceType\":      \"SCHEDULED_TRANSFER\"}"),
			},
			expectedServiceType: "SCHEDULED_TRANSFER",
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			recipientReference := scenario.paymentDetail.GetServiceTypeFromMetadata(context.Background())
			assert.Equal(t, scenario.expectedServiceType, recipientReference)
		})
	}
}

func TestPaymentDetail_GetGrabRefundRecipientReference(t *testing.T) {
	scenarios := []struct {
		name                       string
		paymentDetail              *PaymentDetail
		expectedRecipientReference string
	}{
		{
			name: "grab-REFUND",
			paymentDetail: &PaymentDetail{
				TransactionType: constants.SpendMoneyRevTxType,
				Metadata:        []byte("{\"grab_activity_type\":      \"TRANSPORT\", \"original_transaction_id\":      \"abc123\"}"),
			},
			expectedRecipientReference: "Refund to abc123",
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			recipientReference := scenario.paymentDetail.GetGrabRefundRecipientReference(context.Background())
			assert.Equal(t, scenario.expectedRecipientReference, recipientReference)
		})
	}
}

func TestPaymentDetail_GetCounterPartyAccount(t *testing.T) {
	scenarios := []struct {
		name                        string
		paymentDetail               *PaymentDetail
		expectedCounterPartyAccount *dto.AccountDetail
	}{
		{
			name: "grab-REFUND",
			paymentDetail: &PaymentDetail{
				CounterPartyAccount: []byte("{\"number\":      \"*************\", \"displayName\": \"TESTER1\" }"),
			},
			expectedCounterPartyAccount: &dto.AccountDetail{
				Number:      "*************",
				DisplayName: "TESTER1",
			},
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			counterPartyAccount := scenario.paymentDetail.GetCounterPartyAccount(context.Background())
			assert.Equal(t, scenario.expectedCounterPartyAccount, counterPartyAccount)
		})
	}
}

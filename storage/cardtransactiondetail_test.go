package storage

import (
	"context"
	"testing"

	"gitlab.myteksi.net/dakota/transaction-history/dto"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
)

func TestCardTxnDetail_GetFormattedTxnStatus(t *testing.T) {
	scenarios := []struct {
		name           string
		txnData        *TransactionsData
		cardData       *CardTransactionDetail
		expectedStatus string
	}{
		{
			name: "Card-COMPLETED",
			cardData: &CardTransactionDetail{
				Status: constants.CompletedStatus,
			},
			expectedStatus: "COMPLETED",
		},
		{
			name: "Card-AUTHORISED",
			cardData: &CardTransactionDetail{
				Status: constants.AuthorizedStatus,
			},
			expectedStatus: "PROCESSING",
		},
		{
			name: "Card-CANCELED",
			cardData: &CardTransactionDetail{
				Status: "CANCELED",
			},
			expectedStatus: "CANCELED",
		},
		{
			name: "Card-REFUNDED",
			cardData: &CardTransactionDetail{
				Status:       constants.CompletedStatus,
				TransferType: "REFUND",
			},
			expectedStatus: "COMPLETED",
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			status := scenario.cardData.GetFormattedTxnStatus()
			assert.Equal(t, scenario.expectedStatus, status)
		})
	}
}

func TestCardTxnDetail_GetMetadata(t *testing.T) {
	scenarios := []struct {
		name           string
		cardData       *CardTransactionDetail
		expectedResult *dto.CardDetailMetadata
	}{
		{
			name: "Card-with metadata",
			cardData: &CardTransactionDetail{
				Metadata: []byte(`{"transactionType": "SPEND_CARD_PRESENT", "networkID": "MCC"}`),
			},
			expectedResult: &dto.CardDetailMetadata{
				TransactionType: "SPEND_CARD_PRESENT",
				NetworkID:       "MCC",
			},
		},
		{
			name: "Card-without metadata",
			cardData: &CardTransactionDetail{
				Metadata: nil,
			},
			expectedResult: &dto.CardDetailMetadata{},
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			result := scenario.cardData.GetMetadata(context.Background())
			assert.Equal(t, scenario.expectedResult, result)
		})
	}
}

func TestCardTxnDetail_IsForeignTransaction(t *testing.T) {
	scenarios := []struct {
		name           string
		cardData       *CardTransactionDetail
		expectedResult bool
	}{
		{
			name: "Card-with metadata DCC currencyConvType",
			cardData: &CardTransactionDetail{
				Metadata: []byte(`{"transactionType": "SPEND_CARD_PRESENT", "currencyConvType": "DCC"}`),
			},
			expectedResult: true,
		},
		{
			name: "Card-with metadata FX currencyConvType",
			cardData: &CardTransactionDetail{
				Metadata: []byte(`{"transactionType": "SPEND_CARD_PRESENT", "currencyConvType": "FX"}`),
			},
			expectedResult: true,
		},
		{
			name: "Card-with metadata LOCAL currencyConvType",
			cardData: &CardTransactionDetail{
				Metadata: []byte(`{"transactionType": "SPEND_CARD_PRESENT", "currencyConvType": "LOCAL"}`),
			},
			expectedResult: false,
		},
		{
			name: "Card-with metadata empty currencyConvType",
			cardData: &CardTransactionDetail{
				Metadata: []byte(`{"transactionType": "SPEND_CARD_PRESENT", "currencyConvType": ""}`),
			},
			expectedResult: false,
		},
		{
			name: "Card-without metadata",
			cardData: &CardTransactionDetail{
				Metadata: nil,
			},
			expectedResult: false,
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			result := scenario.cardData.IsForeignTransaction(context.Background())
			assert.Equal(t, scenario.expectedResult, result)
		})
	}
}

func TestCardTxnDetail_HasCurrencyConversion(t *testing.T) {
	scenarios := []struct {
		name           string
		cardData       *CardTransactionDetail
		expectedResult bool
	}{
		{
			name: "Card-with metadata DCC currencyConvType",
			cardData: &CardTransactionDetail{
				Metadata: []byte(`{"transactionType": "SPEND_CARD_PRESENT", "currencyConvType": "DCC"}`),
			},
			expectedResult: false,
		},
		{
			name: "Card-with metadata FX currencyConvType",
			cardData: &CardTransactionDetail{
				Metadata: []byte(`{"transactionType": "SPEND_CARD_PRESENT", "currencyConvType": "FX"}`),
			},
			expectedResult: true,
		},
		{
			name: "Card-with metadata LOCAL currencyConvType",
			cardData: &CardTransactionDetail{
				Metadata: []byte(`{"transactionType": "SPEND_CARD_PRESENT", "currencyConvType": "LOCAL"}`),
			},
			expectedResult: false,
		},
		{
			name: "Card-with metadata empty currencyConvType",
			cardData: &CardTransactionDetail{
				Metadata: []byte(`{"transactionType": "SPEND_CARD_PRESENT", "currencyConvType": ""}`),
			},
			expectedResult: false,
		},
		{
			name: "Card-without metadata",
			cardData: &CardTransactionDetail{
				Metadata: nil,
			},
			expectedResult: false,
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			result := scenario.cardData.HasCurrencyConversion(context.Background())
			assert.Equal(t, scenario.expectedResult, result)
		})
	}
}

func TestCardTransactionDetail_IsCardMaintenanceFee(t *testing.T) {
	scenarios := []struct {
		name           string
		cardData       *CardTransactionDetail
		expectedResult bool
	}{
		{
			name: "Card-with maintenance fee transaction type",
			cardData: &CardTransactionDetail{
				TransactionType: constants.CardAnnualFeeWaiverTransactionType,
			},
			expectedResult: true,
		},
		{
			name: "Card-without maintenance fee transaction type",
			cardData: &CardTransactionDetail{
				TransactionType: "SPEND_CARD_PRESENT",
			},
			expectedResult: false,
		},
	}

	for _, s := range scenarios {
		scenario := s
		t.Run(scenario.name, func(t *testing.T) {
			result := scenario.cardData.IsCardMaintenanceFee()
			assert.Equal(t, scenario.expectedResult, result)
		})
	}
}

package storage

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/transaction-history/constants"
	"gitlab.myteksi.net/dakota/transaction-history/dto"
	"gitlab.myteksi.net/dakota/transaction-history/utils"
)

var (
	// TimeZoneLocation denotes the timezone of Malaysia
	TimeZoneLocation = time.FixedZone("MYT", 8*60*60)
)

// BulkUpsertTx ...
func BulkUpsertTx(ctx context.Context, transactions []*PaymentDetail) (bool, error) {
	for _, transaction := range transactions {
		// Check if the combination is present in DB
		filter := []data.Condition{
			data.EqualTo("AccountID", transaction.AccountID),
			data.EqualTo("TransactionID", transaction.TransactionID),
		}
		matchedTx, err := PaymentDetailD.Find(ctx, filter...)

		if err != nil || len(matchedTx) == 0 {
			transaction.UUID = uuid.NewString() //Adding new UUID
		} else {
			transaction.UUID = matchedTx[0].UUID           //keeping the UUID consistent
			transaction.ID = matchedTx[0].ID               //keeping the ID consistent
			transaction.CreatedAt = matchedTx[0].CreatedAt //keeping the CreatedAt consistent
		}

		upsertErr := PaymentDetailD.Upsert(ctx, transaction)
		if upsertErr != nil {
			slog.FromContext(ctx).Warn(constants.BulkUpsertLogTag, fmt.Sprintf("upsert failed with error: %s", upsertErr.Error()))
			return false, upsertErr
		}
	}
	return true, nil
}

// CheckAndUpdateCalendarActivity insert/update the calendar activity for account
func CheckAndUpdateCalendarActivity(ctx context.Context, transaction *TransactionsData) (*AccountCalendarActivity, error) {
	year, month, _ := transaction.BatchValueTimestamp.In(TimeZoneLocation).Date()
	monthStr := fmt.Sprintf("%d", int(month))
	monthStr = fmt.Sprintf("%02s", monthStr)
	filters := []data.Condition{
		data.EqualTo("AccountID", transaction.AccountID),
		data.EqualTo("Year", year),
	}
	result, err := AccountCalendarActivityD.Find(ctx, filters...)
	var activity *AccountCalendarActivity

	// prepare new if not found
	if err != nil || len(result) == 0 {
		activity = &AccountCalendarActivity{
			AccountID: transaction.AccountID,
			Year:      int64(year),
			Months:    monthStr,
			UpdatedAt: time.Now(),
			CreatedAt: time.Now(),
		}
	} else { // check & update If exist
		activity = result[0]
		monthsList := strings.Split(activity.Months, ",")
		if !utils.SearchStringArray(monthsList, monthStr) {
			slog.FromContext(ctx).Info(constants.CalendarActivityLogTag, fmt.Sprintf("monthsList: %v, monthStr: %s", monthsList, monthStr))
			activity.Months = fmt.Sprintf("%s,%s", monthStr, activity.Months)
			activity.UpdatedAt = time.Now()
		}
	}

	upsertErr := AccountCalendarActivityD.Upsert(ctx, activity)
	if upsertErr != nil {
		slog.FromContext(ctx).Warn(constants.CalendarActivityLogTag, fmt.Sprintf("upsert failed with error: %s", upsertErr.Error()))
		return nil, upsertErr
	}
	return activity, nil
}

// UpdateInterestEarned : insert/update the total interest earned for an account.
func UpdateInterestEarned(ctx context.Context, accountID, accountAddress string, money dto.Money) error {
	var interestAggregateRecord *InterestAggregate

	filters := []data.Condition{
		data.EqualTo("AccountID", accountID),
		data.EqualTo("AccountAddress", accountAddress),
	}
	result, err := InterestAggregateD.Find(ctx, filters...)

	if err != nil || len(result) == 0 {
		interestAggregateRecord = &InterestAggregate{
			AccountID:           accountID,
			AccountAddress:      accountAddress,
			TotalInterestEarned: money.Amount,
			Currency:            money.Currency,
			CreatedAt:           time.Now(),
			UpdatedAt:           time.Now(),
		}
	} else {
		interestAggregateRecord = result[0]
		interestAggregateRecord.TotalInterestEarned = interestAggregateRecord.TotalInterestEarned + money.Amount
		interestAggregateRecord.UpdatedAt = time.Now()
	}
	upsertErr := InterestAggregateD.Upsert(ctx, interestAggregateRecord)
	if upsertErr != nil {
		slog.FromContext(ctx).Warn(constants.InterestAggregateLogTag, fmt.Sprintf("upsert failed with error: %s", upsertErr.Error()))
		return upsertErr
	}
	return nil
}
